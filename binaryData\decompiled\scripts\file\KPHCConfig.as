package file
{
   import mogames.gameData.base.func.KPHCVO;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.base.vo.NeedVO;
   
   public class KPHCConfig
   {
      
      private static var _instance:KPHCConfig;
      
      private var _list:Vector.<KPHCVO>;
      
      public function KPHCConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : KPHCConfig
      {
         if(!_instance)
         {
            _instance = new KPHCConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = new Vector.<KPHCVO>();
         this._list[this._list.length] = new KPHCVO(new NeedVO(68001,5),new BaseRewardVO(18501,1),10000,20);
         this._list[this._list.length] = new KPHCVO(new NeedVO(68002,5),new BaseRewardVO(18502,1),10000,20);
      }
      
      public function findVO(param1:int) : KPHCVO
      {
         var _loc2_:KPHCVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.source.needID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

