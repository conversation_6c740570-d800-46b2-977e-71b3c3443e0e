package com.mogames.data
{
   import com.mogames.utils.MathUtil;
   
   public class RandomVO
   {
      
      private var _min:Oint = new Oint();
      
      private var _max:Oint = new Oint();
      
      public function RandomVO(param1:int, param2:int)
      {
         super();
         MathUtil.saveINT(this._min,param1);
         MathUtil.saveINT(this._max,param2);
      }
      
      public function newMaxValue(param1:Boolean) : int
      {
         if(param1)
         {
            return this.max;
         }
         return this.randomValue;
      }
      
      public function get randomValue() : int
      {
         return MathUtil.randomNum(this.min,this.max);
      }
      
      public function get min() : int
      {
         return MathUtil.loadINT(this._min);
      }
      
      public function get max() : int
      {
         return MathUtil.loadINT(this._max);
      }
   }
}

