package com.mogames.sound
{
   public class SoundManager
   {
      
      private static var _instance:SoundManager;
      
      public static var openAudio:<PERSON>olean = true;
      
      public static var openMusic:Boolean = true;
      
      private var _audios:Vector.<GameSound>;
      
      private var _musics:Vector.<GameSound>;
      
      private var _lastMusic:GameSound;
      
      public function SoundManager()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this._audios = SoundConfig.instance().newAuidos();
         this._musics = SoundConfig.instance().newMusics();
      }
      
      public static function instance() : SoundManager
      {
         if(!_instance)
         {
            _instance = new SoundManager();
         }
         return _instance;
      }
      
      public function playAudio(param1:String, param2:int = 0) : void
      {
         if(!openAudio)
         {
            return;
         }
         var _loc3_:GameSound = this.findAudio(param1);
         if(!_loc3_)
         {
            return;
         }
         _loc3_.play(param2);
      }
      
      public function playNumAudio(param1:String, param2:int = 3) : void
      {
         if(!openAudio || !param1)
         {
            return;
         }
         var _loc3_:GameSound = this.findAudio(param1);
         if(!_loc3_)
         {
            return;
         }
         _loc3_.playCount(param2);
      }
      
      public function stopAudio(param1:String) : void
      {
         if(!param1)
         {
            return;
         }
         var _loc2_:GameSound = this.findAudio(param1);
         _loc2_.stop();
      }
      
      public function playMusic(param1:String) : void
      {
         if(!param1)
         {
            return;
         }
         var _loc2_:GameSound = this.findMusic(param1);
         this._lastMusic = _loc2_;
         if(!openMusic || !_loc2_)
         {
            return;
         }
         this.stopAllMusic();
         _loc2_.play(10000);
      }
      
      public function isPlayMusic(param1:String) : Boolean
      {
         if(!this._lastMusic)
         {
            return false;
         }
         return this._lastMusic.soundVO.name == param1;
      }
      
      public function stopAllAudio() : void
      {
         var _loc1_:GameSound = null;
         for each(_loc1_ in this._audios)
         {
            _loc1_.stop();
         }
      }
      
      public function stopAllMusic() : void
      {
         var _loc1_:GameSound = null;
         for each(_loc1_ in this._musics)
         {
            _loc1_.stop();
         }
      }
      
      public function setMusicMute() : void
      {
         openMusic = !openMusic;
         if(!openMusic)
         {
            this.stopAllMusic();
         }
         else if(this._lastMusic != null)
         {
            this.playMusic(this._lastMusic.soundVO.name);
         }
      }
      
      private function findAudio(param1:String) : GameSound
      {
         var _loc2_:GameSound = null;
         for each(_loc2_ in this._audios)
         {
            if(_loc2_.soundVO.name == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      private function findMusic(param1:String) : GameSound
      {
         var _loc2_:GameSound = null;
         for each(_loc2_ in this._musics)
         {
            if(_loc2_.soundVO.name == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

