package mogames.gameBuff.buff
{
   import mogames.gameBuff.base.TimeRoleBuff;
   import mogames.gameData.buff.ConstBuffVO;
   
   public class YinShenBuff extends TimeRoleBuff
   {
      
      public function YinShenBuff(param1:ConstBuffVO)
      {
         super(param1);
      }
      
      override protected function createData() : void
      {
         _owner.isMDEF = true;
         _owner.isPDEF = true;
         _owner.view.body.alpha = 0.5;
      }
      
      override protected function onCleanRole() : void
      {
         _owner.isMDEF = false;
         _owner.isPDEF = false;
         if(_owner.view.body)
         {
            _owner.view.body.alpha = 1;
         }
      }
   }
}

