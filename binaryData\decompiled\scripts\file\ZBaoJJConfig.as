package file
{
   import mogames.gameData.base.func.ZBaoJJVO;
   import mogames.gameData.base.vo.NeedVO;
   
   public class ZBaoJJConfig
   {
      
      private static var _instance:ZBaoJJConfig;
      
      private var _list:Array;
      
      public var stoneNeed:NeedVO;
      
      public function ZBaoJJConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : ZBaoJJConfig
      {
         if(!_instance)
         {
            _instance = new ZBaoJJConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.stoneNeed = new NeedVO(10982,1);
         this._list = [];
         this._list[this._list.length] = new ZBaoJJVO(30001,30101,80000,400,500,[new NeedVO(10527,10),new NeedVO(10528,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30002,30102,60000,300,200,[new NeedVO(10501,5)]);
         this._list[this._list.length] = new ZBaoJJVO(30003,30103,100000,500,1000,[new NeedVO(10508,10),new NeedVO(10509,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30004,30104,60000,300,200,[new NeedVO(10503,5)]);
         this._list[this._list.length] = new ZBaoJJVO(30005,30105,250000,1000,300,[new NeedVO(10550,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30006,30106,80000,600,200,[new NeedVO(10510,5)]);
         this._list[this._list.length] = new ZBaoJJVO(30007,30107,50000,250,300,[new NeedVO(10521,10),new NeedVO(10522,10),new NeedVO(10534,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30008,30108,50000,250,300,[new NeedVO(10523,10),new NeedVO(10524,10),new NeedVO(10535,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30009,30109,50000,250,300,[new NeedVO(10525,10),new NeedVO(10526,10),new NeedVO(10533,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30010,30110,80000,500,200,[new NeedVO(10505,5)]);
         this._list[this._list.length] = new ZBaoJJVO(30013,30113,500000,2000,200,[new NeedVO(10580,6)]);
         this._list[this._list.length] = new ZBaoJJVO(30011,30111,50000,400,200,[new NeedVO(10536,10),new NeedVO(10537,10),new NeedVO(10538,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30014,30114,50000,250,300,[new NeedVO(10539,5)]);
         this._list[this._list.length] = new ZBaoJJVO(30015,30115,50000,250,300,[new NeedVO(10547,6),new NeedVO(10548,6),new NeedVO(10549,6)]);
         this._list[this._list.length] = new ZBaoJJVO(30016,30116,400000,1000,1000,[new NeedVO(10551,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30017,30117,300000,700,300,[new NeedVO(10559,10),new NeedVO(10560,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30018,30118,500000,1500,300,[new NeedVO(10563,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30019,30119,500000,800,300,[new NeedVO(10553,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30024,30124,500000,500,400,[new NeedVO(10571,6)]);
         this._list[this._list.length] = new ZBaoJJVO(30026,30126,888888,0,1000,[new NeedVO(10000,1)]);
         this._list[this._list.length] = new ZBaoJJVO(30028,30128,888888,666,300,[new NeedVO(10590,20),new NeedVO(10591,20),new NeedVO(10592,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30036,30136,888888,888,200,[new NeedVO(10598,20),new NeedVO(10599,20),new NeedVO(10600,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30136,30236,888888,888,200,[new NeedVO(10598,20),new NeedVO(10599,20),new NeedVO(10600,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30037,30137,888888,888,200,[new NeedVO(10911,20),new NeedVO(10912,20),new NeedVO(10913,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30137,30237,888888,888,200,[new NeedVO(10911,20),new NeedVO(10912,20),new NeedVO(10913,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30102,30202,160000,800,200,[new NeedVO(10545,5)]);
         this._list[this._list.length] = new ZBaoJJVO(30103,30203,350000,1000,1000,[new NeedVO(10552,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30104,30204,160000,800,200,[new NeedVO(10545,5)]);
         this._list[this._list.length] = new ZBaoJJVO(30105,30205,500000,1500,300,[new NeedVO(10550,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30107,30207,100000,350,300,[new NeedVO(10521,10),new NeedVO(10522,10),new NeedVO(10534,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30108,30208,100000,350,300,[new NeedVO(10523,10),new NeedVO(10524,10),new NeedVO(10535,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30109,30209,100000,350,300,[new NeedVO(10525,10),new NeedVO(10526,10),new NeedVO(10533,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30110,30210,160000,800,200,[new NeedVO(10545,5)]);
         this._list[this._list.length] = new ZBaoJJVO(30111,30211,150000,800,1000,[new NeedVO(10562,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30113,30213,750000,2000,200,[new NeedVO(10580,8)]);
         this._list[this._list.length] = new ZBaoJJVO(30114,30214,200000,500,300,[new NeedVO(10539,5)]);
         this._list[this._list.length] = new ZBaoJJVO(30115,30215,100000,350,300,[new NeedVO(10547,8),new NeedVO(10548,8),new NeedVO(10549,8)]);
         this._list[this._list.length] = new ZBaoJJVO(30116,30216,800000,1500,1000,[new NeedVO(10551,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30117,30217,150000,800,250,[new NeedVO(10559,10),new NeedVO(10560,10),new NeedVO(10561,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30118,30218,800000,2000,300,[new NeedVO(10563,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30119,30219,500000,1000,300,[new NeedVO(10553,30)]);
         this._list[this._list.length] = new ZBaoJJVO(30124,30224,700000,500,600,[new NeedVO(10571,6)]);
         this._list[this._list.length] = new ZBaoJJVO(30126,30226,888888,0,1000,[new NeedVO(10574,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30128,30228,888888,1666,300,[new NeedVO(10590,30),new NeedVO(10591,30),new NeedVO(10592,30)]);
         this._list[this._list.length] = new ZBaoJJVO(30051,30351,888888,2000,300,[new NeedVO(10831,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30071,30371,888888,2000,300,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30029,30129,66666,500,1000,[new NeedVO(10831,5)]);
         this._list[this._list.length] = new ZBaoJJVO(30030,30130,666666,1500,300,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30032,30132,66666,500,1000,[new NeedVO(10831,5)]);
         this._list[this._list.length] = new ZBaoJJVO(30033,30133,66666,500,1000,[new NeedVO(10831,5)]);
         this._list[this._list.length] = new ZBaoJJVO(30034,30134,88888,888,1000,[new NeedVO(10831,15)]);
         this._list[this._list.length] = new ZBaoJJVO(30035,30135,66666,500,1000,[new NeedVO(10831,5)]);
         this._list[this._list.length] = new ZBaoJJVO(30045,30145,66666,500,1000,[new NeedVO(10831,5)]);
         this._list[this._list.length] = new ZBaoJJVO(30046,30146,66666,500,1000,[new NeedVO(10831,5)]);
         this._list[this._list.length] = new ZBaoJJVO(30129,30229,666666,1000,1000,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30130,30230,888888,2000,300,[new NeedVO(10832,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30132,30232,666666,1000,1000,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30133,30233,666666,1000,1000,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30135,30235,666666,1000,1000,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30134,30234,888888,1000,1000,[new NeedVO(10831,25)]);
         this._list[this._list.length] = new ZBaoJJVO(30101,30201,666666,888,500,[new NeedVO(10831,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30145,30245,666666,1000,1000,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30146,30246,666666,1000,1000,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30060,30360,888888,2000,300,[new NeedVO(10832,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30056,30356,888888,2000,300,[new NeedVO(10832,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30063,30363,888888,2000,300,[new NeedVO(10831,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30052,30352,888888,2000,300,[new NeedVO(10832,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30055,30355,888888,2000,300,[new NeedVO(10832,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30062,30362,888888,2000,1000,[new NeedVO(10832,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30064,30364,888888,2000,300,[new NeedVO(10832,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30072,30372,888888,2000,300,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30057,30357,888888,2000,300,[new NeedVO(10832,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30053,30353,888888,3000,1000,[new NeedVO(10833,15),new NeedVO(10575,30)]);
         this._list[this._list.length] = new ZBaoJJVO(30054,30354,888888,3000,1000,[new NeedVO(10833,15)]);
         this._list[this._list.length] = new ZBaoJJVO(30068,30368,888888,3000,1000,[new NeedVO(10914,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30069,30369,888888,3000,1000,[new NeedVO(10930,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30073,30373,888888,3000,1000,[new NeedVO(10833,15)]);
         this._list[this._list.length] = new ZBaoJJVO(30074,30374,888888,3000,1000,[new NeedVO(10833,15)]);
         this._list[this._list.length] = new ZBaoJJVO(30351,30451,888888,2000,300,[new NeedVO(10831,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30352,30452,888888,2000,300,[new NeedVO(10832,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30355,30455,888888,2000,300,[new NeedVO(10832,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30360,30460,888888,2000,300,[new NeedVO(10832,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30364,30464,888888,2000,300,[new NeedVO(10832,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30362,30462,888888,2000,1000,[new NeedVO(10833,15)]);
         this._list[this._list.length] = new ZBaoJJVO(30363,30463,888888,2000,300,[new NeedVO(10831,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30371,30471,888888,2000,300,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30372,30472,888888,2000,300,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30067,30367,888888,3000,1000,[new NeedVO(10916,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30038,30138,666666,1000,500,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30138,30238,666666,2000,250,[new NeedVO(10832,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30039,30139,666666,1000,500,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30139,30239,666666,2000,250,[new NeedVO(10832,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30040,30140,666666,1000,500,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30140,30240,666666,2000,250,[new NeedVO(10832,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30041,30141,666666,1000,500,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30141,30241,666666,2000,250,[new NeedVO(10832,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30042,30142,666666,1000,500,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30142,30242,666666,2000,250,[new NeedVO(10832,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30043,30143,666666,1000,500,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30143,30243,666666,2000,250,[new NeedVO(10832,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30044,30144,666666,1000,500,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30144,30244,666666,2000,250,[new NeedVO(10832,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30048,30148,666666,1000,500,[new NeedVO(10831,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30148,30248,666666,2000,250,[new NeedVO(10832,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30070,30370,888888,3000,1000,[new NeedVO(10975,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30085,30385,888888,2000,1000,[]);
         this._list[this._list.length] = new ZBaoJJVO(30086,30386,888888,2000,1000,[]);
         this._list[this._list.length] = new ZBaoJJVO(30087,30387,888888,2000,1000,[]);
         this._list[this._list.length] = new ZBaoJJVO(30088,30388,888888,2000,1000,[]);
         this._list[this._list.length] = new ZBaoJJVO(30090,30390,888888,2000,1000,[]);
         this._list[this._list.length] = new ZBaoJJVO(30091,30391,888888,2000,1000,[]);
         this._list[this._list.length] = new ZBaoJJVO(30092,30392,888888,2000,1000,[]);
         this._list[this._list.length] = new ZBaoJJVO(30093,30393,888888,2000,1000,[]);
         this._list[this._list.length] = new ZBaoJJVO(30094,30394,888888,2000,1000,[]);
         this._list[this._list.length] = new ZBaoJJVO(30095,30395,888888,2000,1000,[]);
         this._list[this._list.length] = new ZBaoJJVO(30096,30396,888888,2000,1000,[]);
         this._list[this._list.length] = new ZBaoJJVO(30097,30397,888888,2000,1000,[]);
         this._list[this._list.length] = new ZBaoJJVO(31096,31097,888888,2000,1000,[new NeedVO(10970,20)]);
         this._list[this._list.length] = new ZBaoJJVO(30098,31098,888888,2000,1000,[new NeedVO(10969,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31001,31101,888888,2000,1000,[]);
         this._list[this._list.length] = new ZBaoJJVO(31002,31102,888888,2000,1000,[]);
         this._list[this._list.length] = new ZBaoJJVO(31003,31103,888888,2000,1000,[]);
         this._list[this._list.length] = new ZBaoJJVO(31004,31104,888888,2000,1000,[]);
         this._list[this._list.length] = new ZBaoJJVO(31021,31121,888888,2000,1000,[]);
         this._list[this._list.length] = new ZBaoJJVO(31022,31122,888888,2000,1000,[]);
         this._list[this._list.length] = new ZBaoJJVO(31023,31123,888888,2000,1000,[]);
         this._list[this._list.length] = new ZBaoJJVO(31024,31124,888888,2000,1000,[]);
         this._list[this._list.length] = new ZBaoJJVO(31030,31130,999999,6666,1000,[new NeedVO(10853,20),new NeedVO(10854,20),new NeedVO(10855,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31130,31131,999999,7777,1000,[new NeedVO(10853,25),new NeedVO(10854,25),new NeedVO(10855,25)]);
         this._list[this._list.length] = new ZBaoJJVO(31131,31132,999999,7777,1000,[new NeedVO(10853,30),new NeedVO(10854,30),new NeedVO(10855,30)]);
         this._list[this._list.length] = new ZBaoJJVO(31061,31161,888888,3000,300,[new NeedVO(10832,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31062,31162,888888,3000,300,[new NeedVO(10832,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31063,31163,888888,3000,300,[new NeedVO(10832,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31064,31164,888888,3000,300,[new NeedVO(10832,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31161,31165,888888,4000,200,[new NeedVO(10833,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31162,31166,888888,4000,200,[new NeedVO(10833,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31163,31167,888888,4000,200,[new NeedVO(10833,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31164,31168,888888,4000,200,[new NeedVO(10833,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31094,31095,888888,4000,1000,[new NeedVO(10833,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31192,31196,888888,4000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31196,31197,888888,5000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31193,31198,888888,4000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31198,31199,888888,5000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31240,31241,888888,6000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31241,31242,888888,7000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
         this._list[this._list.length] = new ZBaoJJVO(31184,31186,888888,4000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31185,31187,888888,4000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31271,31272,888888,7000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31274,31275,888888,7000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31275,31276,888888,9000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
         this._list[this._list.length] = new ZBaoJJVO(31272,31273,888888,9000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
         this._list[this._list.length] = new ZBaoJJVO(31277,31278,888888,7000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31278,31279,888888,9000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
         this._list[this._list.length] = new ZBaoJJVO(32101,32102,888888,9000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
         this._list[this._list.length] = new ZBaoJJVO(32102,32103,888888,9000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
         this._list[this._list.length] = new ZBaoJJVO(31331,31341,888888,7000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31341,31351,888888,9000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
         this._list[this._list.length] = new ZBaoJJVO(31301,31311,888888,4000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31311,31321,888888,4000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31302,31312,888888,4000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31312,31322,888888,4000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31303,31313,888888,4000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31313,31323,888888,4000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31304,31314,888888,4000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31314,31324,888888,4000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30249,30358,888888,3000,200,[new NeedVO(10833,10),new NeedVO(10832,10)]);
         this._list[this._list.length] = new ZBaoJJVO(30358,30359,888888,3000,200,[new NeedVO(10833,15),new NeedVO(10832,15)]);
         this._list[this._list.length] = new ZBaoJJVO(31332,31342,888888,7000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31342,31352,888888,9000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
         this._list[this._list.length] = new ZBaoJJVO(31333,31343,888888,7000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31343,31353,888888,9000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
         this._list[this._list.length] = new ZBaoJJVO(31334,31344,888888,7000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31344,31354,888888,9000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
         this._list[this._list.length] = new ZBaoJJVO(31401,31411,888888,3000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31411,31421,888888,3000,1000,[new NeedVO(10833,15),new NeedVO(10856,15)]);
         this._list[this._list.length] = new ZBaoJJVO(31402,31412,888888,3000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31412,31422,888888,3000,1000,[new NeedVO(10833,15),new NeedVO(10856,15)]);
         this._list[this._list.length] = new ZBaoJJVO(31403,31413,888888,3000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31413,31423,888888,3000,1000,[new NeedVO(10833,15),new NeedVO(10856,15)]);
         this._list[this._list.length] = new ZBaoJJVO(31404,31414,888888,3000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31414,31424,888888,3000,1000,[new NeedVO(10833,15),new NeedVO(10856,15)]);
         this._list[this._list.length] = new ZBaoJJVO(31501,31511,888888,7000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31511,31521,888888,9000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
         this._list[this._list.length] = new ZBaoJJVO(31404,31414,888888,3000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31414,31424,888888,3000,1000,[new NeedVO(10833,15),new NeedVO(10856,15)]);
         this._list[this._list.length] = new ZBaoJJVO(31502,31512,888888,7000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31512,31522,888888,9000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
         this._list[this._list.length] = new ZBaoJJVO(31503,31513,888888,7000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31513,31523,888888,9000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
         this._list[this._list.length] = new ZBaoJJVO(31504,31514,888888,7000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31514,31524,888888,9000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
         this._list[this._list.length] = new ZBaoJJVO(31701,31711,888888,3000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31711,31721,888888,3000,1000,[new NeedVO(10833,15),new NeedVO(10856,15)]);
         this._list[this._list.length] = new ZBaoJJVO(31702,31712,888888,3000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31712,31722,888888,3000,1000,[new NeedVO(10833,15),new NeedVO(10856,15)]);
         this._list[this._list.length] = new ZBaoJJVO(32121,32122,888888,9000,1000,[new NeedVO(10833,50),new NeedVO(10856,50)]);
         this._list[this._list.length] = new ZBaoJJVO(32122,32123,888888,9000,1000,[new NeedVO(10833,50),new NeedVO(10856,50)]);
         this._list[this._list.length] = new ZBaoJJVO(31531,31541,888888,7000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31541,31551,888888,9000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
         this._list[this._list.length] = new ZBaoJJVO(31703,31713,888888,3000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31713,31723,888888,3000,1000,[new NeedVO(10833,15),new NeedVO(10856,15)]);
         this._list[this._list.length] = new ZBaoJJVO(31532,31542,888888,7000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31542,31552,888888,9000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
         this._list[this._list.length] = new ZBaoJJVO(31704,31714,888888,3000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31714,31724,888888,3000,1000,[new NeedVO(10833,15),new NeedVO(10856,15)]);
         this._list[this._list.length] = new ZBaoJJVO(31801,31811,888888,3000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31811,31821,888888,3000,1000,[new NeedVO(10833,15),new NeedVO(10856,15)]);
         this._list[this._list.length] = new ZBaoJJVO(31533,31543,888888,7000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31543,31553,888888,9000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
         this._list[this._list.length] = new ZBaoJJVO(31534,31544,888888,7000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31544,31554,888888,9000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
         this._list[this._list.length] = new ZBaoJJVO(31802,31812,888888,3000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31812,31822,888888,3000,1000,[new NeedVO(10833,15),new NeedVO(10856,15)]);
         this._list[this._list.length] = new ZBaoJJVO(31803,31813,888888,3000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31813,31823,888888,3000,1000,[new NeedVO(10833,15),new NeedVO(10856,15)]);
         this._list[this._list.length] = new ZBaoJJVO(31561,31571,888888,7000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31571,31581,888888,9000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
         this._list[this._list.length] = new ZBaoJJVO(31804,31814,888888,3000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31814,31824,888888,3000,1000,[new NeedVO(10833,15),new NeedVO(10856,15)]);
         this._list[this._list.length] = new ZBaoJJVO(31562,31572,888888,7000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31572,31582,888888,9000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
         this._list[this._list.length] = new ZBaoJJVO(31805,31815,888888,3000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31815,31825,888888,3000,1000,[new NeedVO(10833,15),new NeedVO(10856,15)]);
         this._list[this._list.length] = new ZBaoJJVO(31563,31573,888888,7000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31573,31583,888888,9000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
         this._list[this._list.length] = new ZBaoJJVO(31806,31816,888888,3000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31816,31826,888888,3000,1000,[new NeedVO(10833,15),new NeedVO(10856,15)]);
         this._list[this._list.length] = new ZBaoJJVO(31564,31574,888888,7000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(31574,31584,888888,9000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
         this._list[this._list.length] = new ZBaoJJVO(31807,31817,888888,3000,1000,[new NeedVO(10833,10),new NeedVO(10856,10)]);
         this._list[this._list.length] = new ZBaoJJVO(31817,31827,888888,3000,1000,[new NeedVO(10833,15),new NeedVO(10856,15)]);
         this._list[this._list.length] = new ZBaoJJVO(34001,34011,888888,7000,1000,[new NeedVO(10833,20),new NeedVO(10856,20)]);
         this._list[this._list.length] = new ZBaoJJVO(34011,34021,888888,9000,1000,[new NeedVO(10833,30),new NeedVO(10856,30)]);
      }
      
      public function findVO(param1:int) : ZBaoJJVO
      {
         var _loc2_:ZBaoJJVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.oldID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

