package com.mogames.display
{
   import com.mogames.data.ImageInfo;
   import com.mogames.utils.MethodUtil;
   import flash.display.Bitmap;
   import flash.events.Event;
   
   public class BMCSprite extends EventSprite
   {
      
      protected var _bitmap:Bitmap;
      
      protected var _sequences:Vector.<ImageInfo>;
      
      protected var _frameFunc:Function;
      
      protected var _endFunc:Function;
      
      protected var _loop:Boolean;
      
      public var curFrame:int;
      
      public var totalFrame:int;
      
      public function BMCSprite()
      {
         super();
         this._bitmap = new Bitmap();
         addChild(this._bitmap);
         MethodUtil.setMousable(this,false);
      }
      
      public function setSequences(param1:Vector.<ImageInfo>, param2:Boolean = true, param3:Function = null, param4:Function = null, param5:Boolean = true) : void
      {
         this._sequences = param1;
         this._frameFunc = param3;
         this._endFunc = param4;
         this._loop = param5;
         this.totalFrame = param1.length;
         this.curFrame = 0;
         this.showCurFrame();
         if(param2)
         {
            this.play();
         }
      }
      
      public function setloopTime(param1:Number, param2:Function) : void
      {
      }
      
      public function play() : void
      {
         addEventListener(Event.ENTER_FRAME,this.updateFrame);
      }
      
      public function stop() : void
      {
         removeEventListener(Event.ENTER_FRAME,this.updateFrame);
      }
      
      public function gotoAndStop(param1:int) : void
      {
         this.curFrame = Math.max(0,param1 - 1);
         this.showCurFrame();
         this.stop();
      }
      
      protected function updateFrame(param1:Event = null) : void
      {
         if(this.curFrame >= this.totalFrame)
         {
            this.handlerEnd();
            if(this._endFunc != null)
            {
               this._endFunc();
            }
         }
         else
         {
            this.showCurFrame();
            if(this._frameFunc != null)
            {
               this._frameFunc(this.curFrame + 1);
            }
            ++this.curFrame;
         }
      }
      
      protected function handlerEnd() : void
      {
         if(this._loop)
         {
            this.curFrame = 0;
         }
         else
         {
            this.gotoAndStop(this.totalFrame - 1);
         }
      }
      
      private function showCurFrame() : void
      {
         this._bitmap.bitmapData = this._sequences[this.curFrame].bitmapData;
         this._bitmap.x = this._sequences[this.curFrame].pivotX;
         this._bitmap.y = this._sequences[this.curFrame].pivotY;
      }
      
      public function clean() : void
      {
         this.stop();
         this._sequences = null;
         this._bitmap.bitmapData = null;
         this._frameFunc = null;
         this._endFunc = null;
      }
      
      override public function destroy() : void
      {
         this.clean();
         this._bitmap = null;
         super.destroy();
      }
   }
}

