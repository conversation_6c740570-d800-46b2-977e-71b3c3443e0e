package file
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameFuben.yougu.jiebiao.enemy.TDArgVO;
   
   public class JinZhouConfig
   {
      
      private static var _instance:JinZhouConfig;
      
      private var _maxHero:Oint = new Oint();
      
      private var _escape:Oint = new Oint();
      
      private var _food:Oint = new Oint();
      
      private var _wood:Oint = new Oint();
      
      public function JinZhouConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : JinZhouConfig
      {
         if(!_instance)
         {
            _instance = new JinZhouConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         MathUtil.saveINT(this._maxHero,8);
         MathUtil.saveINT(this._food,750);
         MathUtil.saveINT(this._wood,1000);
         MathUtil.saveINT(this._escape,10);
      }
      
      public function newWaveData() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO();
         _loc2_ = new OneWaveVO(45);
         _loc2_.newEnemy = new WaveEnemyVO(10,this.newArgVO(101,6000,1,1,70,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.newEnemy = new WaveEnemyVO(20,this.newArgVO(102,8000,1,1,80,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.newEnemy = new WaveEnemyVO(13,this.newArgVO(103,10000,1,1,85,0));
         _loc2_.newEnemy = new WaveEnemyVO(1,this.newArgVO(202,100000,1,1,40,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.newEnemy = new WaveEnemyVO(17,this.newArgVO(104,12000,1,1,70,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.newEnemy = new WaveEnemyVO(15,this.newArgVO(105,14000,1,1,80,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.newEnemy = new WaveEnemyVO(16,this.newArgVO(106,16000,1,1,100,0));
         _loc2_.newEnemy = new WaveEnemyVO(1,this.newArgVO(205,100000,1,1,40,0));
         _loc2_.newEnemy = new WaveEnemyVO(1,this.newArgVO(390,150000,1,1,40,13130));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
      
      private function newArgVO(param1:int, param2:int, param3:int, param4:int, param5:int, param6:int) : TDArgVO
      {
         return new TDArgVO(true,param1,param2,param3,param4,param5,param6);
      }
      
      public function get maxHero() : int
      {
         return MathUtil.loadINT(this._maxHero);
      }
      
      public function get needWood() : int
      {
         return MathUtil.loadINT(this._wood);
      }
      
      public function get needFood() : int
      {
         return MathUtil.loadINT(this._food);
      }
      
      public function get failNeed() : int
      {
         return MathUtil.loadINT(this._escape);
      }
   }
}

