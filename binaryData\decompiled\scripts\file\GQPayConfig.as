package file
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.base.func.GQPayVO;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.base.vo.EquipRewardVO;
   
   public class GQPayConfig
   {
      
      private static var _instance:GQPayConfig;
      
      private var _list:Array;
      
      private var _total:Oint = new Oint();
      
      public var timeInfor:String;
      
      public function GQPayConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : GQPayConfig
      {
         if(!_instance)
         {
            _instance = new GQPayConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = [];
         this._list[this._list.length] = new GQPayVO(11,500,[new BaseRewardVO(50040,40),new BaseRewardVO(10986,1),new BaseRewardVO(10984,5)]);
         this._list[this._list.length] = new GQPayVO(12,1500,[new BaseRewardVO(50030,40),new BaseRewardVO(50031,40),new BaseRewardVO(10291,20)]);
         this._list[this._list.length] = new GQPayVO(13,3000,[new BaseRewardVO(50044,40),new BaseRewardVO(19014,2),new BaseRewardVO(10987,6)]);
         this._list[this._list.length] = new GQPayVO(14,5000,[new BaseRewardVO(50014,40),new BaseRewardVO(19015,2),new EquipRewardVO(31673,3)]);
         this._list[this._list.length] = new GQPayVO(15,7500,[new BaseRewardVO(50048,40),new BaseRewardVO(18555,6),new BaseRewardVO(10856,50)]);
         this._list[this._list.length] = new GQPayVO(16,10000,[new BaseRewardVO(18955,2),new BaseRewardVO(19016,3),new EquipRewardVO(31677,3)]);
         this.timeInfor = "活动时间:07月22日-08月03日";
         MathUtil.saveINT(this._total,0);
      }
      
      public function get list() : Array
      {
         return this._list;
      }
      
      public function get total() : int
      {
         return MathUtil.loadINT(this._total);
      }
      
      public function set total(param1:int) : void
      {
         MathUtil.saveINT(this._total,param1);
      }
      
      public function get timeStamp() : Object
      {
         var _loc1_:Object = new Object();
         _loc1_.sDate = "2025-07-22|00:00:00";
         _loc1_.eDate = "2025-08-03|23:59:59";
         return _loc1_;
      }
   }
}

