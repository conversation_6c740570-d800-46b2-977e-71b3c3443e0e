package mogames.gameData.base.func
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import flash.geom.Point;
   import mogames.gameData.flag.FlagProxy;
   
   public class QuanVO
   {
      
      private var _id:Oint = new Oint();
      
      private var _flagID:Oint = new Oint();
      
      private var _location:Point;
      
      public function QuanVO(param1:int, param2:int, param3:Point)
      {
         super();
         MathUtil.saveINT(this._id,param1);
         MathUtil.saveINT(this._flagID,param2);
         this._location = param3;
      }
      
      public function handlerGet() : void
      {
         FlagProxy.instance().openFlag.setValue(this.flagID);
      }
      
      private function get flagID() : int
      {
         return MathUtil.loadINT(this._flagID);
      }
      
      public function get localID() : int
      {
         return MathUtil.loadINT(this._id);
      }
      
      public function get canGet() : Boolean
      {
         return FlagProxy.instance().openFlag.isComplete(30) && !FlagProxy.instance().openFlag.isComplete(this.flagID);
      }
      
      public function get location() : Point
      {
         return this._location;
      }
   }
}

