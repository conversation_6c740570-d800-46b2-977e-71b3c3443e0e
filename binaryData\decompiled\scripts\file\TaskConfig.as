package file
{
   import com.mogames.data.RandomVO;
   import com.mogames.data.ValueAreaVO;
   import mogames.gameData.task.base.BaseTaskVO;
   import mogames.gameData.task.tavern.vo.FeelAreaVO;
   import mogames.gameData.task.tavern.vo.FeelTaskVO1;
   import mogames.gameData.task.tavern.vo.FeelTaskVO2;
   import mogames.gameData.task.tavern.vo.FeelTaskVO3;
   import mogames.gameData.task.tavern.vo.FeelTaskVO4;
   import mogames.gameData.task.tavern.vo.FeelTaskVO5;
   import mogames.gameData.task.town.TownTaskVO;
   
   public class TaskConfig
   {
      
      private static var _instance:TaskConfig;
      
      private var _dict:Array;
      
      private var _areas:Array;
      
      public function TaskConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : TaskConfig
      {
         if(!_instance)
         {
            _instance = new TaskConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._dict = [];
         this._dict[this._dict.length] = new FeelTaskVO1(1001,20002,new RandomVO(1,1),new RandomVO(25,50),"这位兄台，现在兵荒马乱的，可不可以送我@param0把@param1用来防身？");
         this._dict[this._dict.length] = new FeelTaskVO1(1002,20102,new RandomVO(1,1),new RandomVO(25,50),"这位兄台，现在兵荒马乱的，可不可以送我@param0把@param1用来防身？");
         this._dict[this._dict.length] = new FeelTaskVO1(1003,20202,new RandomVO(1,1),new RandomVO(25,50),"这位兄台，现在兵荒马乱的，可不可以送我@param0把@param1用来防身？");
         this._dict[this._dict.length] = new FeelTaskVO1(1004,20302,new RandomVO(1,1),new RandomVO(25,50),"这位兄台，现在兵荒马乱的，可不可以送我@param0把@param1用来防身？");
         this._dict[this._dict.length] = new FeelTaskVO1(1005,20402,new RandomVO(1,1),new RandomVO(25,50),"这位兄台，现在兵荒马乱的，可不可以送我@param0把@param1用来防身？");
         this._dict[this._dict.length] = new FeelTaskVO1(1006,20003,new RandomVO(2,2),new RandomVO(55,88),"这位兄台，现在兵荒马乱的，可不可以送我@param0把@param1用来防身？");
         this._dict[this._dict.length] = new FeelTaskVO1(1007,20103,new RandomVO(2,2),new RandomVO(55,88),"这位兄台，现在兵荒马乱的，可不可以送我@param0把@param1用来防身？");
         this._dict[this._dict.length] = new FeelTaskVO1(1008,20203,new RandomVO(2,2),new RandomVO(55,88),"这位兄台，现在兵荒马乱的，可不可以送我@param0把@param1用来防身？");
         this._dict[this._dict.length] = new FeelTaskVO1(1009,20303,new RandomVO(2,2),new RandomVO(55,88),"这位兄台，现在兵荒马乱的，可不可以送我@param0把@param1用来防身？");
         this._dict[this._dict.length] = new FeelTaskVO1(1010,20403,new RandomVO(2,2),new RandomVO(55,88),"这位兄台，现在兵荒马乱的，可不可以送我@param0把@param1用来防身？");
         this._dict[this._dict.length] = new FeelTaskVO2(1700,new RandomVO(1,3),new RandomVO(10,99));
         this._dict[this._dict.length] = new FeelTaskVO3(1101,10021,new RandomVO(1,2),new RandomVO(15,50),"这位兄台，能送我@param0壶@param1吗？");
         this._dict[this._dict.length] = new FeelTaskVO3(1102,10022,new RandomVO(1,2),new RandomVO(15,50),"这位小帅哥，能帮我找@param0壶@param1吗？");
         this._dict[this._dict.length] = new FeelTaskVO3(1103,10023,new RandomVO(1,2),new RandomVO(15,50),"小兄弟，酒馆里的酒都像清水，能帮我找@param0壶@param1吗？");
         this._dict[this._dict.length] = new FeelTaskVO3(1104,10024,new RandomVO(1,2),new RandomVO(15,50),"这位大哥，能帮我找@param0匹@param1吗？下次有钱了加倍还你！");
         this._dict[this._dict.length] = new FeelTaskVO3(1105,10025,new RandomVO(1,2),new RandomVO(15,50),"大侠！帮个小忙，找@param0杯@param1给我解解渴？");
         this._dict[this._dict.length] = new FeelTaskVO3(1106,10026,new RandomVO(1,2),new RandomVO(15,50),"这位仁兄，能帮我找@param0块@param1吗？家里穷困买不起。");
         this._dict[this._dict.length] = new FeelTaskVO3(1107,10021,new RandomVO(3,4),new RandomVO(51,88),"这位兄台，能送我@param0壶@param1吗？");
         this._dict[this._dict.length] = new FeelTaskVO3(1108,10022,new RandomVO(3,4),new RandomVO(51,88),"这位小帅哥，能帮我找@param0壶@param1吗？");
         this._dict[this._dict.length] = new FeelTaskVO3(1109,10023,new RandomVO(3,4),new RandomVO(51,88),"小兄弟，酒馆里的酒都像清水，能帮我找@param0壶@param1吗？");
         this._dict[this._dict.length] = new FeelTaskVO3(1110,10024,new RandomVO(3,4),new RandomVO(51,88),"这位大哥，能帮我找@param0匹@param1吗？下次有钱了加倍还你！");
         this._dict[this._dict.length] = new FeelTaskVO3(1111,10025,new RandomVO(3,4),new RandomVO(51,88),"大侠！帮个小忙，找@param0杯@param1给我解解渴？");
         this._dict[this._dict.length] = new FeelTaskVO3(1112,10026,new RandomVO(3,4),new RandomVO(51,88),"这位仁兄，能帮我找@param0块@param1吗？家里穷困买不起。");
         this._dict[this._dict.length] = new FeelTaskVO4(1150,new RandomVO(500,1000),new RandomVO(5,25));
         this._dict[this._dict.length] = new FeelTaskVO4(1151,new RandomVO(1000,1500),new RandomVO(25,35));
         this._dict[this._dict.length] = new FeelTaskVO4(1152,new RandomVO(1500,2500),new RandomVO(35,45));
         this._dict[this._dict.length] = new FeelTaskVO4(1153,new RandomVO(2500,4500),new RandomVO(45,55));
         this._dict[this._dict.length] = new FeelTaskVO4(1154,new RandomVO(4500,6500),new RandomVO(55,65));
         this._dict[this._dict.length] = new FeelTaskVO4(1155,new RandomVO(6500,9000),new RandomVO(65,75));
         this._dict[this._dict.length] = new FeelTaskVO4(1156,new RandomVO(9000,11000),new RandomVO(75,85));
         this._dict[this._dict.length] = new FeelTaskVO4(1157,new RandomVO(11000,15000),new RandomVO(85,100));
         this._dict[this._dict.length] = new FeelTaskVO5(1180,new RandomVO(100,150),new RandomVO(10,40));
         this._dict[this._dict.length] = new FeelTaskVO5(1181,new RandomVO(100,3000),new RandomVO(41,70));
         this._dict[this._dict.length] = new TownTaskVO(2300,1,"赈灾事件");
         this._dict[this._dict.length] = new TownTaskVO(2301,2,"城被一伙山贼进攻霸占了，请速速前往剿灭。");
         this._dict[this._dict.length] = new TownTaskVO(2302,3,"城发生了暴动事件，请速速前往镇压。");
         this._areas = [];
         this._areas[this._areas.length] = new FeelAreaVO(new ValueAreaVO(0,200),[1101,1102,1103,1104,1105,1106,1150]);
         this._areas[this._areas.length] = new FeelAreaVO(new ValueAreaVO(201,400),[1101,1102,1103,1104,1105,1106,1151,1001,1002]);
         this._areas[this._areas.length] = new FeelAreaVO(new ValueAreaVO(401,600),[1104,1105,1106,1003,1004,1152]);
         this._areas[this._areas.length] = new FeelAreaVO(new ValueAreaVO(601,800),[1107,1108,1109,1110,1111,1112,1153]);
         this._areas[this._areas.length] = new FeelAreaVO(new ValueAreaVO(801,1000),[1006,1007,1008,1009,1010,1154,1110,1111,1112]);
      }
      
      public function findTaskVO(param1:int) : BaseTaskVO
      {
         var _loc2_:BaseTaskVO = null;
         for each(_loc2_ in this._dict)
         {
            if(_loc2_.taskID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function randomFeel(param1:int) : int
      {
         var _loc2_:FeelAreaVO = null;
         for each(_loc2_ in this._areas)
         {
            if(_loc2_.isInRate(param1))
            {
               return _loc2_.randTaskID;
            }
         }
         return 0;
      }
   }
}

