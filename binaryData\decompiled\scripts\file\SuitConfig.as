package file
{
   import com.mogames.data.ValueVO;
   import mogames.gameData.BaseSuitVO;
   import mogames.gameData.suit.PetSuitVO;
   import mogames.gameData.suit.SuitAttVO;
   
   public class SuitConfig
   {
      
      private static var _instance:SuitConfig;
      
      private var _heros:Vector.<BaseSuitVO>;
      
      private var _pets:Vector.<PetSuitVO>;
      
      public function SuitConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : SuitConfig
      {
         if(!_instance)
         {
            _instance = new SuitConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._heros = new Vector.<BaseSuitVO>();
         this._heros[this._heros.length] = new BaseSuitVO(1001,[20007,21007,22007,23007,24007,25007],new SuitAttVO(2,new ValueVO(2850,0)),new SuitAttVO(3,new ValueVO(32,0)),new SuitAttVO(0,new ValueVO(120,0)),"地银套装");
         this._heros[this._heros.length] = new BaseSuitVO(1002,[20107,21107,22107,23107,24107,25107],new SuitAttVO(2,new ValueVO(2425,0)),new SuitAttVO(3,new ValueVO(40,0)),new SuitAttVO(0,new ValueVO(140,0)),"秘月套装");
         this._heros[this._heros.length] = new BaseSuitVO(1003,[20207,21207,22207,23207,24207,25207],new SuitAttVO(2,new ValueVO(1925,0)),new SuitAttVO(3,new ValueVO(28,0)),new SuitAttVO(0,new ValueVO(140,0)),"凌风套装");
         this._heros[this._heros.length] = new BaseSuitVO(1004,[20307,21307,22307,23307,24307,25307],new SuitAttVO(2,new ValueVO(2150,0)),new SuitAttVO(3,new ValueVO(32,0)),new SuitAttVO(0,new ValueVO(126,0)),"雀舌套装");
         this._heros[this._heros.length] = new BaseSuitVO(1005,[20407,21407,22407,23407,24407,25407],new SuitAttVO(2,new ValueVO(2550,0)),new SuitAttVO(3,new ValueVO(32,0)),new SuitAttVO(0,new ValueVO(175,0)),"未央套装");
         this._heros[this._heros.length] = new BaseSuitVO(1011,[20008,21008,22008,23008,24008,25008],new SuitAttVO(2,new ValueVO(4275,0)),new SuitAttVO(3,new ValueVO(48,0)),new SuitAttVO(0,new ValueVO(180,0)),"赤狱套装");
         this._heros[this._heros.length] = new BaseSuitVO(1012,[20108,21108,22108,23108,24108,25108],new SuitAttVO(2,new ValueVO(3638,0)),new SuitAttVO(3,new ValueVO(60,0)),new SuitAttVO(0,new ValueVO(210,0)),"天云套装");
         this._heros[this._heros.length] = new BaseSuitVO(1013,[20208,21208,22208,23208,24208,25208],new SuitAttVO(2,new ValueVO(2887,0)),new SuitAttVO(3,new ValueVO(42,0)),new SuitAttVO(0,new ValueVO(210,0)),"火龙套装");
         this._heros[this._heros.length] = new BaseSuitVO(1014,[20308,21308,22308,23308,24308,25308],new SuitAttVO(2,new ValueVO(3225,0)),new SuitAttVO(3,new ValueVO(48,0)),new SuitAttVO(0,new ValueVO(189,0)),"龙舌套装");
         this._heros[this._heros.length] = new BaseSuitVO(1015,[20408,21408,22408,23408,24408,25408],new SuitAttVO(2,new ValueVO(3825,0)),new SuitAttVO(3,new ValueVO(48,0)),new SuitAttVO(0,new ValueVO(262,0)),"紫电套装");
         this._heros[this._heros.length] = new BaseSuitVO(1021,[20009,21009,22009,23009,24009,25009],new SuitAttVO(2,new ValueVO(6412,0)),new SuitAttVO(3,new ValueVO(64,0)),new SuitAttVO(0,new ValueVO(240,0)),"苍琊套装");
         this._heros[this._heros.length] = new BaseSuitVO(1022,[20109,21109,22109,23109,24109,25109],new SuitAttVO(2,new ValueVO(5457,0)),new SuitAttVO(3,new ValueVO(80,0)),new SuitAttVO(0,new ValueVO(280,0)),"狂琅套装");
         this._heros[this._heros.length] = new BaseSuitVO(1023,[20209,21209,22209,23209,24209,25209],new SuitAttVO(2,new ValueVO(4330,0)),new SuitAttVO(3,new ValueVO(54,0)),new SuitAttVO(0,new ValueVO(270,0)),"赤焰套装");
         this._heros[this._heros.length] = new BaseSuitVO(1024,[20309,21309,22309,23309,24309,25309],new SuitAttVO(2,new ValueVO(4837,0)),new SuitAttVO(3,new ValueVO(64,0)),new SuitAttVO(0,new ValueVO(252,0)),"雀羽套装");
         this._heros[this._heros.length] = new BaseSuitVO(1025,[20409,21409,22409,23409,24409,25409],new SuitAttVO(2,new ValueVO(5737,0)),new SuitAttVO(3,new ValueVO(64,0)),new SuitAttVO(0,new ValueVO(349,0)),"灭陨套装");
         this._heros[this._heros.length] = new BaseSuitVO(1031,[20010,21010,22010,23010,24010,25010],new SuitAttVO(2,new ValueVO(9618,0)),new SuitAttVO(3,new ValueVO(80,0)),new SuitAttVO(0,new ValueVO(300,0)),"末日套装");
         this._heros[this._heros.length] = new BaseSuitVO(1032,[20110,21110,22110,23110,24110,25110],new SuitAttVO(2,new ValueVO(8185,0)),new SuitAttVO(3,new ValueVO(100,0)),new SuitAttVO(0,new ValueVO(350,0)),"炽勇套装");
         this._heros[this._heros.length] = new BaseSuitVO(1033,[20210,21210,22210,23210,24210,25210],new SuitAttVO(2,new ValueVO(6495,0)),new SuitAttVO(3,new ValueVO(66,0)),new SuitAttVO(0,new ValueVO(340,0)),"荣光套装");
         this._heros[this._heros.length] = new BaseSuitVO(1034,[20310,21310,22310,23310,24310,25310],new SuitAttVO(2,new ValueVO(7255,0)),new SuitAttVO(3,new ValueVO(80,0)),new SuitAttVO(0,new ValueVO(315,0)),"苍羽套装");
         this._heros[this._heros.length] = new BaseSuitVO(1035,[20410,21410,22410,23410,24410,25410],new SuitAttVO(2,new ValueVO(8605,0)),new SuitAttVO(3,new ValueVO(80,0)),new SuitAttVO(0,new ValueVO(436,0)),"玉清套装");
         this._heros[this._heros.length] = new BaseSuitVO(1041,[20011,21011,22011,23011,24011,25011],new SuitAttVO(2,new ValueVO(14427,0)),new SuitAttVO(3,new ValueVO(96,0)),new SuitAttVO(0,new ValueVO(360,0)),"地煞套装");
         this._heros[this._heros.length] = new BaseSuitVO(1042,[20111,21111,22111,23111,24111,25111],new SuitAttVO(2,new ValueVO(12227,0)),new SuitAttVO(3,new ValueVO(120,0)),new SuitAttVO(0,new ValueVO(420,0)),"天狼套装");
         this._heros[this._heros.length] = new BaseSuitVO(1043,[20211,21211,22211,23211,24211,25211],new SuitAttVO(2,new ValueVO(9742,0)),new SuitAttVO(3,new ValueVO(78,0)),new SuitAttVO(0,new ValueVO(410,0)),"苍龙套装");
         this._heros[this._heros.length] = new BaseSuitVO(1044,[20311,21311,22311,23311,24311,25311],new SuitAttVO(2,new ValueVO(10882,0)),new SuitAttVO(3,new ValueVO(96,0)),new SuitAttVO(0,new ValueVO(385,0)),"啸羽套装");
         this._heros[this._heros.length] = new BaseSuitVO(1045,[20411,21411,22411,23411,24411,25411],new SuitAttVO(2,new ValueVO(12907,0)),new SuitAttVO(3,new ValueVO(96,0)),new SuitAttVO(0,new ValueVO(523,0)),"智者套装");
         this._heros[this._heros.length] = new BaseSuitVO(1051,[20012,21012,22012,23012,24012,25012],new SuitAttVO(2,new ValueVO(17312,0)),new SuitAttVO(3,new ValueVO(112,0)),new SuitAttVO(0,new ValueVO(420,0)),"碧水套装");
         this._heros[this._heros.length] = new BaseSuitVO(1052,[20112,21112,22112,23112,24112,25112],new SuitAttVO(2,new ValueVO(15895,0)),new SuitAttVO(3,new ValueVO(140,0)),new SuitAttVO(0,new ValueVO(490,0)),"君子套装");
         this._heros[this._heros.length] = new BaseSuitVO(1053,[20212,21212,22212,23212,24212,25212],new SuitAttVO(2,new ValueVO(12664,0)),new SuitAttVO(3,new ValueVO(90,0)),new SuitAttVO(0,new ValueVO(480,0)),"冷月套装");
         this._heros[this._heros.length] = new BaseSuitVO(1054,[20312,21312,22312,23312,24312,25312],new SuitAttVO(2,new ValueVO(13058,0)),new SuitAttVO(3,new ValueVO(112,0)),new SuitAttVO(0,new ValueVO(455,0)),"虎头套装");
         this._heros[this._heros.length] = new BaseSuitVO(1055,[20412,21412,22412,23412,24412,25412],new SuitAttVO(2,new ValueVO(15448,0)),new SuitAttVO(3,new ValueVO(112,0)),new SuitAttVO(0,new ValueVO(610,0)),"瑚金套装");
         this._heros[this._heros.length] = new BaseSuitVO(1061,[20013,21013,22013,23013,24013,25013],new SuitAttVO(2,new ValueVO(20774,0)),new SuitAttVO(3,new ValueVO(128,0)),new SuitAttVO(0,new ValueVO(480,0)),"火麟套装");
         this._heros[this._heros.length] = new BaseSuitVO(1062,[20113,21113,22113,23113,24113,25113],new SuitAttVO(2,new ValueVO(19868,0)),new SuitAttVO(3,new ValueVO(160,0)),new SuitAttVO(0,new ValueVO(560,0)),"岩陨套装");
         this._heros[this._heros.length] = new BaseSuitVO(1063,[20213,21213,22213,23213,24213,25213],new SuitAttVO(2,new ValueVO(16463,0)),new SuitAttVO(3,new ValueVO(102,0)),new SuitAttVO(0,new ValueVO(550,0)),"煞灭套装");
         this._heros[this._heros.length] = new BaseSuitVO(1064,[20313,21313,22313,23313,24313,25313],new SuitAttVO(2,new ValueVO(18281,0)),new SuitAttVO(3,new ValueVO(128,0)),new SuitAttVO(0,new ValueVO(525,0)),"苍月套装");
         this._heros[this._heros.length] = new BaseSuitVO(1065,[20413,21413,22413,23413,24413,25413],new SuitAttVO(2,new ValueVO(19618,0)),new SuitAttVO(3,new ValueVO(128,0)),new SuitAttVO(0,new ValueVO(697,0)),"紫耀套装");
         this._heros[this._heros.length] = new BaseSuitVO(1071,[20014,21014,22014,23014,24014,25014],new SuitAttVO(2,new ValueVO(24928,0)),new SuitAttVO(3,new ValueVO(144,0)),new SuitAttVO(0,new ValueVO(540,0)),"虎牢套装");
         this._heros[this._heros.length] = new BaseSuitVO(1072,[20114,21114,22114,23114,24114,25114],new SuitAttVO(2,new ValueVO(23841,0)),new SuitAttVO(3,new ValueVO(180,0)),new SuitAttVO(0,new ValueVO(630,0)),"当阳套装");
         this._heros[this._heros.length] = new BaseSuitVO(1073,[20214,21214,22214,23214,24214,25214],new SuitAttVO(2,new ValueVO(19755,0)),new SuitAttVO(3,new ValueVO(114,0)),new SuitAttVO(0,new ValueVO(620,0)),"狂歌套装");
         this._heros[this._heros.length] = new BaseSuitVO(1074,[20314,21314,22314,23314,24314,25314],new SuitAttVO(2,new ValueVO(21937,0)),new SuitAttVO(3,new ValueVO(144,0)),new SuitAttVO(0,new ValueVO(605,0)),"逍遥套装");
         this._heros[this._heros.length] = new BaseSuitVO(1075,[20414,21414,22414,23414,24414,25414],new SuitAttVO(2,new ValueVO(23541,0)),new SuitAttVO(3,new ValueVO(144,0)),new SuitAttVO(0,new ValueVO(784,0)),"问天套装");
         this._heros[this._heros.length] = new BaseSuitVO(1081,[20015,21015,22015,23015,24015,25015],new SuitAttVO(2,new ValueVO(29915,0)),new SuitAttVO(3,new ValueVO(160,0)),new SuitAttVO(0,new ValueVO(600,0)),"银狻套装");
         this._heros[this._heros.length] = new BaseSuitVO(1082,[20115,21115,22115,23115,24115,25115],new SuitAttVO(2,new ValueVO(28609,0)),new SuitAttVO(3,new ValueVO(200,0)),new SuitAttVO(0,new ValueVO(700,0)),"流魔套装");
         this._heros[this._heros.length] = new BaseSuitVO(1083,[20215,21215,22215,23215,24215,25215],new SuitAttVO(2,new ValueVO(23706,0)),new SuitAttVO(3,new ValueVO(126,0)),new SuitAttVO(0,new ValueVO(690,0)),"雁回套装");
         this._heros[this._heros.length] = new BaseSuitVO(1084,[20315,21315,22315,23315,24315,25315],new SuitAttVO(2,new ValueVO(26325,0)),new SuitAttVO(3,new ValueVO(160,0)),new SuitAttVO(0,new ValueVO(680,0)),"百凌套装");
         this._heros[this._heros.length] = new BaseSuitVO(1085,[20415,21415,22415,23415,24415,25415],new SuitAttVO(2,new ValueVO(28249,0)),new SuitAttVO(3,new ValueVO(160,0)),new SuitAttVO(0,new ValueVO(870,0)),"琉巫套装");
         this._heros[this._heros.length] = new BaseSuitVO(1101,[20051,21051,22051,23051,24051,25051],new SuitAttVO(3,new ValueVO(944,0)),new SuitAttVO(0,new ValueVO(1464,0)),new SuitAttVO(2,new ValueVO(42000,0)),"神临套装");
         this._heros[this._heros.length] = new BaseSuitVO(1102,[20151,21151,22151,23151,24151,25151],new SuitAttVO(3,new ValueVO(1154,0)),new SuitAttVO(0,new ValueVO(1498,0)),new SuitAttVO(2,new ValueVO(41900,0)),"神兵套装");
         this._heros[this._heros.length] = new BaseSuitVO(1103,[20251,21251,22251,23251,24251,25251],new SuitAttVO(3,new ValueVO(1050,0)),new SuitAttVO(0,new ValueVO(1448,0)),new SuitAttVO(2,new ValueVO(38400,0)),"神斗套装");
         this._heros[this._heros.length] = new BaseSuitVO(1104,[20351,21351,22351,23351,24351,25351],new SuitAttVO(3,new ValueVO(1050,0)),new SuitAttVO(0,new ValueVO(1416,0)),new SuitAttVO(2,new ValueVO(37800,0)),"神者套装");
         this._heros[this._heros.length] = new BaseSuitVO(1105,[20451,21451,22451,23451,24451,25451],new SuitAttVO(3,new ValueVO(840,0)),new SuitAttVO(0,new ValueVO(1520,0)),new SuitAttVO(2,new ValueVO(36000,0)),"神皆套装");
         this._heros[this._heros.length] = new BaseSuitVO(1111,[20052,21052,22052,23052,24052,25052],new SuitAttVO(3,new ValueVO(1226,0)),new SuitAttVO(2,new ValueVO(58800,0)),new SuitAttVO(0,new ValueVO(2488,0)),"1星神临套装");
         this._heros[this._heros.length] = new BaseSuitVO(1112,[20152,21152,22152,23152,24152,25152],new SuitAttVO(3,new ValueVO(1500,0)),new SuitAttVO(2,new ValueVO(58860,0)),new SuitAttVO(0,new ValueVO(2525,0)),"1星神兵套装");
         this._heros[this._heros.length] = new BaseSuitVO(1113,[20252,21252,22252,23252,24252,25252],new SuitAttVO(3,new ValueVO(1364,0)),new SuitAttVO(2,new ValueVO(53760,0)),new SuitAttVO(0,new ValueVO(2461,0)),"1星神斗套装");
         this._heros[this._heros.length] = new BaseSuitVO(1114,[20352,21352,22352,23352,24352,25352],new SuitAttVO(3,new ValueVO(1364,0)),new SuitAttVO(2,new ValueVO(52920,0)),new SuitAttVO(0,new ValueVO(2407,0)),"1星神者套装");
         this._heros[this._heros.length] = new BaseSuitVO(1115,[20452,21452,22452,23452,24452,25452],new SuitAttVO(3,new ValueVO(1092,0)),new SuitAttVO(2,new ValueVO(50400,0)),new SuitAttVO(0,new ValueVO(2584,0)),"1星神皆套装");
         this._heros[this._heros.length] = new BaseSuitVO(1116,[20053,21053,22053,23053,24053,25053],new SuitAttVO(3,new ValueVO(1592,0)),new SuitAttVO(2,new ValueVO(82320,0)),new SuitAttVO(0,new ValueVO(4229,0)),"2星神临套装");
         this._heros[this._heros.length] = new BaseSuitVO(1117,[20153,21153,22153,23153,24153,25153],new SuitAttVO(3,new ValueVO(1950,0)),new SuitAttVO(2,new ValueVO(82204,0)),new SuitAttVO(0,new ValueVO(4352,0)),"2星神兵套装");
         this._heros[this._heros.length] = new BaseSuitVO(1118,[20253,21253,22253,23253,24253,25253],new SuitAttVO(3,new ValueVO(1772,0)),new SuitAttVO(2,new ValueVO(75264,0)),new SuitAttVO(0,new ValueVO(4183,0)),"2星神斗套装");
         this._heros[this._heros.length] = new BaseSuitVO(1119,[20353,21353,22353,23353,24353,25353],new SuitAttVO(3,new ValueVO(1772,0)),new SuitAttVO(2,new ValueVO(74088,0)),new SuitAttVO(0,new ValueVO(4091,0)),"2星神者套装");
         this._heros[this._heros.length] = new BaseSuitVO(1120,[20453,21453,22453,23453,24453,25453],new SuitAttVO(3,new ValueVO(1418,0)),new SuitAttVO(2,new ValueVO(70560,0)),new SuitAttVO(0,new ValueVO(4392,0)),"2星神皆套装");
         this._heros[this._heros.length] = new BaseSuitVO(1121,[20054,21054,22054,23054,24054,25054],new SuitAttVO(3,new ValueVO(2388,0)),new SuitAttVO(2,new ValueVO(115248,0)),new SuitAttVO(0,new ValueVO(7189,0)),"3星神临套装");
         this._heros[this._heros.length] = new BaseSuitVO(1122,[20154,21154,22154,23154,24154,25154],new SuitAttVO(3,new ValueVO(2924,0)),new SuitAttVO(2,new ValueVO(119485,0)),new SuitAttVO(0,new ValueVO(7418,0)),"3星神兵套装");
         this._heros[this._heros.length] = new BaseSuitVO(1123,[20254,21254,22254,23254,24254,25254],new SuitAttVO(3,new ValueVO(2658,0)),new SuitAttVO(2,new ValueVO(105369,0)),new SuitAttVO(0,new ValueVO(7116,0)),"3星神斗套装");
         this._heros[this._heros.length] = new BaseSuitVO(1124,[20354,21354,22354,23354,24354,25354],new SuitAttVO(3,new ValueVO(2658,0)),new SuitAttVO(2,new ValueVO(103723,0)),new SuitAttVO(0,new ValueVO(6954,0)),"3星神者套装");
         this._heros[this._heros.length] = new BaseSuitVO(1125,[20454,21454,22454,23454,24454,25454],new SuitAttVO(3,new ValueVO(2126,0)),new SuitAttVO(2,new ValueVO(98784,0)),new SuitAttVO(0,new ValueVO(7466,0)),"3星神皆套装");
         this._heros[this._heros.length] = new BaseSuitVO(1126,[20055,21055,22055,23055,24055,25055],new SuitAttVO(3,new ValueVO(3582,0)),new SuitAttVO(2,new ValueVO(161347,0)),new SuitAttVO(0,new ValueVO(12221,0)),"4星神临套装");
         this._heros[this._heros.length] = new BaseSuitVO(1127,[20155,21155,22155,23155,24155,25155],new SuitAttVO(3,new ValueVO(4386,0)),new SuitAttVO(2,new ValueVO(163279,0)),new SuitAttVO(0,new ValueVO(12620,0)),"4星神兵套装");
         this._heros[this._heros.length] = new BaseSuitVO(1128,[20255,21255,22255,23255,24255,25255],new SuitAttVO(3,new ValueVO(3986,0)),new SuitAttVO(2,new ValueVO(147516,0)),new SuitAttVO(0,new ValueVO(12097,0)),"4星神斗套装");
         this._heros[this._heros.length] = new BaseSuitVO(1129,[20355,21355,22355,23355,24355,25355],new SuitAttVO(3,new ValueVO(3986,0)),new SuitAttVO(2,new ValueVO(145212,0)),new SuitAttVO(0,new ValueVO(11821,0)),"4星神者套装");
         this._heros[this._heros.length] = new BaseSuitVO(1130,[20455,21455,22455,23455,24455,25455],new SuitAttVO(3,new ValueVO(3188,0)),new SuitAttVO(2,new ValueVO(138297,0)),new SuitAttVO(0,new ValueVO(12692,0)),"4星神皆套装");
         this._heros[this._heros.length] = new BaseSuitVO(1131,[20056,21056,22056,23056,24056,25056],new SuitAttVO(3,new ValueVO(4656,0)),new SuitAttVO(2,new ValueVO(209751,0)),new SuitAttVO(0,new ValueVO(20775,0)),"5星神临套装");
         this._heros[this._heros.length] = new BaseSuitVO(1132,[20156,21156,22156,23156,24156,25156],new SuitAttVO(3,new ValueVO(5700,0)),new SuitAttVO(2,new ValueVO(209262,0)),new SuitAttVO(0,new ValueVO(21414,0)),"5星神兵套装");
         this._heros[this._heros.length] = new BaseSuitVO(1133,[20256,21256,22256,23256,24256,25256],new SuitAttVO(3,new ValueVO(5180,0)),new SuitAttVO(2,new ValueVO(191770,0)),new SuitAttVO(0,new ValueVO(20564,0)),"5星神斗套装");
         this._heros[this._heros.length] = new BaseSuitVO(1134,[20356,21356,22356,23356,24356,25356],new SuitAttVO(3,new ValueVO(5180,0)),new SuitAttVO(2,new ValueVO(188775,0)),new SuitAttVO(0,new ValueVO(20095,0)),"5星神者套装");
         this._heros[this._heros.length] = new BaseSuitVO(1135,[20456,21456,22456,23456,24456,25456],new SuitAttVO(3,new ValueVO(4144,0)),new SuitAttVO(2,new ValueVO(179786,0)),new SuitAttVO(0,new ValueVO(21576,0)),"5星神皆套装");
         this._heros[this._heros.length] = new BaseSuitVO(1136,[20057,21057,22057,23057,24057,25057],new SuitAttVO(3,new ValueVO(5820,0)),new SuitAttVO(2,new ValueVO(272676,0)),new SuitAttVO(0,new ValueVO(30123,0)),"6星神临套装");
         this._heros[this._heros.length] = new BaseSuitVO(1137,[20157,21157,22157,23157,24157,25157],new SuitAttVO(3,new ValueVO(7125,0)),new SuitAttVO(2,new ValueVO(279040,0)),new SuitAttVO(0,new ValueVO(23150,0)),"6星神兵套装");
         this._heros[this._heros.length] = new BaseSuitVO(1138,[20257,21257,22257,23257,24257,25257],new SuitAttVO(3,new ValueVO(6475,0)),new SuitAttVO(2,new ValueVO(249301,0)),new SuitAttVO(0,new ValueVO(29817,0)),"6星神斗套装");
         this._heros[this._heros.length] = new BaseSuitVO(1139,[20357,21357,22357,23357,24357,25357],new SuitAttVO(3,new ValueVO(6475,0)),new SuitAttVO(2,new ValueVO(245407,0)),new SuitAttVO(0,new ValueVO(29137,0)),"6星神者套装");
         this._heros[this._heros.length] = new BaseSuitVO(1140,[20457,21457,22457,23457,24457,25457],new SuitAttVO(3,new ValueVO(5180,0)),new SuitAttVO(2,new ValueVO(233721,0)),new SuitAttVO(0,new ValueVO(31285,0)),"6星神皆套装");
         this._heros[this._heros.length] = new BaseSuitVO(1141,[20058,21058,22058,23058,24058,25058],new SuitAttVO(3,new ValueVO(7275,0)),new SuitAttVO(2,new ValueVO(354479,0)),new SuitAttVO(0,new ValueVO(43679,0)),"1星圣临套装");
         this._heros[this._heros.length] = new BaseSuitVO(1142,[20158,21158,22158,23158,24158,25158],new SuitAttVO(3,new ValueVO(8906,0)),new SuitAttVO(2,new ValueVO(356752,0)),new SuitAttVO(0,new ValueVO(45117,0)),"1星圣兵套装");
         this._heros[this._heros.length] = new BaseSuitVO(1143,[20258,21258,22258,23258,24258,25258],new SuitAttVO(3,new ValueVO(8093,0)),new SuitAttVO(2,new ValueVO(324091,0)),new SuitAttVO(0,new ValueVO(43235,0)),"1星圣斗套装");
         this._heros[this._heros.length] = new BaseSuitVO(1144,[20358,21358,22358,23358,24358,25358],new SuitAttVO(3,new ValueVO(8093,0)),new SuitAttVO(2,new ValueVO(319029,0)),new SuitAttVO(0,new ValueVO(42249,0)),"1星圣者套装");
         this._heros[this._heros.length] = new BaseSuitVO(1145,[20458,21458,22458,23458,24458,25458],new SuitAttVO(3,new ValueVO(6475,0)),new SuitAttVO(2,new ValueVO(303838,0)),new SuitAttVO(0,new ValueVO(45363,0)),"1星圣皆套装");
         this._heros[this._heros.length] = new BaseSuitVO(1146,[20059,21059,22059,23059,24059,25059],new SuitAttVO(3,new ValueVO(9093,0)),new SuitAttVO(2,new ValueVO(460822,0)),new SuitAttVO(0,new ValueVO(63334,0)),"2星圣临套装");
         this._heros[this._heros.length] = new BaseSuitVO(1147,[20159,21159,22159,23159,24159,25159],new SuitAttVO(3,new ValueVO(11132,0)),new SuitAttVO(2,new ValueVO(467777,0)),new SuitAttVO(0,new ValueVO(65184,0)),"2星圣兵套装");
         this._heros[this._heros.length] = new BaseSuitVO(1148,[20259,21259,22259,23259,24259,25259],new SuitAttVO(3,new ValueVO(10116,0)),new SuitAttVO(2,new ValueVO(421318,0)),new SuitAttVO(0,new ValueVO(62690,0)),"2星圣斗套装");
         this._heros[this._heros.length] = new BaseSuitVO(1149,[20359,21359,22359,23359,24359,25359],new SuitAttVO(3,new ValueVO(10116,0)),new SuitAttVO(2,new ValueVO(414737,0)),new SuitAttVO(0,new ValueVO(61261,0)),"2星圣者套装");
         this._heros[this._heros.length] = new BaseSuitVO(1150,[20459,21459,22459,23459,24459,25459],new SuitAttVO(3,new ValueVO(8093,0)),new SuitAttVO(2,new ValueVO(394989,0)),new SuitAttVO(0,new ValueVO(65776,0)),"2星圣皆套装");
         this._heros[this._heros.length] = new BaseSuitVO(1151,[20060,21060,22060,23060,24060,25060],new SuitAttVO(3,new ValueVO(11367,0)),new SuitAttVO(2,new ValueVO(599069,0)),new SuitAttVO(0,new ValueVO(91836,0)),"3星圣临套装");
         this._heros[this._heros.length] = new BaseSuitVO(1152,[20160,21160,22160,23160,24160,25160],new SuitAttVO(3,new ValueVO(13916,0)),new SuitAttVO(2,new ValueVO(569112,0)),new SuitAttVO(0,new ValueVO(85819,0)),"3星圣兵套装");
         this._heros[this._heros.length] = new BaseSuitVO(1153,[20260,21260,22260,23260,24260,25260],new SuitAttVO(3,new ValueVO(12646,0)),new SuitAttVO(2,new ValueVO(547714,0)),new SuitAttVO(0,new ValueVO(90903,0)),"3星圣斗套装");
         this._heros[this._heros.length] = new BaseSuitVO(1154,[20360,21360,22360,23360,24360,25360],new SuitAttVO(3,new ValueVO(12646,0)),new SuitAttVO(2,new ValueVO(539160,0)),new SuitAttVO(0,new ValueVO(88830,0)),"3星圣者套装");
         this._heros[this._heros.length] = new BaseSuitVO(1155,[20460,21460,22460,23460,24460,25460],new SuitAttVO(3,new ValueVO(10117,0)),new SuitAttVO(2,new ValueVO(513486,0)),new SuitAttVO(0,new ValueVO(95376,0)),"3星圣皆套装");
         this._heros[this._heros.length] = new BaseSuitVO(1156,[20061,21061,22061,23061,24061,25061],new SuitAttVO(3,new ValueVO(14208,0)),new SuitAttVO(2,new ValueVO(748837,0)),new SuitAttVO(0,new ValueVO(114795,0)),"4星圣临套装");
         this._heros[this._heros.length] = new BaseSuitVO(1157,[20161,21161,22161,23161,24161,25161],new SuitAttVO(3,new ValueVO(17395,0)),new SuitAttVO(2,new ValueVO(711390,0)),new SuitAttVO(0,new ValueVO(107274,0)),"4星圣兵套装");
         this._heros[this._heros.length] = new BaseSuitVO(1158,[20261,21261,22261,23261,24261,25261],new SuitAttVO(3,new ValueVO(15808,0)),new SuitAttVO(2,new ValueVO(684642,0)),new SuitAttVO(0,new ValueVO(113629,0)),"4星圣斗套装");
         this._heros[this._heros.length] = new BaseSuitVO(1159,[20361,21361,22361,23361,24361,25361],new SuitAttVO(3,new ValueVO(15808,0)),new SuitAttVO(2,new ValueVO(673950,0)),new SuitAttVO(0,new ValueVO(111037,0)),"4星圣者套装");
         this._heros[this._heros.length] = new BaseSuitVO(1160,[20461,21461,22461,23461,24461,25461],new SuitAttVO(3,new ValueVO(12646,0)),new SuitAttVO(2,new ValueVO(641858,0)),new SuitAttVO(0,new ValueVO(119221,0)),"4星圣皆套装");
         this._pets = new Vector.<PetSuitVO>();
         this._pets[this._pets.length] = new PetSuitVO(2001,[80001,80002,80003],new SuitAttVO(2,new ValueVO(6000,0)),new SuitAttVO(0,new ValueVO(300,0)),"虎啸套装");
         this._pets[this._pets.length] = new PetSuitVO(2002,[80101,80102,80103],new SuitAttVO(2,new ValueVO(5000,0)),new SuitAttVO(0,new ValueVO(380,0)),"青龙套装");
         this._pets[this._pets.length] = new PetSuitVO(2003,[80201,80202,80203],new SuitAttVO(2,new ValueVO(10000,0)),new SuitAttVO(0,new ValueVO(400,0)),"玄武套装");
         this._pets[this._pets.length] = new PetSuitVO(2004,[80301,80302,80303],new SuitAttVO(2,new ValueVO(8000,0)),new SuitAttVO(0,new ValueVO(450,0)),"朱雀套装");
         this._pets[this._pets.length] = new PetSuitVO(2005,[80401,80402,80403],new SuitAttVO(2,new ValueVO(15000,0)),new SuitAttVO(0,new ValueVO(550,0)),"火麒麟套装");
         this._pets[this._pets.length] = new PetSuitVO(2011,[81001,81002,81003],new SuitAttVO(2,new ValueVO(18000,0)),new SuitAttVO(0,new ValueVO(600,0)),"虎啸1星套装");
         this._pets[this._pets.length] = new PetSuitVO(2012,[81101,81102,81103],new SuitAttVO(2,new ValueVO(10000,0)),new SuitAttVO(0,new ValueVO(760,0)),"青龙1星套装");
         this._pets[this._pets.length] = new PetSuitVO(2013,[81201,81202,81203],new SuitAttVO(2,new ValueVO(20000,0)),new SuitAttVO(0,new ValueVO(800,0)),"玄武1星套装");
         this._pets[this._pets.length] = new PetSuitVO(2014,[81301,81302,81303],new SuitAttVO(2,new ValueVO(16000,0)),new SuitAttVO(0,new ValueVO(900,0)),"朱雀1星套装");
         this._pets[this._pets.length] = new PetSuitVO(2015,[81401,81402,81403],new SuitAttVO(2,new ValueVO(40000,0)),new SuitAttVO(0,new ValueVO(2132,0)),"火麒麟1星套装");
         this._pets[this._pets.length] = new PetSuitVO(2016,[81501,81502,81503],new SuitAttVO(2,new ValueVO(50000,0)),new SuitAttVO(0,new ValueVO(2666,0)),"白泽1星套装");
         this._pets[this._pets.length] = new PetSuitVO(2021,[82001,82002,82003],new SuitAttVO(2,new ValueVO(36000,0)),new SuitAttVO(0,new ValueVO(1200,0)),"虎啸2星套装");
         this._pets[this._pets.length] = new PetSuitVO(2022,[82101,82102,82103],new SuitAttVO(2,new ValueVO(30000,0)),new SuitAttVO(0,new ValueVO(1520,0)),"青龙2星套装");
         this._pets[this._pets.length] = new PetSuitVO(2023,[82201,82202,82203],new SuitAttVO(2,new ValueVO(40000,0)),new SuitAttVO(0,new ValueVO(1600,0)),"玄武2星套装");
         this._pets[this._pets.length] = new PetSuitVO(2024,[82301,82302,82303],new SuitAttVO(2,new ValueVO(32000,0)),new SuitAttVO(0,new ValueVO(1800,0)),"朱雀2星套装");
         this._pets[this._pets.length] = new PetSuitVO(2025,[82401,82402,82403],new SuitAttVO(2,new ValueVO(80000,0)),new SuitAttVO(0,new ValueVO(3110,0)),"火麒麟2星套装");
         this._pets[this._pets.length] = new PetSuitVO(2026,[82501,82502,82503],new SuitAttVO(2,new ValueVO(100000,0)),new SuitAttVO(0,new ValueVO(3888,0)),"白泽2星套装");
         this._pets[this._pets.length] = new PetSuitVO(2031,[83001,83002,83003],new SuitAttVO(2,new ValueVO(54000,0)),new SuitAttVO(0,new ValueVO(1800,0)),"虎啸3星套装");
         this._pets[this._pets.length] = new PetSuitVO(2032,[83101,83102,83103],new SuitAttVO(2,new ValueVO(40000,0)),new SuitAttVO(0,new ValueVO(2220,0)),"青龙3星套装");
         this._pets[this._pets.length] = new PetSuitVO(2033,[83201,83202,83203],new SuitAttVO(2,new ValueVO(60000,0)),new SuitAttVO(0,new ValueVO(2400,0)),"玄武3星套装");
         this._pets[this._pets.length] = new PetSuitVO(2034,[83301,83302,83303],new SuitAttVO(2,new ValueVO(48000,0)),new SuitAttVO(0,new ValueVO(2700,0)),"朱雀3星套装");
         this._pets[this._pets.length] = new PetSuitVO(2035,[83401,83402,83403],new SuitAttVO(2,new ValueVO(120000,0)),new SuitAttVO(0,new ValueVO(5243,0)),"火麒麟3星套装");
         this._pets[this._pets.length] = new PetSuitVO(2036,[83501,83502,83503],new SuitAttVO(2,new ValueVO(150000,0)),new SuitAttVO(0,new ValueVO(6554,0)),"白泽3星套装");
         this._pets[this._pets.length] = new PetSuitVO(2041,[84001,84002,84003],new SuitAttVO(2,new ValueVO(81000,0)),new SuitAttVO(0,new ValueVO(2700,0)),"虎啸4星套装");
         this._pets[this._pets.length] = new PetSuitVO(2042,[84101,84102,84103],new SuitAttVO(2,new ValueVO(60000,0)),new SuitAttVO(0,new ValueVO(3330,0)),"青龙4星套装");
         this._pets[this._pets.length] = new PetSuitVO(2043,[84201,84202,84203],new SuitAttVO(2,new ValueVO(90000,0)),new SuitAttVO(0,new ValueVO(3600,0)),"玄武4星套装");
         this._pets[this._pets.length] = new PetSuitVO(2044,[84301,84302,84303],new SuitAttVO(2,new ValueVO(72000,0)),new SuitAttVO(0,new ValueVO(4050,0)),"朱雀4星套装");
         this._pets[this._pets.length] = new PetSuitVO(2045,[84401,84402,84403],new SuitAttVO(2,new ValueVO(180000,0)),new SuitAttVO(0,new ValueVO(7864,0)),"火麒麟4星套装");
         this._pets[this._pets.length] = new PetSuitVO(2046,[84501,84502,84503],new SuitAttVO(2,new ValueVO(225000,0)),new SuitAttVO(0,new ValueVO(9831,0)),"白泽4星套装");
         this._pets[this._pets.length] = new PetSuitVO(2051,[85001,85002,85003],new SuitAttVO(2,new ValueVO(121000,0)),new SuitAttVO(0,new ValueVO(4050,0)),"虎啸5星套装");
         this._pets[this._pets.length] = new PetSuitVO(2052,[85101,85102,85103],new SuitAttVO(2,new ValueVO(110000,0)),new SuitAttVO(0,new ValueVO(4995,0)),"青龙5星套装");
         this._pets[this._pets.length] = new PetSuitVO(2053,[85201,85202,85203],new SuitAttVO(2,new ValueVO(135000,0)),new SuitAttVO(0,new ValueVO(5400,0)),"玄武5星套装");
         this._pets[this._pets.length] = new PetSuitVO(2054,[85301,85302,85303],new SuitAttVO(2,new ValueVO(108000,0)),new SuitAttVO(0,new ValueVO(6075,0)),"朱雀5星套装");
         this._pets[this._pets.length] = new PetSuitVO(2055,[85401,85402,85403],new SuitAttVO(2,new ValueVO(270000,0)),new SuitAttVO(0,new ValueVO(10233,0)),"火麒麟5星套装");
         this._pets[this._pets.length] = new PetSuitVO(2056,[85501,85502,85503],new SuitAttVO(2,new ValueVO(337000,0)),new SuitAttVO(0,new ValueVO(12780,0)),"白泽5星套装");
         this._pets[this._pets.length] = new PetSuitVO(2061,[89001,89002,89003],new SuitAttVO(2,new ValueVO(180000,0)),new SuitAttVO(0,new ValueVO(8010,0)),"虎啸1星套神装");
         this._pets[this._pets.length] = new PetSuitVO(2062,[89101,89102,89103],new SuitAttVO(2,new ValueVO(200000,0)),new SuitAttVO(0,new ValueVO(9980,0)),"青龙1星套神装");
         this._pets[this._pets.length] = new PetSuitVO(2063,[89201,89202,89203],new SuitAttVO(2,new ValueVO(202500,0)),new SuitAttVO(0,new ValueVO(10800,0)),"玄武1星套神装");
         this._pets[this._pets.length] = new PetSuitVO(2064,[89301,89302,89303],new SuitAttVO(2,new ValueVO(162000,0)),new SuitAttVO(0,new ValueVO(12150,0)),"朱雀1星套神装");
         this._pets[this._pets.length] = new PetSuitVO(2065,[89401,89402,89403],new SuitAttVO(2,new ValueVO(405000,0)),new SuitAttVO(0,new ValueVO(20466,0)),"火麒麟1星套神装");
         this._pets[this._pets.length] = new PetSuitVO(2066,[89501,89502,89503],new SuitAttVO(2,new ValueVO(505000,0)),new SuitAttVO(0,new ValueVO(25560,0)),"白泽1星套神装");
         this._pets[this._pets.length] = new PetSuitVO(2071,[89011,89012,89013],new SuitAttVO(2,new ValueVO(360000,0)),new SuitAttVO(0,new ValueVO(16020,0)),"虎啸2星套神装");
         this._pets[this._pets.length] = new PetSuitVO(2072,[89111,89112,89113],new SuitAttVO(2,new ValueVO(400000,0)),new SuitAttVO(0,new ValueVO(20060,0)),"青龙2星套神装");
         this._pets[this._pets.length] = new PetSuitVO(2073,[89211,89212,89213],new SuitAttVO(2,new ValueVO(450000,0)),new SuitAttVO(0,new ValueVO(21600,0)),"玄武2星套神装");
         this._pets[this._pets.length] = new PetSuitVO(2074,[89311,89312,89313],new SuitAttVO(2,new ValueVO(322000,0)),new SuitAttVO(0,new ValueVO(24300,0)),"朱雀2星套神装");
         this._pets[this._pets.length] = new PetSuitVO(2075,[89411,89412,89413],new SuitAttVO(2,new ValueVO(810000,0)),new SuitAttVO(0,new ValueVO(40832,0)),"火麒麟2星套神装");
         this._pets[this._pets.length] = new PetSuitVO(2076,[89511,89512,89513],new SuitAttVO(2,new ValueVO(1010000,0)),new SuitAttVO(0,new ValueVO(50120,0)),"白泽2星套神装");
         this._pets[this._pets.length] = new PetSuitVO(2081,[89021,89022,89023],new SuitAttVO(2,new ValueVO(720000,0)),new SuitAttVO(0,new ValueVO(32040,0)),"虎啸3星套神装");
         this._pets[this._pets.length] = new PetSuitVO(2082,[89121,89122,89123],new SuitAttVO(2,new ValueVO(800000,0)),new SuitAttVO(0,new ValueVO(40120,0)),"青龙3星套神装");
         this._pets[this._pets.length] = new PetSuitVO(2083,[89221,89222,89223],new SuitAttVO(2,new ValueVO(900000,0)),new SuitAttVO(0,new ValueVO(43200,0)),"玄武3星套神装");
         this._pets[this._pets.length] = new PetSuitVO(2084,[89321,89322,89323],new SuitAttVO(2,new ValueVO(644000,0)),new SuitAttVO(0,new ValueVO(48600,0)),"朱雀3星套神装");
         this._pets[this._pets.length] = new PetSuitVO(2085,[89421,89422,89423],new SuitAttVO(2,new ValueVO(1620000,0)),new SuitAttVO(0,new ValueVO(81664,0)),"火麒麟3星套神装");
         this._pets[this._pets.length] = new PetSuitVO(2086,[89521,89522,89523],new SuitAttVO(2,new ValueVO(2020000,0)),new SuitAttVO(0,new ValueVO(100240,0)),"白泽3星套神装");
      }
      
      public function findSuit(param1:int) : BaseSuitVO
      {
         var _loc2_:BaseSuitVO = null;
         for each(_loc2_ in this._heros)
         {
            if(_loc2_.suitID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findPetSuit(param1:int) : PetSuitVO
      {
         var _loc2_:PetSuitVO = null;
         for each(_loc2_ in this._pets)
         {
            if(_loc2_.suitID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

