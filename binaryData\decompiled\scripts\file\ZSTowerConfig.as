package file
{
   import com.mogames.utils.MathUtil;
   import mogames.ConstData;
   import mogames.gameData.base.func.ZSTowerVO;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class ZSTowerConfig
   {
      
      private static var _instance:ZSTowerConfig;
      
      public var activeVO:ZSTowerVO;
      
      public var openIndex:int;
      
      public var waves:Array;
      
      private var _args:Array;
      
      private var _list:Array;
      
      public function ZSTowerConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : ZSTowerConfig
      {
         if(!_instance)
         {
            _instance = new ZSTowerConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.openIndex = 10;
         this._list = [];
         this._list[this._list.length] = new ZSTowerVO(0,[new BaseRewardVO(10701,1),new BaseRewardVO(10711,1),new BaseRewardVO(10712,1)]);
         this._list[this._list.length] = new ZSTowerVO(1,[new BaseRewardVO(10702,1),new BaseRewardVO(10713,1),new BaseRewardVO(10714,1)]);
         this._list[this._list.length] = new ZSTowerVO(2,[new BaseRewardVO(10703,1),new BaseRewardVO(10718,1),new BaseRewardVO(10719,1)]);
         this._list[this._list.length] = new ZSTowerVO(3,[new BaseRewardVO(10704,1),new BaseRewardVO(10720,1),new BaseRewardVO(10721,1)]);
         this._list[this._list.length] = new ZSTowerVO(4,[new BaseRewardVO(10705,1),new BaseRewardVO(10736,1),new BaseRewardVO(10737,1)]);
         this._list[this._list.length] = new ZSTowerVO(5,[new BaseRewardVO(10706,1),new BaseRewardVO(10746,1),new BaseRewardVO(10747,1)]);
         this._list[this._list.length] = new ZSTowerVO(6,[new BaseRewardVO(10707,1),new BaseRewardVO(10756,1),new BaseRewardVO(10757,1)]);
         this._list[this._list.length] = new ZSTowerVO(7,[new BaseRewardVO(10708,1),new BaseRewardVO(10766,1),new BaseRewardVO(10767,1)]);
         this._list[this._list.length] = new ZSTowerVO(8,[new BaseRewardVO(10709,1),new BaseRewardVO(10776,1),new BaseRewardVO(10777,1)]);
         this._list[this._list.length] = new ZSTowerVO(9,[new BaseRewardVO(10710,1),new BaseRewardVO(10778,1),new BaseRewardVO(10779,1)]);
         this.waves = [];
         this.waves[this.waves.length] = {
            "time":1,
            "num":3
         };
         this.waves[this.waves.length] = {
            "time":8,
            "num":3
         };
         this.waves[this.waves.length] = {
            "time":10,
            "num":4
         };
         this.waves[this.waves.length] = {
            "time":12,
            "num":4
         };
         this.waves[this.waves.length] = {
            "time":14,
            "num":5
         };
         this.waves[this.waves.length] = {
            "time":16,
            "num":5
         };
         this.waves[this.waves.length] = {
            "time":18,
            "num":6
         };
         this.waves[this.waves.length] = {
            "time":20,
            "num":6
         };
         this.waves[this.waves.length] = {
            "time":22,
            "num":7
         };
         this.waves[this.waves.length] = {
            "time":24,
            "num":8
         };
         this._args = [];
         this._args[0] = [new BossArgVO(308,12000,600,350,30,30,150,150,new BossSkillData0(100,{
            "hurt":2000,
            "keepTime":3
         },2),1007,0),new BossArgVO(353,12000,700,350,30,30,150,150,new BossSkillData0(100,{"hurt":2500},2),1011,0),new BossArgVO(354,12000,800,350,30,30,150,150,new BossSkillData0(100,{"hurt":2000},2),1010,0),new BossArgVO(355,12000,900,350,30,30,150,150,new BossSkillData0(100,{
            "hurt":2500,
            "roleNum":20
         },2),1005,0),new BossArgVO(356,14000,1000,450,60,60,200,150,new BossSkillData1(10,{
            "hurt":2500,
            "roleNum":3
         },3),1026,0),new BossArgVO(351,14000,1100,450,60,60,200,150,new BossSkillData1(11,{
            "hurt":1000,
            "keepTime":5,
            "hurtCount":5
         },3),1001,0),new BossArgVO(352,14000,1200,450,60,60,200,150,new BossSkillData1(12,{
            "hurt":3000,
            "hurtCount":5
         },3),1002,0),new BossArgVO(378,14000,1300,450,60,60,200,150,new BossSkillData1(13,{
            "hurt":3000,
            "beiPer":600
         },3),1029,0)];
         this._args[1] = [new BossArgVO(363,16000,1000,450,40,40,150,150,new BossSkillData0(100,{
            "hurt":2000,
            "roleNum":4
         },2),1004,0),new BossArgVO(372,16000,1100,450,40,40,150,150,new BossSkillData0(100,{"hurt":2500},2),1012,0),new BossArgVO(375,16000,1200,450,40,40,150,150,new BossSkillData0(100,{
            "hurt":3500,
            "hurtCount":3
         },2),1017,0),new BossArgVO(380,16000,1300,450,40,40,150,150,new BossSkillData0(100,{"hurt":3500},2),1018,0),new BossArgVO(404,18000,1400,550,70,70,200,150,new BossSkillData1(10,{
            "hurt":3000,
            "spdPer":80,
            "keepTime":5
         },3),1031,0),new BossArgVO(412,18000,1500,550,70,70,200,150,new BossSkillData1(11,{
            "hurt":4000,
            "roleNum":25,
            "keepTime":2
         },3),1036,0),new BossArgVO(422,18000,1600,550,70,70,200,150,new BossSkillData1(12,{
            "hurt":3000,
            "roleNum":20,
            "defPer":5,
            "keepTime":5
         },3),1039,0),new BossArgVO(432,18000,1700,550,70,70,200,150,new BossSkillData1(13,{
            "hurt":4000,
            "hpRate":25,
            "hpPer":20
         },3),1038,0)];
         this._args[2] = [new BossArgVO(458,20000,1400,550,50,50,150,150,new BossSkillData0(100,{
            "hurt":2500,
            "defPer":30,
            "keepTime":4
         },2),1044,0),new BossArgVO(479,20000,1500,550,50,50,150,150,new BossSkillData0(100,{
            "keepTime":10,
            "curePer":10
         },2),1045,0),new BossArgVO(454,20000,1600,550,50,50,150,150,new BossSkillData0(100,{
            "hurt":4000,
            "killPer":1000
         },2),1042,0),new BossArgVO(457,20000,1700,550,50,50,150,150,new BossSkillData0(100,{
            "hurt":4000,
            "keepTime":5
         },2),1043,0),new BossArgVO(456,22000,1800,600,80,80,200,150,new BossSkillData1(10,{
            "hurt":4000,
            "keepTime":5
         },3),1041,0),new BossArgVO(480,22000,1900,600,80,80,200,150,new BossSkillData1(11,{
            "hurt":4500,
            "hurtPer":20,
            "keepTime":3
         },3),1046,0),new BossArgVO(433,22000,2000,600,80,80,200,150,new BossSkillData1(12,{
            "hurt":4500,
            "loseHurt":80,
            "roleNum":10,
            "keepTime":8
         },3),1037,0),new BossArgVO(455,22000,2100,600,80,80,200,150,new BossSkillData1(13,{"curePer":800},3),1040,0)];
         this._args[3] = [new BossArgVO(503,30000,2100,700,50,50,150,150,new BossSkillData0(100,{
            "hurt":5000,
            "curePer":80
         },2),1052,0),new BossArgVO(504,30000,2200,700,50,50,150,150,new BossSkillData0(100,{
            "hurt":5000,
            "hpPer":10,
            "loseHurt":60,
            "hpPer":6
         },2),1053,0),new BossArgVO(524,30000,2300,700,50,50,150,150,new BossSkillData0(100,{
            "hurt":5000,
            "critPer":30,
            "keepTime":5
         },2),1054,0),new BossArgVO(525,30000,2400,700,50,50,150,150,new BossSkillData0(100,{
            "hurt":5000,
            "cure":50
         },2),1055,0),new BossArgVO(481,35000,3100,850,80,80,200,150,new BossSkillData1(10,{
            "hurt":6500,
            "atkPer":50,
            "defPer":20,
            "keepTime":3
         },3),1048,0),new BossArgVO(527,35000,3200,850,80,80,200,150,new BossSkillData1(11,{
            "hurt":6500,
            "defPer":20,
            "keepTime":3
         },3),1057,0),new BossArgVO(528,35000,3300,850,80,80,200,150,new BossSkillData1(12,{
            "hurt":6500,
            "hpPer":20
         },3),1058,0),new BossArgVO(548,35000,3400,850,80,80,200,150,new BossSkillData1(13,{
            "hurt":6500,
            "keepTime":3
         },3),1047,0)];
         this._args[4] = [new BossArgVO(570,35000,2500,750,50,50,150,150,new BossSkillData0(100,{
            "hurt":6500,
            "wit":30
         },2),1061,0),new BossArgVO(571,35000,2600,750,50,50,150,150,new BossSkillData0(100,{
            "hurt":6500,
            "keepTime":2
         },2),1062,0),new BossArgVO(572,35000,2700,750,50,50,150,150,new BossSkillData0(100,{
            "hurt":6500,
            "atkPer":20,
            "keepTime":3
         },2),1063,0),new BossArgVO(594,35000,2800,750,50,50,150,150,new BossSkillData0(100,{"hurt":6500},2),1066,0),new BossArgVO(549,40000,3500,950,80,80,200,150,new BossSkillData1(10,{
            "hurt":9500,
            "hpRate":200,
            "hpPer":50
         },3),1059,0),new BossArgVO(550,40000,3600,950,80,80,200,150,new BossSkillData1(11,{
            "hurt":9500,
            "miss":300,
            "crit":300,
            "keepTime":3
         },3),1060,0),new BossArgVO(592,40000,3700,950,80,80,200,150,new BossSkillData1(12,{
            "hurt":9500,
            "atkPer":100,
            "keepTime":8
         },3),1064,0),new BossArgVO(593,40000,3800,950,80,80,200,150,new BossSkillData1(13,{
            "hurt":9500,
            "keepTime":3
         },3),1065,0)];
         this._args[5] = [new BossArgVO(594,45000,3500,800,50,50,150,150,new BossSkillData0(100,{"hurt":7500},2),1066,0),new BossArgVO(595,45000,3600,800,50,50,150,150,new BossSkillData0(100,{
            "hurt":7500,
            "baseMiss":100
         },2),1067,0),new BossArgVO(615,45000,3700,800,50,50,150,150,new BossSkillData0(100,{
            "hurt":7500,
            "hpRate":300,
            "hpPer":500
         },2),1068,0),new BossArgVO(616,45000,3800,800,50,50,150,150,new BossSkillData0(100,{
            "hurt":7500,
            "baseCrit":1000,
            "atkPer":20,
            "defPer":3
         },2),1069,0),new BossArgVO(617,60000,4500,1050,80,80,200,150,new BossSkillData1(10,{
            "hurt":10500,
            "defPer":500,
            "spdPer":500
         },3),1073,0),new BossArgVO(618,60000,4600,1050,80,80,200,150,new BossSkillData1(11,{"hurt":10500},3),1071,0),new BossArgVO(621,60000,4700,1050,80,80,200,150,new BossSkillData1(12,{
            "hurt":10500,
            "rebound":800,
            "keepTime":5
         },3),1074,0),new BossArgVO(622,60000,4800,1050,80,80,200,150,new BossSkillData1(13,{"hurt":10500},3),1075,0)];
         this._args[6] = [new BossArgVO(570,65000,4500,900,50,50,150,150,new BossSkillData0(100,{
            "hurt":8500,
            "wit":30
         },2),1061,0),new BossArgVO(571,65000,4600,900,50,50,150,150,new BossSkillData0(100,{
            "hurt":8500,
            "keepTime":2
         },2),1062,0),new BossArgVO(572,65000,4700,900,50,50,150,150,new BossSkillData0(100,{
            "hurt":8500,
            "atkPer":20,
            "keepTime":3
         },2),1063,0),new BossArgVO(594,65000,4800,900,50,50,150,150,new BossSkillData0(100,{"hurt":8500},2),1066,0),new BossArgVO(456,75000,5500,1250,80,80,200,150,new BossSkillData1(10,{
            "hurt":12000,
            "keepTime":5
         },3),1041,0),new BossArgVO(480,75000,5600,1250,80,80,200,150,new BossSkillData1(11,{
            "hurt":12000,
            "hurtPer":20,
            "keepTime":3
         },3),1046,0),new BossArgVO(433,75000,5700,1250,80,80,200,150,new BossSkillData1(12,{
            "hurt":12000,
            "loseHurt":80,
            "roleNum":10,
            "keepTime":8
         },3),1037,0),new BossArgVO(455,75000,5800,1250,80,80,200,150,new BossSkillData1(13,{"curePer":800},3),1040,0)];
         this._args[7] = [new BossArgVO(594,75000,5500,1250,50,50,150,150,new BossSkillData0(100,{"hurt":10500},2),1066,0),new BossArgVO(595,75000,5600,1250,50,50,150,150,new BossSkillData0(100,{
            "hurt":10500,
            "baseMiss":100
         },2),1067,0),new BossArgVO(615,75000,5700,1250,50,50,150,150,new BossSkillData0(100,{
            "hurt":10500,
            "hpRate":300,
            "hpPer":500
         },2),1068,0),new BossArgVO(616,75000,5800,1250,50,50,150,150,new BossSkillData0(100,{
            "hurt":10500,
            "baseCrit":1000,
            "atkPer":20,
            "defPer":3
         },2),1069,0),new BossArgVO(617,100000,7500,1450,80,80,200,150,new BossSkillData1(10,{
            "hurt":14500,
            "defPer":500,
            "spdPer":500
         },3),1073,0),new BossArgVO(618,100000,7600,1450,80,80,200,150,new BossSkillData1(11,{"hurt":14500},3),1071,0),new BossArgVO(621,100000,7700,1450,80,80,200,150,new BossSkillData1(12,{
            "hurt":14500,
            "rebound":800,
            "keepTime":5
         },3),1074,0),new BossArgVO(622,100000,7800,1450,80,80,200,150,new BossSkillData1(13,{"hurt":14500},3),1075,0)];
         this._args[8] = [new BossArgVO(570,85000,6500,900,50,50,150,150,new BossSkillData0(100,{
            "hurt":13500,
            "wit":30
         },2),1061,0),new BossArgVO(571,85000,6600,900,50,50,150,150,new BossSkillData0(100,{
            "hurt":13500,
            "keepTime":2
         },2),1062,0),new BossArgVO(572,85000,6700,900,50,50,150,150,new BossSkillData0(100,{
            "hurt":13500,
            "atkPer":20,
            "keepTime":3
         },2),1063,0),new BossArgVO(594,85000,6800,900,50,50,150,150,new BossSkillData0(100,{"hurt":13500},2),1066,0),new BossArgVO(456,125000,8500,1250,80,80,200,150,new BossSkillData1(10,{
            "hurt":16000,
            "keepTime":5
         },3),1041,0),new BossArgVO(480,125000,8600,1250,80,80,200,150,new BossSkillData1(11,{
            "hurt":16000,
            "hurtPer":20,
            "keepTime":3
         },3),1046,0),new BossArgVO(433,125000,8700,1250,80,80,200,150,new BossSkillData1(12,{
            "hurt":16000,
            "loseHurt":80,
            "roleNum":10,
            "keepTime":8
         },3),1037,0),new BossArgVO(593,125000,8800,950,80,80,200,150,new BossSkillData1(13,{
            "hurt":15500,
            "keepTime":3
         },3),1065,0)];
         this._args[9] = [new BossArgVO(594,125000,9500,800,50,50,150,150,new BossSkillData0(100,{"hurt":27500},2),1066,0),new BossArgVO(595,125000,9600,800,50,50,150,150,new BossSkillData0(100,{
            "hurt":27500,
            "baseMiss":100
         },2),1067,0),new BossArgVO(615,125000,9700,800,50,50,150,150,new BossSkillData0(100,{
            "hurt":27500,
            "hpRate":300,
            "hpPer":500
         },2),1068,0),new BossArgVO(616,125000,9800,800,50,50,150,150,new BossSkillData0(100,{
            "hurt":27500,
            "baseCrit":1000,
            "atkPer":20,
            "defPer":3
         },2),1069,0),new BossArgVO(617,160000,11500,1050,80,80,200,150,new BossSkillData1(10,{
            "hurt":30500,
            "defPer":500,
            "spdPer":500
         },3),1073,0),new BossArgVO(618,160000,11600,1050,80,80,200,150,new BossSkillData1(11,{"hurt":30500},3),1071,0),new BossArgVO(621,160000,11700,1050,80,80,200,150,new BossSkillData1(12,{
            "hurt":30500,
            "rebound":800,
            "keepTime":5
         },3),1074,0),new BossArgVO(622,160000,11800,1050,80,80,200,150,new BossSkillData1(13,{"hurt":30500},3),1075,0)];
      }
      
      public function newWaves(param1:int) : Array
      {
         var _loc5_:int = 0;
         var _loc2_:int = int(this.waves[param1].num);
         var _loc3_:Array = this._args[this.activeVO.index].slice();
         var _loc4_:Array = [];
         var _loc6_:int = 0;
         while(_loc6_ < _loc2_)
         {
            _loc5_ = Math.random() * _loc3_.length;
            _loc4_[_loc6_] = _loc3_[_loc5_];
            _loc3_.splice(_loc5_,1);
            _loc6_++;
         }
         return _loc4_;
      }
      
      public function get list() : Array
      {
         return this._list;
      }
      
      public function newReward(param1:Boolean) : Array
      {
         if(!param1)
         {
            return this.loseReward;
         }
         return this.winReward;
      }
      
      public function get enterTip() : String
      {
         if(this.isOpen)
         {
            return "";
         }
         return "开启条件：主公等级达到70级";
      }
      
      public function get isOpen() : Boolean
      {
         if(Sanx.isLocal)
         {
            return true;
         }
         return MasterProxy.instance().masterVO.level >= 70;
      }
      
      private function get winReward() : Array
      {
         var _loc4_:int = 0;
         var _loc1_:Array = [];
         var _loc2_:int = ConstData.INT2.v;
         if(MathUtil.checkOdds(900))
         {
            _loc2_ = ConstData.INT3.v;
         }
         var _loc3_:Array = this.activeVO.rewards.slice();
         var _loc5_:int = 0;
         while(_loc5_ < _loc2_)
         {
            _loc4_ = Math.random() * _loc3_.length;
            _loc1_[_loc1_.length] = _loc3_[_loc4_];
            _loc3_.splice(_loc4_,1);
            _loc5_++;
         }
         return _loc1_;
      }
      
      private function get loseReward() : Array
      {
         var _loc1_:int = Math.random() * this.activeVO.rewards.length;
         return [this.activeVO.rewards[_loc1_]];
      }
   }
}

