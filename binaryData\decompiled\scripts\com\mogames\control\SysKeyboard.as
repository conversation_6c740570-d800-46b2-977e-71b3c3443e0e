package com.mogames.control
{
   import com.mogames.data.FuncVector;
   import flash.events.KeyboardEvent;
   import flash.utils.Dictionary;
   import mogames.Layers;
   
   public class SysKeyboard
   {
      
      private var _dic:Dictionary;
      
      private var _active:Boolean;
      
      private var _downFuncs:FuncVector;
      
      private var _upFuncs:FuncVector;
      
      public function SysKeyboard()
      {
         super();
         this._dic = new Dictionary();
         this._downFuncs = new FuncVector();
         this._upFuncs = new FuncVector();
         this.start();
      }
      
      public function start() : void
      {
         this._active = true;
         Layers.gameStage.addEventListener(KeyboardEvent.KEY_DOWN,this.onKeyDown);
         Layers.gameStage.addEventListener(KeyboardEvent.KEY_UP,this.onKeyUp);
      }
      
      public function stop() : void
      {
         this._active = false;
         this.cleanKey();
         Layers.gameStage.removeEventListener(KeyboardEvent.KEY_DOWN,this.onKeyDown);
         Layers.gameStage.removeEventListener(KeyboardEvent.KEY_UP,this.onKeyUp);
      }
      
      public function isKeyDown(param1:int) : Boolean
      {
         if(!this._active)
         {
            return false;
         }
         return this._dic[param1];
      }
      
      public function cleanKey() : void
      {
         var _loc1_:String = null;
         for(_loc1_ in this._dic)
         {
            delete this._dic[_loc1_];
         }
      }
      
      public function get downFuncs() : FuncVector
      {
         return this._downFuncs;
      }
      
      public function get upFuncs() : FuncVector
      {
         return this._upFuncs;
      }
      
      private function onKeyDown(param1:KeyboardEvent) : void
      {
         var _loc2_:Function = null;
         if(!this._active)
         {
            return;
         }
         this._dic[param1.keyCode] = true;
         for each(_loc2_ in this._downFuncs.funcs)
         {
            _loc2_(param1.keyCode);
         }
      }
      
      private function onKeyUp(param1:KeyboardEvent) : void
      {
         var _loc2_:Function = null;
         if(!this._active)
         {
            return;
         }
         this._dic[param1.keyCode] = false;
         for each(_loc2_ in this._upFuncs.funcs)
         {
            _loc2_(param1.keyCode);
         }
      }
      
      public function cleanList() : void
      {
         this._downFuncs.clean();
         this._upFuncs.clean();
      }
   }
}

