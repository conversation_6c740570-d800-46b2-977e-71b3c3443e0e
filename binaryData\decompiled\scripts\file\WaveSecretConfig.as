package file
{
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.base.vo.EquipRewardVO;
   import mogames.gameData.base.vo.NeedVO;
   import mogames.gameData.mission.base.MissionBattleVO;
   import mogames.gameData.mission.secret.*;
   import mogames.gameData.mission.wave.WaveDataVO;
   
   public class WaveSecretConfig
   {
      
      private static var _instance:WaveSecretConfig;
      
      private var _dic:Vector.<WaveDataVO>;
      
      private var _battles:Vector.<MissionBattleVO>;
      
      public function WaveSecretConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.initWave();
         this.initBattle();
      }
      
      public static function instance() : WaveSecretConfig
      {
         if(!_instance)
         {
            _instance = new WaveSecretConfig();
         }
         return _instance;
      }
      
      private function initBattle() : void
      {
         this._battles = new Vector.<MissionBattleVO>();
         this._battles[this._battles.length] = new MissionBattleVO(2001,1036,1550,[new BaseRewardVO(10000,1300)],[],"scene/yzmj01.json");
         this._battles[this._battles.length] = new MissionBattleVO(2002,1142,1754,[new BaseRewardVO(10000,1400)],[],"scene/yzmj02.json");
         this._battles[this._battles.length] = new MissionBattleVO(2004,1248,1962,[new BaseRewardVO(10000,1500)],[],"scene/yzmj03.json");
         this._battles[this._battles.length] = new MissionBattleVO(2005,1811,1914,[new BaseRewardVO(10000,1600)],[],"scene/yzmj04.json");
         this._battles[this._battles.length] = new MissionBattleVO(2007,1355,2175,[new BaseRewardVO(10000,1700)],[],"scene/yzmj05.json");
         this._battles[this._battles.length] = new MissionBattleVO(2008,1462,2393,[new BaseRewardVO(10000,1800),new EquipRewardVO(25003,1)],[],"scene/yzmj06.json");
         this._battles[this._battles.length] = new MissionBattleVO(2010,1569,2613,[new BaseRewardVO(10000,1900),new EquipRewardVO(25103,1)],[],"scene/yzmj07.json");
         this._battles[this._battles.length] = new MissionBattleVO(2011,1677,2837,[new BaseRewardVO(10000,2000),new EquipRewardVO(25203,1)],[],"scene/yzmj08.json");
         this._battles[this._battles.length] = new MissionBattleVO(2013,1731,2950,[new BaseRewardVO(10000,2100),new EquipRewardVO(25303,1)],[],"scene/yzmj09.json");
         this._battles[this._battles.length] = new MissionBattleVO(2014,1784,3064,[new BaseRewardVO(10000,2200),new EquipRewardVO(25403,1)],[],"scene/yzmj10.json");
         this._battles[this._battles.length] = new MissionBattleVO(2016,1816,3107,[new BaseRewardVO(10000,2300),new BaseRewardVO(10404,1)],[new NeedVO(10000,500),new NeedVO(10003,10)],"scene/yzmj11-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2017,1848,3150,[new BaseRewardVO(10000,2300),new BaseRewardVO(10027,1)],[new NeedVO(10000,600),new NeedVO(10004,10)],"scene/yzmj12-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2018,1880,3193,[new BaseRewardVO(10000,2300),new BaseRewardVO(10301,1)],[new NeedVO(10000,700),new NeedVO(10005,10)],"scene/yzmj13-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2019,1912,3236,[new BaseRewardVO(10000,2300),new BaseRewardVO(10028,1)],[new NeedVO(10000,800),new NeedVO(10006,10)],"scene/yzmj14-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2020,1944,3279,[new BaseRewardVO(10000,2300),new BaseRewardVO(10404,1)],[new NeedVO(10000,900),new NeedVO(10007,10)],"scene/yzmj15-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2021,1976,3322,[new BaseRewardVO(10000,2300),new BaseRewardVO(10029,1)],[new NeedVO(10000,1000),new NeedVO(10003,20)],"scene/yzmj16-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2022,2008,3365,[new BaseRewardVO(10000,2300),new BaseRewardVO(10301,1)],[new NeedVO(10000,1100),new NeedVO(10004,20)],"scene/yzmj17-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2024,2040,3408,[new BaseRewardVO(10000,2300),new BaseRewardVO(10027,1)],[new NeedVO(10000,1200),new NeedVO(10005,20)],"scene/yzmj18-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2025,2072,3451,[new BaseRewardVO(10000,2300),new BaseRewardVO(10404,1)],[new NeedVO(10000,1300),new NeedVO(10006,20)],"scene/yzmj19-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2026,2104,3494,[new BaseRewardVO(10000,2300),new BaseRewardVO(10028,1)],[new NeedVO(10000,1400),new NeedVO(10007,20)],"scene/yzmj20-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2027,2136,3537,[new BaseRewardVO(10000,2300),new BaseRewardVO(10301,1)],[new NeedVO(10000,1500),new NeedVO(10003,30)],"scene/yzmj21-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2028,2168,3580,[new BaseRewardVO(10000,2300),new BaseRewardVO(10029,1)],[new NeedVO(10000,1600),new NeedVO(10004,30)],"scene/yzmj22-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2029,2200,3623,[new BaseRewardVO(10000,2300),new BaseRewardVO(10404,1)],[new NeedVO(10000,1700),new NeedVO(10005,30)],"scene/yzmj23-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2031,2232,2666,[new BaseRewardVO(10000,2500),new BaseRewardVO(10404,1)],[new NeedVO(10000,2000),new NeedVO(10006,30)],"scene/qzmj24.json");
         this._battles[this._battles.length] = new MissionBattleVO(2032,2264,3709,[new BaseRewardVO(10000,2500),new BaseRewardVO(10027,1)],[new NeedVO(10000,2000),new NeedVO(10007,30)],"scene/qzmj25.json");
         this._battles[this._battles.length] = new MissionBattleVO(2033,2296,3752,[new BaseRewardVO(10000,2500),new BaseRewardVO(10301,1)],[new NeedVO(10000,2000),new NeedVO(10003,35)],"scene/qzmj26.json");
         this._battles[this._battles.length] = new MissionBattleVO(2034,2328,3795,[new BaseRewardVO(10000,2500),new BaseRewardVO(10028,1)],[new NeedVO(10000,2000),new NeedVO(10004,35)],"scene/qzmj27.json");
         this._battles[this._battles.length] = new MissionBattleVO(2036,2360,3838,[new BaseRewardVO(10000,2500),new BaseRewardVO(10404,1)],[new NeedVO(10000,2000),new NeedVO(10005,35)],"scene/qzmj28.json");
         this._battles[this._battles.length] = new MissionBattleVO(2037,2392,3881,[new BaseRewardVO(10000,2500),new BaseRewardVO(10029,1)],[new NeedVO(10000,2000),new NeedVO(10006,35)],"scene/qzmj29.json");
         this._battles[this._battles.length] = new MissionBattleVO(2038,2424,3924,[new BaseRewardVO(10000,2500),new BaseRewardVO(10301,1)],[new NeedVO(10000,2000),new NeedVO(10007,35)],"scene/qzmj30.json");
         this._battles[this._battles.length] = new MissionBattleVO(2039,2456,3967,[new BaseRewardVO(10000,2500),new BaseRewardVO(10027,1)],[new NeedVO(10000,2000),new NeedVO(10003,40)],"scene/qzmj31.json");
         this._battles[this._battles.length] = new MissionBattleVO(2041,2488,4010,[new BaseRewardVO(10000,2500),new BaseRewardVO(10404,1)],[new NeedVO(10000,2000),new NeedVO(10004,40)],"scene/qzmj32.json");
         this._battles[this._battles.length] = new MissionBattleVO(2042,2520,4053,[new BaseRewardVO(10000,2500),new BaseRewardVO(10028,1)],[new NeedVO(10000,2000),new NeedVO(10005,40)],"scene/qzmj33.json");
         this._battles[this._battles.length] = new MissionBattleVO(2043,2552,4096,[new BaseRewardVO(10000,2500),new BaseRewardVO(10301,1)],[new NeedVO(10000,2000),new NeedVO(10006,40)],"scene/qzmj34.json");
         this._battles[this._battles.length] = new MissionBattleVO(2044,2584,4139,[new BaseRewardVO(10000,2500),new BaseRewardVO(10029,1)],[new NeedVO(10000,2000),new NeedVO(10007,40)],"scene/qzmj35.json");
         this._battles[this._battles.length] = new MissionBattleVO(2045,2616,4182,[new BaseRewardVO(10000,2500),new BaseRewardVO(10404,1)],[new NeedVO(10000,2000),new NeedVO(10003,45)],"scene/qzmj36.json");
         this._battles[this._battles.length] = new MissionBattleVO(2047,2642,4225,[new BaseRewardVO(10000,2700),new BaseRewardVO(10404,1)],[new NeedVO(10000,2500),new NeedVO(10003,45)],"scene/yzmj11-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2048,2671,4268,[new BaseRewardVO(10000,2700),new BaseRewardVO(10027,1)],[new NeedVO(10000,2500),new NeedVO(10004,45)],"scene/qzmj24.json");
         this._battles[this._battles.length] = new MissionBattleVO(2049,2700,4311,[new BaseRewardVO(10000,2700),new BaseRewardVO(10301,1)],[new NeedVO(10000,2500),new NeedVO(10005,45)],"scene/yzmj13-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2050,2729,4354,[new BaseRewardVO(10000,2700),new BaseRewardVO(10028,1)],[new NeedVO(10000,2500),new NeedVO(10006,45)],"scene/qzmj26.json");
         this._battles[this._battles.length] = new MissionBattleVO(2052,2758,4397,[new BaseRewardVO(10000,2700),new BaseRewardVO(10404,1)],[new NeedVO(10000,2500),new NeedVO(10007,45)],"scene/yzmj15-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2053,2787,4440,[new BaseRewardVO(10000,2700),new BaseRewardVO(10029,1)],[new NeedVO(10000,2500),new NeedVO(10003,50)],"scene/qzmj28.json");
         this._battles[this._battles.length] = new MissionBattleVO(2054,2816,4483,[new BaseRewardVO(10000,2700),new BaseRewardVO(10301,1)],[new NeedVO(10000,2500),new NeedVO(10004,50)],"scene/yzmj17-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2055,2845,4526,[new BaseRewardVO(10000,2700),new BaseRewardVO(10027,1)],[new NeedVO(10000,2500),new NeedVO(10005,50)],"scene/qzmj30.json");
         this._battles[this._battles.length] = new MissionBattleVO(2057,2874,4569,[new BaseRewardVO(10000,2700),new BaseRewardVO(10404,1)],[new NeedVO(10000,2500),new NeedVO(10006,50)],"scene/yzmj19-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2058,2903,4612,[new BaseRewardVO(10000,2700),new BaseRewardVO(10028,1)],[new NeedVO(10000,2500),new NeedVO(10007,50)],"scene/qzmj32.json");
         this._battles[this._battles.length] = new MissionBattleVO(2059,2932,4655,[new BaseRewardVO(10000,2700),new BaseRewardVO(10301,1)],[new NeedVO(10000,2500),new NeedVO(10003,50)],"scene/yzmj21-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2060,2961,4698,[new BaseRewardVO(10000,2700),new BaseRewardVO(10029,1)],[new NeedVO(10000,2500),new NeedVO(10004,50)],"scene/qzmj34.json");
         this._battles[this._battles.length] = new MissionBattleVO(2062,2990,4741,[new BaseRewardVO(10000,2800),new BaseRewardVO(10404,1)],[new NeedVO(10000,2700),new NeedVO(10003,52)],"scene/yzmj12-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2063,3019,4784,[new BaseRewardVO(10000,2800),new BaseRewardVO(10027,1)],[new NeedVO(10000,2700),new NeedVO(10004,52)],"scene/qzmj25.json");
         this._battles[this._battles.length] = new MissionBattleVO(2064,3048,4827,[new BaseRewardVO(10000,2800),new BaseRewardVO(10405,1)],[new NeedVO(10000,2700),new NeedVO(10005,52)],"scene/yzmj14-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2065,3077,4870,[new BaseRewardVO(10000,2800),new BaseRewardVO(10028,1)],[new NeedVO(10000,2700),new NeedVO(10006,52)],"scene/qzmj27.json");
         this._battles[this._battles.length] = new MissionBattleVO(2067,3106,4913,[new BaseRewardVO(10000,2800),new BaseRewardVO(10404,1)],[new NeedVO(10000,2700),new NeedVO(10007,52)],"scene/yzmj16-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2068,3135,4956,[new BaseRewardVO(10000,2800),new BaseRewardVO(10029,1)],[new NeedVO(10000,2700),new NeedVO(10003,52)],"scene/qzmj29.json");
         this._battles[this._battles.length] = new MissionBattleVO(2069,3164,5999,[new BaseRewardVO(10000,2800),new BaseRewardVO(10406,1)],[new NeedVO(10000,2700),new NeedVO(10004,52)],"scene/yzmj18-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2070,3193,5042,[new BaseRewardVO(10000,2800),new BaseRewardVO(10027,1)],[new NeedVO(10000,2700),new NeedVO(10005,52)],"scene/qzmj31.json");
         this._battles[this._battles.length] = new MissionBattleVO(2072,3222,5085,[new BaseRewardVO(10000,2800),new BaseRewardVO(10404,1)],[new NeedVO(10000,2700),new NeedVO(10006,52)],"scene/yzmj20-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2073,3251,5128,[new BaseRewardVO(10000,2800),new BaseRewardVO(10028,1)],[new NeedVO(10000,2700),new NeedVO(10007,52)],"scene/qzmj33.json");
         this._battles[this._battles.length] = new MissionBattleVO(2074,3280,5171,[new BaseRewardVO(10000,2800),new BaseRewardVO(10407,1)],[new NeedVO(10000,2700),new NeedVO(10003,52)],"scene/yzmj22-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2075,3309,5214,[new BaseRewardVO(10000,2800),new BaseRewardVO(10029,1)],[new NeedVO(10000,2700),new NeedVO(10004,52)],"scene/qzmj35.json");
         this._battles[this._battles.length] = new MissionBattleVO(2077,3338,5257,[new BaseRewardVO(10000,2900),new BaseRewardVO(10404,1)],[new NeedVO(10000,2800),new NeedVO(10003,55)],"scene/yzmj18-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2078,3367,5300,[new BaseRewardVO(10000,2900),new BaseRewardVO(10027,1)],[new NeedVO(10000,2800),new NeedVO(10004,55)],"scene/yzmj19-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2079,3396,5343,[new BaseRewardVO(10000,2900),new BaseRewardVO(10405,1)],[new NeedVO(10000,2800),new NeedVO(10005,55)],"scene/yzmj20-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2080,3425,5386,[new BaseRewardVO(10000,2900),new BaseRewardVO(10028,1)],[new NeedVO(10000,2800),new NeedVO(10006,55)],"scene/yzmj21-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2082,3454,5429,[new BaseRewardVO(10000,2900),new BaseRewardVO(10404,1)],[new NeedVO(10000,2800),new NeedVO(10007,55)],"scene/yzmj22-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2083,3483,5472,[new BaseRewardVO(10000,2900),new BaseRewardVO(10029,1)],[new NeedVO(10000,2800),new NeedVO(10003,55)],"scene/yzmj23-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2084,3512,5515,[new BaseRewardVO(10000,2900),new BaseRewardVO(10406,1)],[new NeedVO(10000,2800),new NeedVO(10004,55)],"scene/qzmj31.json");
         this._battles[this._battles.length] = new MissionBattleVO(2085,3541,5558,[new BaseRewardVO(10000,2900),new BaseRewardVO(10027,1)],[new NeedVO(10000,2800),new NeedVO(10005,55)],"scene/qzmj32.json");
         this._battles[this._battles.length] = new MissionBattleVO(2087,3570,5601,[new BaseRewardVO(10000,2900),new BaseRewardVO(10404,1)],[new NeedVO(10000,2800),new NeedVO(10006,55)],"scene/qzmj33.json");
         this._battles[this._battles.length] = new MissionBattleVO(2088,3599,5644,[new BaseRewardVO(10000,2900),new BaseRewardVO(10028,1)],[new NeedVO(10000,2800),new NeedVO(10007,55)],"scene/qzmj34.json");
         this._battles[this._battles.length] = new MissionBattleVO(2089,3628,5687,[new BaseRewardVO(10000,2900),new BaseRewardVO(10407,1)],[new NeedVO(10000,2800),new NeedVO(10003,55)],"scene/qzmj35.json");
         this._battles[this._battles.length] = new MissionBattleVO(2090,3658,5730,[new BaseRewardVO(10000,2900),new BaseRewardVO(10029,1)],[new NeedVO(10000,2800),new NeedVO(10004,55)],"scene/qzmj36.json");
         this._battles[this._battles.length] = new MissionBattleVO(2092,3686,5773,[new BaseRewardVO(10000,3000),new BaseRewardVO(10404,1)],[new NeedVO(10000,2900),new NeedVO(10003,58)],"scene/jzmj37-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2093,3715,5816,[new BaseRewardVO(10000,3000),new BaseRewardVO(10027,1)],[new NeedVO(10000,2900),new NeedVO(10004,58)],"scene/jzmj38-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2094,3744,5859,[new BaseRewardVO(10000,3000),new BaseRewardVO(10405,1)],[new NeedVO(10000,2900),new NeedVO(10005,58)],"scene/jzmj39-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2095,3773,5902,[new BaseRewardVO(10000,3000),new BaseRewardVO(10028,1)],[new NeedVO(10000,2900),new NeedVO(10006,58)],"scene/jzmj40-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2097,3802,5945,[new BaseRewardVO(10000,3000),new BaseRewardVO(10404,1)],[new NeedVO(10000,2900),new NeedVO(10007,58)],"scene/jzmj41-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2098,3831,5988,[new BaseRewardVO(10000,3000),new BaseRewardVO(10029,1)],[new NeedVO(10000,2900),new NeedVO(10003,61)],"scene/jzmj42-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2099,3860,6031,[new BaseRewardVO(10000,3000),new BaseRewardVO(10406,1)],[new NeedVO(10000,2900),new NeedVO(10004,61)],"scene/jzmj43-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2100,3889,6074,[new BaseRewardVO(10000,3000),new BaseRewardVO(10027,1)],[new NeedVO(10000,2900),new NeedVO(10005,61)],"scene/jzmj44-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2102,3918,6117,[new BaseRewardVO(10000,3000),new BaseRewardVO(10404,1)],[new NeedVO(10000,2900),new NeedVO(10006,61)],"scene/jzmj45-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2103,3947,6160,[new BaseRewardVO(10000,3000),new BaseRewardVO(10028,1)],[new NeedVO(10000,2900),new NeedVO(10007,61)],"scene/jzmj46-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2104,3976,6203,[new BaseRewardVO(10000,3000),new BaseRewardVO(10407,1)],[new NeedVO(10000,2900),new NeedVO(10003,64)],"scene/jzmj47-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2105,4005,6246,[new BaseRewardVO(10000,3000),new BaseRewardVO(10029,1)],[new NeedVO(10000,2900),new NeedVO(10004,64)],"scene/jzmj48-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2107,4034,6289,[new BaseRewardVO(10000,3100),new BaseRewardVO(10404,1)],[new NeedVO(10000,3000),new NeedVO(10003,65)],"scene/qzmj24.json");
         this._battles[this._battles.length] = new MissionBattleVO(2108,4063,6332,[new BaseRewardVO(10000,3100),new BaseRewardVO(10027,1)],[new NeedVO(10000,3000),new NeedVO(10004,65)],"scene/jzmj38-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2109,4092,6375,[new BaseRewardVO(10000,3100),new BaseRewardVO(10405,1)],[new NeedVO(10000,3000),new NeedVO(10005,65)],"scene/qzmj26.json");
         this._battles[this._battles.length] = new MissionBattleVO(2110,4121,6418,[new BaseRewardVO(10000,3100),new BaseRewardVO(10028,1)],[new NeedVO(10000,3000),new NeedVO(10006,65)],"scene/jzmj40-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2112,4150,6461,[new BaseRewardVO(10000,3100),new BaseRewardVO(10404,1)],[new NeedVO(10000,3000),new NeedVO(10007,65)],"scene/qzmj28.json");
         this._battles[this._battles.length] = new MissionBattleVO(2113,4179,6504,[new BaseRewardVO(10000,3100),new BaseRewardVO(10029,1)],[new NeedVO(10000,3000),new NeedVO(10003,66)],"scene/jzmj42-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2114,4208,6547,[new BaseRewardVO(10000,3100),new BaseRewardVO(10406,1)],[new NeedVO(10000,3000),new NeedVO(10004,66)],"scene/qzmj30.json");
         this._battles[this._battles.length] = new MissionBattleVO(2115,4237,6590,[new BaseRewardVO(10000,3100),new BaseRewardVO(10027,1)],[new NeedVO(10000,3000),new NeedVO(10005,66)],"scene/jzmj44-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2117,4266,6633,[new BaseRewardVO(10000,3100),new BaseRewardVO(10404,1)],[new NeedVO(10000,3000),new NeedVO(10006,66)],"scene/qzmj32.json");
         this._battles[this._battles.length] = new MissionBattleVO(2118,4295,6676,[new BaseRewardVO(10000,3100),new BaseRewardVO(10028,1)],[new NeedVO(10000,3000),new NeedVO(10007,66)],"scene/jzmj46-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2119,4324,6719,[new BaseRewardVO(10000,3100),new BaseRewardVO(10407,1)],[new NeedVO(10000,3000),new NeedVO(10003,67)],"scene/qzmj34.json");
         this._battles[this._battles.length] = new MissionBattleVO(2120,4353,6762,[new BaseRewardVO(10000,3100),new BaseRewardVO(10029,1)],[new NeedVO(10000,3000),new NeedVO(10004,67)],"scene/jzmj48-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2122,4382,6805,[new BaseRewardVO(10000,3200),new BaseRewardVO(10404,1)],[new NeedVO(10000,3100),new NeedVO(10003,68)],"scene/qzmj25.json");
         this._battles[this._battles.length] = new MissionBattleVO(2123,4411,6848,[new BaseRewardVO(10000,3200),new BaseRewardVO(10027,1)],[new NeedVO(10000,3100),new NeedVO(10004,68)],"scene/jzmj37-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2124,4440,6891,[new BaseRewardVO(10000,3200),new BaseRewardVO(10405,1)],[new NeedVO(10000,3100),new NeedVO(10005,68)],"scene/qzmj27.json");
         this._battles[this._battles.length] = new MissionBattleVO(2125,4469,6934,[new BaseRewardVO(10000,3200),new BaseRewardVO(10028,1)],[new NeedVO(10000,3100),new NeedVO(10006,68)],"scene/jzmj39-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2127,4498,6977,[new BaseRewardVO(10000,3200),new BaseRewardVO(10404,1)],[new NeedVO(10000,3100),new NeedVO(10007,68)],"scene/qzmj29.json");
         this._battles[this._battles.length] = new MissionBattleVO(2128,4527,7020,[new BaseRewardVO(10000,3200),new BaseRewardVO(10029,1)],[new NeedVO(10000,3100),new NeedVO(10003,69)],"scene/jzmj41-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2129,4556,7063,[new BaseRewardVO(10000,3200),new BaseRewardVO(10406,1)],[new NeedVO(10000,3100),new NeedVO(10004,69)],"scene/qzmj31.json");
         this._battles[this._battles.length] = new MissionBattleVO(2130,4585,7106,[new BaseRewardVO(10000,3200),new BaseRewardVO(10027,1)],[new NeedVO(10000,3100),new NeedVO(10005,69)],"scene/jzmj43-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2132,4614,7149,[new BaseRewardVO(10000,3200),new BaseRewardVO(10404,1)],[new NeedVO(10000,3100),new NeedVO(10006,69)],"scene/qzmj33.json");
         this._battles[this._battles.length] = new MissionBattleVO(2133,4643,7192,[new BaseRewardVO(10000,3200),new BaseRewardVO(10028,1)],[new NeedVO(10000,3100),new NeedVO(10007,69)],"scene/jzmj45-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2134,4672,7235,[new BaseRewardVO(10000,3200),new BaseRewardVO(10407,1)],[new NeedVO(10000,3100),new NeedVO(10003,70)],"scene/qzmj35.json");
         this._battles[this._battles.length] = new MissionBattleVO(2135,4701,7278,[new BaseRewardVO(10000,3200),new BaseRewardVO(10029,1)],[new NeedVO(10000,3100),new NeedVO(10004,70)],"scene/jzmj47-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2137,4730,7321,[new BaseRewardVO(10000,3300),new BaseRewardVO(10404,1)],[new NeedVO(10000,3200),new NeedVO(10003,71)],"scene/yzmj11-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2138,4759,7364,[new BaseRewardVO(10000,3300),new BaseRewardVO(10027,1)],[new NeedVO(10000,3200),new NeedVO(10004,71)],"scene/qzmj24.json");
         this._battles[this._battles.length] = new MissionBattleVO(2139,4788,7407,[new BaseRewardVO(10000,3300),new BaseRewardVO(10301,1)],[new NeedVO(10000,3200),new NeedVO(10005,72)],"scene/yzmj13-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2140,4817,7450,[new BaseRewardVO(10000,3300),new BaseRewardVO(10028,1)],[new NeedVO(10000,3200),new NeedVO(10006,72)],"scene/qzmj26.json");
         this._battles[this._battles.length] = new MissionBattleVO(2142,4846,7493,[new BaseRewardVO(10000,3300),new BaseRewardVO(10404,1)],[new NeedVO(10000,3200),new NeedVO(10007,72)],"scene/yzmj15-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2143,4875,7536,[new BaseRewardVO(10000,3300),new BaseRewardVO(10029,1)],[new NeedVO(10000,3200),new NeedVO(10003,73)],"scene/qzmj28.json");
         this._battles[this._battles.length] = new MissionBattleVO(2144,4904,7579,[new BaseRewardVO(10000,3300),new BaseRewardVO(10301,1)],[new NeedVO(10000,3200),new NeedVO(10004,73)],"scene/yzmj17-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2145,4933,7622,[new BaseRewardVO(10000,3300),new BaseRewardVO(10027,1)],[new NeedVO(10000,3200),new NeedVO(10005,73)],"scene/qzmj30.json");
         this._battles[this._battles.length] = new MissionBattleVO(2147,4962,7665,[new BaseRewardVO(10000,3300),new BaseRewardVO(10404,1)],[new NeedVO(10000,3200),new NeedVO(10006,73)],"scene/yzmj19-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2148,4991,7708,[new BaseRewardVO(10000,3300),new BaseRewardVO(10028,1)],[new NeedVO(10000,3200),new NeedVO(10007,73)],"scene/qzmj32.json");
         this._battles[this._battles.length] = new MissionBattleVO(2149,5020,7751,[new BaseRewardVO(10000,3300),new BaseRewardVO(10301,1)],[new NeedVO(10000,3200),new NeedVO(10003,74)],"scene/yzmj21-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2150,5049,7794,[new BaseRewardVO(10000,3300),new BaseRewardVO(10029,1)],[new NeedVO(10000,3200),new NeedVO(10004,74)],"scene/qzmj34.json");
         this._battles[this._battles.length] = new MissionBattleVO(2152,5078,9000,[new BaseRewardVO(10000,3400),new BaseRewardVO(10404,1)],[new NeedVO(10000,3300),new NeedVO(10003,75)],"scene/yzmj12-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2153,5107,9000,[new BaseRewardVO(10000,3400),new BaseRewardVO(10027,1)],[new NeedVO(10000,3300),new NeedVO(10004,75)],"scene/qzmj25.json");
         this._battles[this._battles.length] = new MissionBattleVO(2154,5136,9000,[new BaseRewardVO(10000,3400),new BaseRewardVO(10405,1)],[new NeedVO(10000,3300),new NeedVO(10005,75)],"scene/yzmj14-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2155,5165,9000,[new BaseRewardVO(10000,3400),new BaseRewardVO(10028,1)],[new NeedVO(10000,3300),new NeedVO(10006,75)],"scene/qzmj27.json");
         this._battles[this._battles.length] = new MissionBattleVO(2157,5194,9000,[new BaseRewardVO(10000,3400),new BaseRewardVO(10404,1)],[new NeedVO(10000,3300),new NeedVO(10007,75)],"scene/yzmj16-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2158,5223,9000,[new BaseRewardVO(10000,3400),new BaseRewardVO(10029,1)],[new NeedVO(10000,3300),new NeedVO(10003,76)],"scene/qzmj29.json");
         this._battles[this._battles.length] = new MissionBattleVO(2159,5252,9000,[new BaseRewardVO(10000,3400),new BaseRewardVO(10406,1)],[new NeedVO(10000,3300),new NeedVO(10004,76)],"scene/yzmj18-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2160,5281,9000,[new BaseRewardVO(10000,3400),new BaseRewardVO(10027,1)],[new NeedVO(10000,3300),new NeedVO(10005,76)],"scene/qzmj31.json");
         this._battles[this._battles.length] = new MissionBattleVO(2162,5310,9000,[new BaseRewardVO(10000,3400),new BaseRewardVO(10404,1)],[new NeedVO(10000,3300),new NeedVO(10006,76)],"scene/yzmj20-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2163,5339,9000,[new BaseRewardVO(10000,3400),new BaseRewardVO(10028,1)],[new NeedVO(10000,3300),new NeedVO(10007,76)],"scene/qzmj33.json");
         this._battles[this._battles.length] = new MissionBattleVO(2164,5368,9000,[new BaseRewardVO(10000,3400),new BaseRewardVO(10407,1)],[new NeedVO(10000,3300),new NeedVO(10003,77)],"scene/yzmj22-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2165,5397,9000,[new BaseRewardVO(10000,3400),new BaseRewardVO(10029,1)],[new NeedVO(10000,3300),new NeedVO(10004,77)],"scene/qzmj35.json");
         this._battles[this._battles.length] = new MissionBattleVO(2167,5426,11000,[new BaseRewardVO(10000,3500),new BaseRewardVO(10404,1)],[new NeedVO(10000,3400),new NeedVO(10003,78)],"scene/qzmj24.json");
         this._battles[this._battles.length] = new MissionBattleVO(2168,5455,11000,[new BaseRewardVO(10000,3500),new BaseRewardVO(10027,1)],[new NeedVO(10000,3400),new NeedVO(10004,78)],"scene/jzmj38-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2169,5484,11000,[new BaseRewardVO(10000,3500),new BaseRewardVO(10405,1)],[new NeedVO(10000,3400),new NeedVO(10005,78)],"scene/qzmj26.json");
         this._battles[this._battles.length] = new MissionBattleVO(2170,5513,11000,[new BaseRewardVO(10000,3500),new BaseRewardVO(10028,1)],[new NeedVO(10000,3400),new NeedVO(10006,78)],"scene/jzmj40-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2172,5542,11000,[new BaseRewardVO(10000,3500),new BaseRewardVO(10404,1)],[new NeedVO(10000,3400),new NeedVO(10007,78)],"scene/qzmj28.json");
         this._battles[this._battles.length] = new MissionBattleVO(2173,5571,11000,[new BaseRewardVO(10000,3500),new BaseRewardVO(10029,1)],[new NeedVO(10000,3400),new NeedVO(10003,79)],"scene/jzmj42-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2174,5600,11000,[new BaseRewardVO(10000,3500),new BaseRewardVO(10406,1)],[new NeedVO(10000,3400),new NeedVO(10004,79)],"scene/qzmj30.json");
         this._battles[this._battles.length] = new MissionBattleVO(2175,5629,11000,[new BaseRewardVO(10000,3500),new BaseRewardVO(10027,1)],[new NeedVO(10000,3400),new NeedVO(10005,79)],"scene/jzmj44-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2177,5658,11000,[new BaseRewardVO(10000,3500),new BaseRewardVO(10404,1)],[new NeedVO(10000,3400),new NeedVO(10006,79)],"scene/qzmj32.json");
         this._battles[this._battles.length] = new MissionBattleVO(2178,5687,11000,[new BaseRewardVO(10000,3500),new BaseRewardVO(10028,1)],[new NeedVO(10000,3400),new NeedVO(10007,79)],"scene/jzmj46-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2179,5716,11000,[new BaseRewardVO(10000,3500),new BaseRewardVO(10407,1)],[new NeedVO(10000,3400),new NeedVO(10003,80)],"scene/qzmj34.json");
         this._battles[this._battles.length] = new MissionBattleVO(2180,5745,11000,[new BaseRewardVO(10000,3500),new BaseRewardVO(10029,1)],[new NeedVO(10000,3400),new NeedVO(10004,80)],"scene/jzmj48-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2182,5574,13000,[new BaseRewardVO(10000,3600),new BaseRewardVO(10404,1)],[new NeedVO(10000,3500),new NeedVO(10003,81)],"scene/yzmj11-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2183,5803,13000,[new BaseRewardVO(10000,3600),new BaseRewardVO(10027,1)],[new NeedVO(10000,3500),new NeedVO(10004,81)],"scene/yzmj12-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2184,5832,13000,[new BaseRewardVO(10000,3600),new BaseRewardVO(10301,1)],[new NeedVO(10000,3500),new NeedVO(10005,81)],"scene/yzmj13-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2185,5861,13000,[new BaseRewardVO(10000,3600),new BaseRewardVO(10028,1)],[new NeedVO(10000,3500),new NeedVO(10006,81)],"scene/yzmj14-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2187,5890,13000,[new BaseRewardVO(10000,3600),new BaseRewardVO(10404,1)],[new NeedVO(10000,3500),new NeedVO(10007,81)],"scene/yzmj15-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2188,5919,13000,[new BaseRewardVO(10000,3600),new BaseRewardVO(10029,1)],[new NeedVO(10000,3500),new NeedVO(10003,82)],"scene/yzmj16-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2189,5948,13000,[new BaseRewardVO(10000,3600),new BaseRewardVO(10301,1)],[new NeedVO(10000,3500),new NeedVO(10004,82)],"scene/yzmj17-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2190,5977,13000,[new BaseRewardVO(10000,3600),new BaseRewardVO(10027,1)],[new NeedVO(10000,3500),new NeedVO(10005,82)],"scene/yzmj18-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2192,6006,13000,[new BaseRewardVO(10000,3600),new BaseRewardVO(10404,1)],[new NeedVO(10000,3500),new NeedVO(10006,82)],"scene/yzmj19-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2193,6035,13000,[new BaseRewardVO(10000,3600),new BaseRewardVO(10028,1)],[new NeedVO(10000,3500),new NeedVO(10007,82)],"scene/yzmj20-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2194,6064,13000,[new BaseRewardVO(10000,3600),new BaseRewardVO(10301,1)],[new NeedVO(10000,3500),new NeedVO(10003,83)],"scene/yzmj21-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2195,6093,13000,[new BaseRewardVO(10000,3600),new BaseRewardVO(10029,1)],[new NeedVO(10000,3500),new NeedVO(10004,83)],"scene/yzmj22-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2197,6122,15000,[new BaseRewardVO(10000,3700),new BaseRewardVO(10404,1)],[new NeedVO(10000,3600),new NeedVO(10003,84)],"scene/yzmj11-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2198,6151,15000,[new BaseRewardVO(10000,3700),new BaseRewardVO(10027,1)],[new NeedVO(10000,3600),new NeedVO(10004,84)],"scene/qzmj24.json");
         this._battles[this._battles.length] = new MissionBattleVO(2199,6180,15000,[new BaseRewardVO(10000,3700),new BaseRewardVO(10301,1)],[new NeedVO(10000,3600),new NeedVO(10005,84)],"scene/yzmj13-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2200,6209,15000,[new BaseRewardVO(10000,3700),new BaseRewardVO(10028,1)],[new NeedVO(10000,3600),new NeedVO(10006,84)],"scene/qzmj26.json");
         this._battles[this._battles.length] = new MissionBattleVO(2202,6238,15000,[new BaseRewardVO(10000,3700),new BaseRewardVO(10404,1)],[new NeedVO(10000,3600),new NeedVO(10007,84)],"scene/yzmj15-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2203,6267,15000,[new BaseRewardVO(10000,3700),new BaseRewardVO(10029,1)],[new NeedVO(10000,3600),new NeedVO(10003,85)],"scene/qzmj28.json");
         this._battles[this._battles.length] = new MissionBattleVO(2204,6296,15000,[new BaseRewardVO(10000,3700),new BaseRewardVO(10301,1)],[new NeedVO(10000,3600),new NeedVO(10004,85)],"scene/yzmj17-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2205,6325,15000,[new BaseRewardVO(10000,3700),new BaseRewardVO(10027,1)],[new NeedVO(10000,3600),new NeedVO(10005,85)],"scene/qzmj30.json");
         this._battles[this._battles.length] = new MissionBattleVO(2207,6354,15000,[new BaseRewardVO(10000,3700),new BaseRewardVO(10404,1)],[new NeedVO(10000,3600),new NeedVO(10006,85)],"scene/yzmj19-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2208,6383,15000,[new BaseRewardVO(10000,3700),new BaseRewardVO(10028,1)],[new NeedVO(10000,3600),new NeedVO(10007,85)],"scene/qzmj32.json");
         this._battles[this._battles.length] = new MissionBattleVO(2209,6412,15000,[new BaseRewardVO(10000,3700),new BaseRewardVO(10301,1)],[new NeedVO(10000,3600),new NeedVO(10003,86)],"scene/yzmj21-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2210,6441,15000,[new BaseRewardVO(10000,3700),new BaseRewardVO(10029,1)],[new NeedVO(10000,3600),new NeedVO(10004,86)],"scene/qzmj34.json");
         this._battles[this._battles.length] = new MissionBattleVO(2212,6470,17000,[new BaseRewardVO(10000,3800),new BaseRewardVO(10404,1)],[new NeedVO(10000,3700),new NeedVO(10003,84)],"scene/yzmj12-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2213,6499,17000,[new BaseRewardVO(10000,3800),new BaseRewardVO(10027,1)],[new NeedVO(10000,3700),new NeedVO(10004,84)],"scene/qzmj25.json");
         this._battles[this._battles.length] = new MissionBattleVO(2214,6528,17000,[new BaseRewardVO(10000,3800),new BaseRewardVO(10301,1)],[new NeedVO(10000,3700),new NeedVO(10005,84)],"scene/yzmj14-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2215,6557,17000,[new BaseRewardVO(10000,3800),new BaseRewardVO(10028,1)],[new NeedVO(10000,3700),new NeedVO(10006,84)],"scene/qzmj27.json");
         this._battles[this._battles.length] = new MissionBattleVO(2217,6586,17000,[new BaseRewardVO(10000,3800),new BaseRewardVO(10404,1)],[new NeedVO(10000,3700),new NeedVO(10007,84)],"scene/yzmj16-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2218,6615,17000,[new BaseRewardVO(10000,3800),new BaseRewardVO(10029,1)],[new NeedVO(10000,3700),new NeedVO(10003,85)],"scene/qzmj29.json");
         this._battles[this._battles.length] = new MissionBattleVO(2219,6644,17000,[new BaseRewardVO(10000,3800),new BaseRewardVO(10301,1)],[new NeedVO(10000,3700),new NeedVO(10004,85)],"scene/yzmj18-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2220,6673,17000,[new BaseRewardVO(10000,3800),new BaseRewardVO(10027,1)],[new NeedVO(10000,3700),new NeedVO(10005,85)],"scene/qzmj31.json");
         this._battles[this._battles.length] = new MissionBattleVO(2222,6702,17000,[new BaseRewardVO(10000,3800),new BaseRewardVO(10404,1)],[new NeedVO(10000,3700),new NeedVO(10006,85)],"scene/yzmj20-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2223,6731,17000,[new BaseRewardVO(10000,3800),new BaseRewardVO(10028,1)],[new NeedVO(10000,3700),new NeedVO(10007,85)],"scene/qzmj33.json");
         this._battles[this._battles.length] = new MissionBattleVO(2224,6760,17000,[new BaseRewardVO(10000,3800),new BaseRewardVO(10301,1)],[new NeedVO(10000,3700),new NeedVO(10003,86)],"scene/yzmj22-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2225,6789,17000,[new BaseRewardVO(10000,3800),new BaseRewardVO(10029,1)],[new NeedVO(10000,3700),new NeedVO(10004,86)],"scene/qzmj35.json");
         this._battles[this._battles.length] = new MissionBattleVO(2227,6818,19000,[new BaseRewardVO(10000,3900),new BaseRewardVO(10404,1)],[new NeedVO(10000,3800),new NeedVO(10003,87)],"scene/jzmj37-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2228,6847,19000,[new BaseRewardVO(10000,3900),new BaseRewardVO(10027,1)],[new NeedVO(10000,3800),new NeedVO(10004,87)],"scene/yzmj12-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2229,6876,19000,[new BaseRewardVO(10000,3900),new BaseRewardVO(10301,1)],[new NeedVO(10000,3800),new NeedVO(10005,87)],"scene/jzmj39-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2230,6905,19000,[new BaseRewardVO(10000,3900),new BaseRewardVO(10028,1)],[new NeedVO(10000,3800),new NeedVO(10006,87)],"scene/yzmj14-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2232,6934,19000,[new BaseRewardVO(10000,3900),new BaseRewardVO(10404,1)],[new NeedVO(10000,3800),new NeedVO(10007,87)],"scene/jzmj41-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2233,6963,19000,[new BaseRewardVO(10000,3900),new BaseRewardVO(10029,1)],[new NeedVO(10000,3800),new NeedVO(10003,88)],"scene/yzmj16-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2234,6992,19000,[new BaseRewardVO(10000,3900),new BaseRewardVO(10301,1)],[new NeedVO(10000,3800),new NeedVO(10004,88)],"scene/jzmj43-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2235,7021,19000,[new BaseRewardVO(10000,3900),new BaseRewardVO(10027,1)],[new NeedVO(10000,3800),new NeedVO(10005,88)],"scene/yzmj18-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2237,7050,19000,[new BaseRewardVO(10000,3900),new BaseRewardVO(10404,1)],[new NeedVO(10000,3800),new NeedVO(10006,88)],"scene/jzmj45-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2238,7079,19000,[new BaseRewardVO(10000,3900),new BaseRewardVO(10028,1)],[new NeedVO(10000,3800),new NeedVO(10007,88)],"scene/yzmj20-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2239,7108,19000,[new BaseRewardVO(10000,3900),new BaseRewardVO(10301,1)],[new NeedVO(10000,3800),new NeedVO(10003,89)],"scene/jzmj47-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2240,7137,19000,[new BaseRewardVO(10000,3900),new BaseRewardVO(10029,1)],[new NeedVO(10000,3800),new NeedVO(10004,89)],"scene/yzmj22-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2242,7166,21000,[new BaseRewardVO(10000,4000),new BaseRewardVO(10404,1)],[new NeedVO(10000,3900),new NeedVO(10003,90)],"scene/yzmj11-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2243,7195,21000,[new BaseRewardVO(10000,4000),new BaseRewardVO(10027,1)],[new NeedVO(10000,3900),new NeedVO(10004,90)],"scene/qzmj24.json");
         this._battles[this._battles.length] = new MissionBattleVO(2244,7224,21000,[new BaseRewardVO(10000,4000),new BaseRewardVO(10301,1)],[new NeedVO(10000,3900),new NeedVO(10005,90)],"scene/yzmj13-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2245,7253,21000,[new BaseRewardVO(10000,4000),new BaseRewardVO(10028,1)],[new NeedVO(10000,3900),new NeedVO(10006,90)],"scene/qzmj26.json");
         this._battles[this._battles.length] = new MissionBattleVO(2247,7282,21000,[new BaseRewardVO(10000,4000),new BaseRewardVO(10404,1)],[new NeedVO(10000,3900),new NeedVO(10007,90)],"scene/yzmj15-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2248,7311,21000,[new BaseRewardVO(10000,4000),new BaseRewardVO(10029,1)],[new NeedVO(10000,3900),new NeedVO(10003,91)],"scene/qzmj28.json");
         this._battles[this._battles.length] = new MissionBattleVO(2249,7340,21000,[new BaseRewardVO(10000,4000),new BaseRewardVO(10301,1)],[new NeedVO(10000,3900),new NeedVO(10004,91)],"scene/yzmj17-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2250,7369,21000,[new BaseRewardVO(10000,4000),new BaseRewardVO(10027,1)],[new NeedVO(10000,3900),new NeedVO(10005,91)],"scene/qzmj30.json");
         this._battles[this._battles.length] = new MissionBattleVO(2252,7398,21000,[new BaseRewardVO(10000,4000),new BaseRewardVO(10404,1)],[new NeedVO(10000,3900),new NeedVO(10006,91)],"scene/yzmj19-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2253,7427,21000,[new BaseRewardVO(10000,4000),new BaseRewardVO(10028,1)],[new NeedVO(10000,3900),new NeedVO(10007,91)],"scene/qzmj32.json");
         this._battles[this._battles.length] = new MissionBattleVO(2254,7456,21000,[new BaseRewardVO(10000,4000),new BaseRewardVO(10301,1)],[new NeedVO(10000,3900),new NeedVO(10003,92)],"scene/yzmj21-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2255,7485,21000,[new BaseRewardVO(10000,4000),new BaseRewardVO(10029,1)],[new NeedVO(10000,3900),new NeedVO(10004,92)],"scene/qzmj34.json");
         this._battles[this._battles.length] = new MissionBattleVO(2257,7514,23000,[new BaseRewardVO(10000,4100),new BaseRewardVO(10404,1)],[new NeedVO(10000,4000),new NeedVO(10003,93)],"scene/yzmj12-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2258,7543,23000,[new BaseRewardVO(10000,4100),new BaseRewardVO(10027,1)],[new NeedVO(10000,4000),new NeedVO(10004,93)],"scene/qzmj25.json");
         this._battles[this._battles.length] = new MissionBattleVO(2259,7572,23000,[new BaseRewardVO(10000,4100),new BaseRewardVO(10301,1)],[new NeedVO(10000,4000),new NeedVO(10005,93)],"scene/yzmj14-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2260,7601,23000,[new BaseRewardVO(10000,4100),new BaseRewardVO(10028,1)],[new NeedVO(10000,4000),new NeedVO(10006,93)],"scene/qzmj27.json");
         this._battles[this._battles.length] = new MissionBattleVO(2262,7630,23000,[new BaseRewardVO(10000,4100),new BaseRewardVO(10404,1)],[new NeedVO(10000,4000),new NeedVO(10007,93)],"scene/yzmj16-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2263,7659,23000,[new BaseRewardVO(10000,4100),new BaseRewardVO(10029,1)],[new NeedVO(10000,4000),new NeedVO(10003,94)],"scene/qzmj29.json");
         this._battles[this._battles.length] = new MissionBattleVO(2264,7688,23000,[new BaseRewardVO(10000,4100),new BaseRewardVO(10301,1)],[new NeedVO(10000,4000),new NeedVO(10004,94)],"scene/yzmj18-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2265,7717,23000,[new BaseRewardVO(10000,4100),new BaseRewardVO(10027,1)],[new NeedVO(10000,4000),new NeedVO(10005,94)],"scene/qzmj31.json");
         this._battles[this._battles.length] = new MissionBattleVO(2267,7746,23000,[new BaseRewardVO(10000,4100),new BaseRewardVO(10404,1)],[new NeedVO(10000,4000),new NeedVO(10006,94)],"scene/yzmj20-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2268,7775,23000,[new BaseRewardVO(10000,4100),new BaseRewardVO(10028,1)],[new NeedVO(10000,4000),new NeedVO(10007,94)],"scene/qzmj33.json");
         this._battles[this._battles.length] = new MissionBattleVO(2269,7804,23000,[new BaseRewardVO(10000,4100),new BaseRewardVO(10301,1)],[new NeedVO(10000,4000),new NeedVO(10003,95)],"scene/yzmj22-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2270,7833,23000,[new BaseRewardVO(10000,4100),new BaseRewardVO(10029,1)],[new NeedVO(10000,4000),new NeedVO(10004,95)],"scene/qzmj35.json");
         this._battles[this._battles.length] = new MissionBattleVO(2272,7862,25000,[new BaseRewardVO(10000,4200),new BaseRewardVO(10404,1)],[new NeedVO(10000,4100),new NeedVO(10003,96)],"scene/yzmj11-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2273,7891,25000,[new BaseRewardVO(10000,4200),new BaseRewardVO(10027,1)],[new NeedVO(10000,4100),new NeedVO(10004,96)],"scene/yzmj12-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2274,7920,25000,[new BaseRewardVO(10000,4200),new BaseRewardVO(10301,1)],[new NeedVO(10000,4100),new NeedVO(10005,96)],"scene/yzmj13-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2275,7949,25000,[new BaseRewardVO(10000,4200),new BaseRewardVO(10028,1)],[new NeedVO(10000,4100),new NeedVO(10006,96)],"scene/yzmj14-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2277,7978,25000,[new BaseRewardVO(10000,4200),new BaseRewardVO(10404,1)],[new NeedVO(10000,4100),new NeedVO(10007,96)],"scene/yzmj15-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2278,8007,25000,[new BaseRewardVO(10000,4200),new BaseRewardVO(10029,1)],[new NeedVO(10000,4100),new NeedVO(10003,97)],"scene/yzmj16-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2279,8036,25000,[new BaseRewardVO(10000,4200),new BaseRewardVO(10301,1)],[new NeedVO(10000,4100),new NeedVO(10004,97)],"scene/yzmj17-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2280,8065,25000,[new BaseRewardVO(10000,4200),new BaseRewardVO(10027,1)],[new NeedVO(10000,4100),new NeedVO(10005,97)],"scene/yzmj18-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2282,8094,25000,[new BaseRewardVO(10000,4200),new BaseRewardVO(10404,1)],[new NeedVO(10000,4100),new NeedVO(10006,97)],"scene/yzmj19-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2283,8123,25000,[new BaseRewardVO(10000,4200),new BaseRewardVO(10028,1)],[new NeedVO(10000,4100),new NeedVO(10007,97)],"scene/yzmj20-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2284,8152,25000,[new BaseRewardVO(10000,4200),new BaseRewardVO(10301,1)],[new NeedVO(10000,4100),new NeedVO(10003,98)],"scene/yzmj21-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2285,8181,25000,[new BaseRewardVO(10000,4200),new BaseRewardVO(10029,1)],[new NeedVO(10000,4100),new NeedVO(10004,98)],"scene/yzmj22-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2287,8210,27000,[new BaseRewardVO(10000,4300),new BaseRewardVO(10404,1)],[new NeedVO(10000,4200),new NeedVO(10003,99)],"scene/yzmj12-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2288,8239,27000,[new BaseRewardVO(10000,4300),new BaseRewardVO(10027,1)],[new NeedVO(10000,4200),new NeedVO(10004,99)],"scene/qzmj25.json");
         this._battles[this._battles.length] = new MissionBattleVO(2289,8268,27000,[new BaseRewardVO(10000,4300),new BaseRewardVO(10301,1)],[new NeedVO(10000,4200),new NeedVO(10005,99)],"scene/yzmj14-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2290,8297,27000,[new BaseRewardVO(10000,4300),new BaseRewardVO(10028,1)],[new NeedVO(10000,4200),new NeedVO(10006,99)],"scene/qzmj27.json");
         this._battles[this._battles.length] = new MissionBattleVO(2292,8326,27000,[new BaseRewardVO(10000,4300),new BaseRewardVO(10404,1)],[new NeedVO(10000,4200),new NeedVO(10007,99)],"scene/yzmj16-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2293,8355,27000,[new BaseRewardVO(10000,4300),new BaseRewardVO(10029,1)],[new NeedVO(10000,4200),new NeedVO(10003,100)],"scene/qzmj29.json");
         this._battles[this._battles.length] = new MissionBattleVO(2294,8384,27000,[new BaseRewardVO(10000,4300),new BaseRewardVO(10301,1)],[new NeedVO(10000,4200),new NeedVO(10004,101)],"scene/yzmj18-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2295,8413,27000,[new BaseRewardVO(10000,4300),new BaseRewardVO(10027,1)],[new NeedVO(10000,4200),new NeedVO(10005,101)],"scene/qzmj31.json");
         this._battles[this._battles.length] = new MissionBattleVO(2297,8442,27000,[new BaseRewardVO(10000,4300),new BaseRewardVO(10404,1)],[new NeedVO(10000,4200),new NeedVO(10006,101)],"scene/yzmj20-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2298,8471,27000,[new BaseRewardVO(10000,4300),new BaseRewardVO(10028,1)],[new NeedVO(10000,4200),new NeedVO(10007,101)],"scene/qzmj33.json");
         this._battles[this._battles.length] = new MissionBattleVO(2299,8500,27000,[new BaseRewardVO(10000,4300),new BaseRewardVO(10301,1)],[new NeedVO(10000,4200),new NeedVO(10003,102)],"scene/yzmj22-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2300,8529,27000,[new BaseRewardVO(10000,4300),new BaseRewardVO(10029,1)],[new NeedVO(10000,4200),new NeedVO(10004,102)],"scene/qzmj35.json");
         this._battles[this._battles.length] = new MissionBattleVO(2302,8529,29000,[new BaseRewardVO(10000,4400),new BaseRewardVO(10404,1)],[new NeedVO(10000,4300),new NeedVO(10003,103)],"scene/qzmj24.json");
         this._battles[this._battles.length] = new MissionBattleVO(2303,8558,29000,[new BaseRewardVO(10000,4400),new BaseRewardVO(10027,1)],[new NeedVO(10000,4300),new NeedVO(10004,104)],"scene/jzmj38-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2304,8587,29000,[new BaseRewardVO(10000,4400),new BaseRewardVO(10405,1)],[new NeedVO(10000,4300),new NeedVO(10005,105)],"scene/qzmj26.json");
         this._battles[this._battles.length] = new MissionBattleVO(2305,8616,29000,[new BaseRewardVO(10000,4400),new BaseRewardVO(10028,1)],[new NeedVO(10000,4300),new NeedVO(10006,106)],"scene/jzmj40-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2307,8645,29000,[new BaseRewardVO(10000,4400),new BaseRewardVO(10404,1)],[new NeedVO(10000,4300),new NeedVO(10007,107)],"scene/qzmj28.json");
         this._battles[this._battles.length] = new MissionBattleVO(2308,8674,29000,[new BaseRewardVO(10000,4400),new BaseRewardVO(10029,1)],[new NeedVO(10000,4300),new NeedVO(10003,108)],"scene/jzmj42-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2309,8703,29000,[new BaseRewardVO(10000,4400),new BaseRewardVO(10406,1)],[new NeedVO(10000,4300),new NeedVO(10004,109)],"scene/qzmj30.json");
         this._battles[this._battles.length] = new MissionBattleVO(2310,8732,29000,[new BaseRewardVO(10000,4400),new BaseRewardVO(10027,1)],[new NeedVO(10000,4300),new NeedVO(10005,110)],"scene/jzmj44-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2312,8761,29000,[new BaseRewardVO(10000,4400),new BaseRewardVO(10404,1)],[new NeedVO(10000,4300),new NeedVO(10006,111)],"scene/qzmj32.json");
         this._battles[this._battles.length] = new MissionBattleVO(2313,8790,29000,[new BaseRewardVO(10000,4400),new BaseRewardVO(10028,1)],[new NeedVO(10000,4300),new NeedVO(10007,112)],"scene/jzmj46-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2314,8829,29000,[new BaseRewardVO(10000,4400),new BaseRewardVO(10407,1)],[new NeedVO(10000,4300),new NeedVO(10003,113)],"scene/qzmj34.json");
         this._battles[this._battles.length] = new MissionBattleVO(2315,8858,29000,[new BaseRewardVO(10000,4400),new BaseRewardVO(10029,1)],[new NeedVO(10000,4300),new NeedVO(10004,114)],"scene/jzmj48-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2317,8888,31000,[new BaseRewardVO(10000,4500),new BaseRewardVO(10404,1)],[new NeedVO(10000,4400),new NeedVO(10003,115)],"scene/qzmj25.json");
         this._battles[this._battles.length] = new MissionBattleVO(2318,8888,31000,[new BaseRewardVO(10000,4500),new BaseRewardVO(10027,1)],[new NeedVO(10000,4400),new NeedVO(10004,116)],"scene/jzmj37-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2319,8888,31000,[new BaseRewardVO(10000,4500),new BaseRewardVO(10405,1)],[new NeedVO(10000,4400),new NeedVO(10005,117)],"scene/qzmj27.json");
         this._battles[this._battles.length] = new MissionBattleVO(2320,8888,31000,[new BaseRewardVO(10000,4500),new BaseRewardVO(10028,1)],[new NeedVO(10000,4400),new NeedVO(10006,118)],"scene/jzmj39-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2322,8888,31000,[new BaseRewardVO(10000,4500),new BaseRewardVO(10404,1)],[new NeedVO(10000,4400),new NeedVO(10007,119)],"scene/qzmj29.json");
         this._battles[this._battles.length] = new MissionBattleVO(2323,8888,31000,[new BaseRewardVO(10000,4500),new BaseRewardVO(10029,1)],[new NeedVO(10000,4400),new NeedVO(10003,120)],"scene/jzmj41-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2324,8888,31000,[new BaseRewardVO(10000,4500),new BaseRewardVO(10406,1)],[new NeedVO(10000,4400),new NeedVO(10004,121)],"scene/qzmj31.json");
         this._battles[this._battles.length] = new MissionBattleVO(2325,8888,31000,[new BaseRewardVO(10000,4500),new BaseRewardVO(10027,1)],[new NeedVO(10000,4400),new NeedVO(10005,122)],"scene/jzmj43-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2327,8888,31000,[new BaseRewardVO(10000,4500),new BaseRewardVO(10404,1)],[new NeedVO(10000,4400),new NeedVO(10006,123)],"scene/qzmj33.json");
         this._battles[this._battles.length] = new MissionBattleVO(2328,8888,31000,[new BaseRewardVO(10000,4500),new BaseRewardVO(10028,1)],[new NeedVO(10000,4400),new NeedVO(10007,124)],"scene/jzmj45-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2329,8888,31000,[new BaseRewardVO(10000,4500),new BaseRewardVO(10407,1)],[new NeedVO(10000,4400),new NeedVO(10003,125)],"scene/qzmj35.json");
         this._battles[this._battles.length] = new MissionBattleVO(2330,8888,31000,[new BaseRewardVO(10000,4500),new BaseRewardVO(10029,1)],[new NeedVO(10000,4400),new NeedVO(10004,126)],"scene/jzmj47-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2332,9000,33000,[new BaseRewardVO(10000,4600),new BaseRewardVO(10404,1)],[new NeedVO(10000,4500),new NeedVO(10003,127)],"scene/jzmj37-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2333,9000,33000,[new BaseRewardVO(10000,4600),new BaseRewardVO(10027,1)],[new NeedVO(10000,4500),new NeedVO(10004,128)],"scene/yzmj12-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2334,9000,33000,[new BaseRewardVO(10000,4600),new BaseRewardVO(10301,1)],[new NeedVO(10000,4500),new NeedVO(10005,129)],"scene/jzmj39-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2335,9000,33000,[new BaseRewardVO(10000,4600),new BaseRewardVO(10028,1)],[new NeedVO(10000,4500),new NeedVO(10006,130)],"scene/yzmj14-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2337,9000,33000,[new BaseRewardVO(10000,4600),new BaseRewardVO(10404,1)],[new NeedVO(10000,4500),new NeedVO(10007,131)],"scene/jzmj41-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2338,9000,33000,[new BaseRewardVO(10000,4600),new BaseRewardVO(10029,1)],[new NeedVO(10000,4500),new NeedVO(10003,132)],"scene/yzmj16-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2339,9000,33000,[new BaseRewardVO(10000,4600),new BaseRewardVO(10301,1)],[new NeedVO(10000,4500),new NeedVO(10004,133)],"scene/jzmj43-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2340,9000,33000,[new BaseRewardVO(10000,4600),new BaseRewardVO(10027,1)],[new NeedVO(10000,4500),new NeedVO(10005,134)],"scene/yzmj18-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2342,9000,33000,[new BaseRewardVO(10000,4600),new BaseRewardVO(10404,1)],[new NeedVO(10000,4500),new NeedVO(10006,135)],"scene/jzmj45-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2343,9000,33000,[new BaseRewardVO(10000,4600),new BaseRewardVO(10028,1)],[new NeedVO(10000,4500),new NeedVO(10007,136)],"scene/yzmj20-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2344,9000,33000,[new BaseRewardVO(10000,4600),new BaseRewardVO(10301,1)],[new NeedVO(10000,4500),new NeedVO(10003,137)],"scene/jzmj47-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2345,9000,33000,[new BaseRewardVO(10000,4600),new BaseRewardVO(10029,1)],[new NeedVO(10000,4500),new NeedVO(10004,138)],"scene/yzmj22-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2347,9500,35000,[new BaseRewardVO(10000,4700),new BaseRewardVO(10404,1)],[new NeedVO(10000,4600),new NeedVO(10003,139)],"scene/yzmj11-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2348,9500,35000,[new BaseRewardVO(10000,4700),new BaseRewardVO(10027,1)],[new NeedVO(10000,4600),new NeedVO(10004,140)],"scene/qzmj24.json");
         this._battles[this._battles.length] = new MissionBattleVO(2349,9500,35000,[new BaseRewardVO(10000,4700),new BaseRewardVO(10301,1)],[new NeedVO(10000,4600),new NeedVO(10005,141)],"scene/yzmj13-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2350,9500,35000,[new BaseRewardVO(10000,4700),new BaseRewardVO(10028,1)],[new NeedVO(10000,4600),new NeedVO(10006,142)],"scene/qzmj26.json");
         this._battles[this._battles.length] = new MissionBattleVO(2352,9500,35000,[new BaseRewardVO(10000,4700),new BaseRewardVO(10404,1)],[new NeedVO(10000,4600),new NeedVO(10007,143)],"scene/yzmj15-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2353,9500,35000,[new BaseRewardVO(10000,4700),new BaseRewardVO(10029,1)],[new NeedVO(10000,4600),new NeedVO(10003,144)],"scene/qzmj28.json");
         this._battles[this._battles.length] = new MissionBattleVO(2354,9500,35000,[new BaseRewardVO(10000,4700),new BaseRewardVO(10301,1)],[new NeedVO(10000,4600),new NeedVO(10004,145)],"scene/yzmj17-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2355,9500,35000,[new BaseRewardVO(10000,4700),new BaseRewardVO(10027,1)],[new NeedVO(10000,4600),new NeedVO(10005,146)],"scene/qzmj30.json");
         this._battles[this._battles.length] = new MissionBattleVO(2357,9500,35000,[new BaseRewardVO(10000,4700),new BaseRewardVO(10404,1)],[new NeedVO(10000,4600),new NeedVO(10006,147)],"scene/yzmj19-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2358,9500,35000,[new BaseRewardVO(10000,4700),new BaseRewardVO(10028,1)],[new NeedVO(10000,4600),new NeedVO(10007,148)],"scene/qzmj32.json");
         this._battles[this._battles.length] = new MissionBattleVO(2359,9500,35000,[new BaseRewardVO(10000,4700),new BaseRewardVO(10301,1)],[new NeedVO(10000,4600),new NeedVO(10003,149)],"scene/yzmj21-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2360,9500,35000,[new BaseRewardVO(10000,4700),new BaseRewardVO(10029,1)],[new NeedVO(10000,4600),new NeedVO(10004,150)],"scene/qzmj34.json");
         this._battles[this._battles.length] = new MissionBattleVO(2362,10000,40000,[new BaseRewardVO(10000,4800),new BaseRewardVO(10404,1)],[new NeedVO(10000,4700),new NeedVO(10003,151)],"scene/yzmj11-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2363,10000,40000,[new BaseRewardVO(10000,4800),new BaseRewardVO(10027,1)],[new NeedVO(10000,4700),new NeedVO(10004,152)],"scene/yzmj12-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2364,10000,40000,[new BaseRewardVO(10000,4800),new BaseRewardVO(10301,1)],[new NeedVO(10000,4700),new NeedVO(10005,153)],"scene/yzmj13-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2365,10000,40000,[new BaseRewardVO(10000,4800),new BaseRewardVO(10028,1)],[new NeedVO(10000,4700),new NeedVO(10006,154)],"scene/yzmj14-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2367,10000,40000,[new BaseRewardVO(10000,4800),new BaseRewardVO(10404,1)],[new NeedVO(10000,4700),new NeedVO(10007,155)],"scene/yzmj15-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2368,10000,40000,[new BaseRewardVO(10000,4800),new BaseRewardVO(10029,1)],[new NeedVO(10000,4700),new NeedVO(10003,156)],"scene/yzmj16-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2369,10000,40000,[new BaseRewardVO(10000,4800),new BaseRewardVO(10301,1)],[new NeedVO(10000,4700),new NeedVO(10004,157)],"scene/yzmj17-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2370,10000,40000,[new BaseRewardVO(10000,4800),new BaseRewardVO(10027,1)],[new NeedVO(10000,4700),new NeedVO(10005,158)],"scene/yzmj18-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2372,10000,40000,[new BaseRewardVO(10000,4800),new BaseRewardVO(10404,1)],[new NeedVO(10000,4700),new NeedVO(10006,159)],"scene/yzmj19-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2373,10000,40000,[new BaseRewardVO(10000,4800),new BaseRewardVO(10028,1)],[new NeedVO(10000,4700),new NeedVO(10007,160)],"scene/yzmj20-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2374,10000,40000,[new BaseRewardVO(10000,4800),new BaseRewardVO(10301,1)],[new NeedVO(10000,4700),new NeedVO(10003,161)],"scene/yzmj21-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2375,10000,40000,[new BaseRewardVO(10000,4800),new BaseRewardVO(10029,1)],[new NeedVO(10000,4700),new NeedVO(10004,162)],"scene/yzmj22-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2377,10500,45000,[new BaseRewardVO(10000,4900),new BaseRewardVO(10404,1)],[new NeedVO(10000,4800),new NeedVO(10003,163)],"scene/yzmj12-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2378,10500,45000,[new BaseRewardVO(10000,4900),new BaseRewardVO(10027,1)],[new NeedVO(10000,4800),new NeedVO(10004,164)],"scene/qzmj25.json");
         this._battles[this._battles.length] = new MissionBattleVO(2379,10500,45000,[new BaseRewardVO(10000,4900),new BaseRewardVO(10301,1)],[new NeedVO(10000,4800),new NeedVO(10005,165)],"scene/yzmj14-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2381,10500,45000,[new BaseRewardVO(10000,4900),new BaseRewardVO(10028,1)],[new NeedVO(10000,4800),new NeedVO(10006,166)],"scene/qzmj27.json");
         this._battles[this._battles.length] = new MissionBattleVO(2382,10500,45000,[new BaseRewardVO(10000,4900),new BaseRewardVO(10404,1)],[new NeedVO(10000,4800),new NeedVO(10007,167)],"scene/yzmj16-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2383,10500,45000,[new BaseRewardVO(10000,4900),new BaseRewardVO(10029,1)],[new NeedVO(10000,4800),new NeedVO(10003,168)],"scene/qzmj29.json");
         this._battles[this._battles.length] = new MissionBattleVO(2385,10500,45000,[new BaseRewardVO(10000,4900),new BaseRewardVO(10301,1)],[new NeedVO(10000,4800),new NeedVO(10004,169)],"scene/yzmj18-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2386,10500,45000,[new BaseRewardVO(10000,4900),new BaseRewardVO(10027,1)],[new NeedVO(10000,4800),new NeedVO(10005,170)],"scene/qzmj31.json");
         this._battles[this._battles.length] = new MissionBattleVO(2387,10500,45000,[new BaseRewardVO(10000,4900),new BaseRewardVO(10404,1)],[new NeedVO(10000,4800),new NeedVO(10006,171)],"scene/yzmj20-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2389,11000,50000,[new BaseRewardVO(10000,5000),new BaseRewardVO(10404,1)],[new NeedVO(10000,4900),new NeedVO(10003,172)],"scene/yzmj11-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2390,11000,50000,[new BaseRewardVO(10000,5000),new BaseRewardVO(10027,1)],[new NeedVO(10000,4900),new NeedVO(10004,173)],"scene/qzmj24.json");
         this._battles[this._battles.length] = new MissionBattleVO(2391,11000,50000,[new BaseRewardVO(10000,5000),new BaseRewardVO(10301,1)],[new NeedVO(10000,4900),new NeedVO(10005,174)],"scene/yzmj13-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2393,11000,50000,[new BaseRewardVO(10000,5000),new BaseRewardVO(10028,1)],[new NeedVO(10000,4900),new NeedVO(10006,175)],"scene/qzmj26.json");
         this._battles[this._battles.length] = new MissionBattleVO(2394,11000,50000,[new BaseRewardVO(10000,5000),new BaseRewardVO(10404,1)],[new NeedVO(10000,4900),new NeedVO(10007,176)],"scene/yzmj15-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2395,11000,50000,[new BaseRewardVO(10000,5000),new BaseRewardVO(10029,1)],[new NeedVO(10000,4900),new NeedVO(10003,177)],"scene/qzmj28.json");
         this._battles[this._battles.length] = new MissionBattleVO(2397,11000,50000,[new BaseRewardVO(10000,5000),new BaseRewardVO(10301,1)],[new NeedVO(10000,4900),new NeedVO(10004,178)],"scene/yzmj17-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2398,11000,50000,[new BaseRewardVO(10000,5000),new BaseRewardVO(10027,1)],[new NeedVO(10000,4900),new NeedVO(10005,179)],"scene/qzmj30.json");
         this._battles[this._battles.length] = new MissionBattleVO(2399,11000,50000,[new BaseRewardVO(10000,5000),new BaseRewardVO(10404,1)],[new NeedVO(10000,4900),new NeedVO(10006,180)],"scene/yzmj19-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2401,11500,55000,[new BaseRewardVO(10000,5100),new BaseRewardVO(10028,1)],[new NeedVO(10000,5000),new NeedVO(10006,181)],"scene/jzmj39-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2402,11500,55000,[new BaseRewardVO(10000,5100),new BaseRewardVO(10404,1)],[new NeedVO(10000,5000),new NeedVO(10007,182)],"scene/qzmj29.json");
         this._battles[this._battles.length] = new MissionBattleVO(2403,11500,55000,[new BaseRewardVO(10000,5100),new BaseRewardVO(10029,1)],[new NeedVO(10000,5000),new NeedVO(10003,183)],"scene/jzmj41-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2405,11500,55000,[new BaseRewardVO(10000,5100),new BaseRewardVO(10406,1)],[new NeedVO(10000,5000),new NeedVO(10004,184)],"scene/qzmj31.json");
         this._battles[this._battles.length] = new MissionBattleVO(2406,11500,55000,[new BaseRewardVO(10000,5100),new BaseRewardVO(10027,1)],[new NeedVO(10000,5000),new NeedVO(10005,185)],"scene/jzmj43-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2407,11500,55000,[new BaseRewardVO(10000,5100),new BaseRewardVO(10404,1)],[new NeedVO(10000,5000),new NeedVO(10006,186)],"scene/qzmj33.json");
         this._battles[this._battles.length] = new MissionBattleVO(2409,11500,55000,[new BaseRewardVO(10000,5100),new BaseRewardVO(10028,1)],[new NeedVO(10000,5000),new NeedVO(10007,187)],"scene/jzmj45-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2410,11500,55000,[new BaseRewardVO(10000,5100),new BaseRewardVO(10407,1)],[new NeedVO(10000,5000),new NeedVO(10003,188)],"scene/qzmj35.json");
         this._battles[this._battles.length] = new MissionBattleVO(2411,11500,55000,[new BaseRewardVO(10000,5100),new BaseRewardVO(10029,1)],[new NeedVO(10000,5000),new NeedVO(10004,189)],"scene/jzmj47-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2413,12000,60000,[new BaseRewardVO(10000,5200),new BaseRewardVO(10404,1)],[new NeedVO(10000,5100),new NeedVO(10003,190)],"scene/jzmj37-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2414,12000,60000,[new BaseRewardVO(10000,5200),new BaseRewardVO(10027,1)],[new NeedVO(10000,5100),new NeedVO(10004,191)],"scene/jzmj38-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2415,12000,60000,[new BaseRewardVO(10000,5200),new BaseRewardVO(10405,1)],[new NeedVO(10000,5100),new NeedVO(10005,192)],"scene/jzmj39-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2417,12000,60000,[new BaseRewardVO(10000,5200),new BaseRewardVO(10028,1)],[new NeedVO(10000,5100),new NeedVO(10006,193)],"scene/jzmj40-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2418,12000,60000,[new BaseRewardVO(10000,5200),new BaseRewardVO(10404,1)],[new NeedVO(10000,5100),new NeedVO(10007,194)],"scene/jzmj41-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2419,12000,60000,[new BaseRewardVO(10000,5200),new BaseRewardVO(10029,1)],[new NeedVO(10000,5100),new NeedVO(10003,195)],"scene/jzmj42-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2421,12000,60000,[new BaseRewardVO(10000,5200),new BaseRewardVO(10406,1)],[new NeedVO(10000,5100),new NeedVO(10004,196)],"scene/jzmj43-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2422,12000,60000,[new BaseRewardVO(10000,5200),new BaseRewardVO(10027,1)],[new NeedVO(10000,5100),new NeedVO(10005,197)],"scene/jzmj44-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2423,12000,60000,[new BaseRewardVO(10000,5200),new BaseRewardVO(10404,1)],[new NeedVO(10000,5100),new NeedVO(10006,198)],"scene/jzmj45-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2425,12500,65000,[new BaseRewardVO(10000,5300),new BaseRewardVO(10404,1)],[new NeedVO(10000,5200),new NeedVO(10003,199)],"scene/yzmj11-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2426,12500,65000,[new BaseRewardVO(10000,5300),new BaseRewardVO(10027,1)],[new NeedVO(10000,5200),new NeedVO(10004,200)],"scene/qzmj24.json");
         this._battles[this._battles.length] = new MissionBattleVO(2427,12500,65000,[new BaseRewardVO(10000,5300),new BaseRewardVO(10301,1)],[new NeedVO(10000,5200),new NeedVO(10005,201)],"scene/yzmj13-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2429,12500,65000,[new BaseRewardVO(10000,5300),new BaseRewardVO(10028,1)],[new NeedVO(10000,5200),new NeedVO(10006,202)],"scene/qzmj26.json");
         this._battles[this._battles.length] = new MissionBattleVO(2430,12500,65000,[new BaseRewardVO(10000,5300),new BaseRewardVO(10404,1)],[new NeedVO(10000,5200),new NeedVO(10007,203)],"scene/yzmj15-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2431,12500,65000,[new BaseRewardVO(10000,5300),new BaseRewardVO(10029,1)],[new NeedVO(10000,5200),new NeedVO(10003,204)],"scene/qzmj28.json");
         this._battles[this._battles.length] = new MissionBattleVO(2433,12500,65000,[new BaseRewardVO(10000,5300),new BaseRewardVO(10301,1)],[new NeedVO(10000,5200),new NeedVO(10004,205)],"scene/yzmj17-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2434,12500,65000,[new BaseRewardVO(10000,5300),new BaseRewardVO(10027,1)],[new NeedVO(10000,5200),new NeedVO(10005,206)],"scene/qzmj30.json");
         this._battles[this._battles.length] = new MissionBattleVO(2435,12500,65000,[new BaseRewardVO(10000,5300),new BaseRewardVO(10404,1)],[new NeedVO(10000,5200),new NeedVO(10006,207)],"scene/yzmj19-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2437,13000,70000,[new BaseRewardVO(10000,5400),new BaseRewardVO(10404,1)],[new NeedVO(10000,5300),new NeedVO(10003,208)],"scene/qzmj25.json");
         this._battles[this._battles.length] = new MissionBattleVO(2438,13000,70000,[new BaseRewardVO(10000,5400),new BaseRewardVO(10027,1)],[new NeedVO(10000,5300),new NeedVO(10004,209)],"scene/jzmj37-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2439,13000,70000,[new BaseRewardVO(10000,5400),new BaseRewardVO(10405,1)],[new NeedVO(10000,5300),new NeedVO(10005,210)],"scene/qzmj27.json");
         this._battles[this._battles.length] = new MissionBattleVO(2441,13000,70000,[new BaseRewardVO(10000,5400),new BaseRewardVO(10028,1)],[new NeedVO(10000,5300),new NeedVO(10006,211)],"scene/jzmj39-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2442,13000,70000,[new BaseRewardVO(10000,5400),new BaseRewardVO(10404,1)],[new NeedVO(10000,5300),new NeedVO(10007,212)],"scene/qzmj29.json");
         this._battles[this._battles.length] = new MissionBattleVO(2443,13000,70000,[new BaseRewardVO(10000,5400),new BaseRewardVO(10029,1)],[new NeedVO(10000,5300),new NeedVO(10003,213)],"scene/jzmj41-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2445,13000,70000,[new BaseRewardVO(10000,5400),new BaseRewardVO(10406,1)],[new NeedVO(10000,5300),new NeedVO(10004,214)],"scene/qzmj31.json");
         this._battles[this._battles.length] = new MissionBattleVO(2446,13000,70000,[new BaseRewardVO(10000,5400),new BaseRewardVO(10027,1)],[new NeedVO(10000,5300),new NeedVO(10005,215)],"scene/jzmj43-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2447,13000,70000,[new BaseRewardVO(10000,5400),new BaseRewardVO(10404,1)],[new NeedVO(10000,5300),new NeedVO(10006,216)],"scene/qzmj33.json");
         this._battles[this._battles.length] = new MissionBattleVO(2449,13500,75000,[new BaseRewardVO(10000,5500),new BaseRewardVO(10027,1)],[new NeedVO(10000,5400),new NeedVO(10004,218)],"scene/jzmj37-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2450,13500,75000,[new BaseRewardVO(10000,5500),new BaseRewardVO(10405,1)],[new NeedVO(10000,5400),new NeedVO(10005,219)],"scene/qzmj27.json");
         this._battles[this._battles.length] = new MissionBattleVO(2451,13500,75000,[new BaseRewardVO(10000,5500),new BaseRewardVO(10028,1)],[new NeedVO(10000,5400),new NeedVO(10006,220)],"scene/jzmj39-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2453,13500,75000,[new BaseRewardVO(10000,5500),new BaseRewardVO(10029,1)],[new NeedVO(10000,5400),new NeedVO(10007,221)],"scene/jzmj41-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2454,13500,75000,[new BaseRewardVO(10000,5500),new BaseRewardVO(10406,1)],[new NeedVO(10000,5400),new NeedVO(10003,222)],"scene/qzmj31.json");
         this._battles[this._battles.length] = new MissionBattleVO(2455,13500,75000,[new BaseRewardVO(10000,5500),new BaseRewardVO(10027,1)],[new NeedVO(10000,5400),new NeedVO(10004,223)],"scene/jzmj43-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2457,13500,75000,[new BaseRewardVO(10000,5500),new BaseRewardVO(10028,1)],[new NeedVO(10000,5400),new NeedVO(10005,224)],"scene/jzmj45-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2458,13500,75000,[new BaseRewardVO(10000,5500),new BaseRewardVO(10407,1)],[new NeedVO(10000,5400),new NeedVO(10006,225)],"scene/qzmj35.json");
         this._battles[this._battles.length] = new MissionBattleVO(2459,13500,75000,[new BaseRewardVO(10000,5500),new BaseRewardVO(10029,1)],[new NeedVO(10000,5400),new NeedVO(10007,226)],"scene/jzmj47-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2461,14000,80000,[new BaseRewardVO(10000,5600),new BaseRewardVO(10404,1)],[new NeedVO(10000,5500),new NeedVO(10003,227)],"scene/yzmj11-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2462,14000,80000,[new BaseRewardVO(10000,5600),new BaseRewardVO(10027,1)],[new NeedVO(10000,5500),new NeedVO(10004,228)],"scene/qzmj24.json");
         this._battles[this._battles.length] = new MissionBattleVO(2463,14000,80000,[new BaseRewardVO(10000,5600),new BaseRewardVO(10301,1)],[new NeedVO(10000,5500),new NeedVO(10005,229)],"scene/yzmj13-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2465,14000,80000,[new BaseRewardVO(10000,5600),new BaseRewardVO(10028,1)],[new NeedVO(10000,5500),new NeedVO(10006,230)],"scene/qzmj26.json");
         this._battles[this._battles.length] = new MissionBattleVO(2466,14000,80000,[new BaseRewardVO(10000,5600),new BaseRewardVO(10404,1)],[new NeedVO(10000,5500),new NeedVO(10007,231)],"scene/yzmj15-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2467,14000,80000,[new BaseRewardVO(10000,5600),new BaseRewardVO(10029,1)],[new NeedVO(10000,5500),new NeedVO(10003,232)],"scene/qzmj28.json");
         this._battles[this._battles.length] = new MissionBattleVO(2469,14000,80000,[new BaseRewardVO(10000,5600),new BaseRewardVO(10301,1)],[new NeedVO(10000,5500),new NeedVO(10004,233)],"scene/yzmj17-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2470,14000,80000,[new BaseRewardVO(10000,5600),new BaseRewardVO(10027,1)],[new NeedVO(10000,5500),new NeedVO(10005,234)],"scene/qzmj30.json");
         this._battles[this._battles.length] = new MissionBattleVO(2471,14000,80000,[new BaseRewardVO(10000,5600),new BaseRewardVO(10404,1)],[new NeedVO(10000,5500),new NeedVO(10006,235)],"scene/yzmj19-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2473,14500,85000,[new BaseRewardVO(10000,5700),new BaseRewardVO(10404,1)],[new NeedVO(10000,5600),new NeedVO(10003,236)],"scene/jzmj37-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2474,14500,85000,[new BaseRewardVO(10000,5700),new BaseRewardVO(10027,1)],[new NeedVO(10000,5600),new NeedVO(10004,237)],"scene/yzmj12-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2475,14500,85000,[new BaseRewardVO(10000,5700),new BaseRewardVO(10301,1)],[new NeedVO(10000,5600),new NeedVO(10005,238)],"scene/jzmj39-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2477,14500,85000,[new BaseRewardVO(10000,5700),new BaseRewardVO(10028,1)],[new NeedVO(10000,5600),new NeedVO(10006,239)],"scene/yzmj14-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2478,14500,85000,[new BaseRewardVO(10000,5700),new BaseRewardVO(10404,1)],[new NeedVO(10000,5600),new NeedVO(10007,240)],"scene/jzmj41-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2479,14500,85000,[new BaseRewardVO(10000,5700),new BaseRewardVO(10029,1)],[new NeedVO(10000,5600),new NeedVO(10003,241)],"scene/yzmj16-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2481,14500,85000,[new BaseRewardVO(10000,5700),new BaseRewardVO(10301,1)],[new NeedVO(10000,5600),new NeedVO(10004,242)],"scene/jzmj43-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2482,14500,85000,[new BaseRewardVO(10000,5700),new BaseRewardVO(10027,1)],[new NeedVO(10000,5600),new NeedVO(10005,243)],"scene/yzmj18-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2483,14500,85000,[new BaseRewardVO(10000,5700),new BaseRewardVO(10404,1)],[new NeedVO(10000,5600),new NeedVO(10006,244)],"scene/jzmj45-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2485,15000,86000,[new BaseRewardVO(10000,5800),new BaseRewardVO(10404,1)],[new NeedVO(10000,5700),new NeedVO(10003,245)],"scene/yzmj12-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2486,15000,86000,[new BaseRewardVO(10000,5800),new BaseRewardVO(10027,1)],[new NeedVO(10000,5700),new NeedVO(10004,246)],"scene/qzmj25.json");
         this._battles[this._battles.length] = new MissionBattleVO(2487,15000,86000,[new BaseRewardVO(10000,5800),new BaseRewardVO(10301,1)],[new NeedVO(10000,5700),new NeedVO(10005,247)],"scene/yzmj14-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2489,15000,86000,[new BaseRewardVO(10000,5800),new BaseRewardVO(10028,1)],[new NeedVO(10000,5700),new NeedVO(10006,248)],"scene/qzmj27.json");
         this._battles[this._battles.length] = new MissionBattleVO(2490,15000,86000,[new BaseRewardVO(10000,5800),new BaseRewardVO(10404,1)],[new NeedVO(10000,5700),new NeedVO(10007,249)],"scene/yzmj16-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2491,15000,86000,[new BaseRewardVO(10000,5800),new BaseRewardVO(10029,1)],[new NeedVO(10000,5700),new NeedVO(10003,250)],"scene/qzmj29.json");
         this._battles[this._battles.length] = new MissionBattleVO(2493,15000,86000,[new BaseRewardVO(10000,5800),new BaseRewardVO(10301,1)],[new NeedVO(10000,5700),new NeedVO(10004,251)],"scene/yzmj18-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2494,15000,86000,[new BaseRewardVO(10000,5800),new BaseRewardVO(10027,1)],[new NeedVO(10000,5700),new NeedVO(10005,252)],"scene/qzmj31.json");
         this._battles[this._battles.length] = new MissionBattleVO(2495,15000,86000,[new BaseRewardVO(10000,5800),new BaseRewardVO(10404,1)],[new NeedVO(10000,5700),new NeedVO(10006,253)],"scene/yzmj20-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2497,15500,87000,[new BaseRewardVO(10000,5900),new BaseRewardVO(10404,1)],[new NeedVO(10000,5800),new NeedVO(10003,253)],"scene/jzmj41-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2498,15500,87000,[new BaseRewardVO(10000,5900),new BaseRewardVO(10027,1)],[new NeedVO(10000,5800),new NeedVO(10004,254)],"scene/qzmj31.json");
         this._battles[this._battles.length] = new MissionBattleVO(2499,15500,87000,[new BaseRewardVO(10000,5900),new BaseRewardVO(10301,1)],[new NeedVO(10000,5800),new NeedVO(10005,255)],"scene/jzmj43-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2501,15500,87000,[new BaseRewardVO(10000,5900),new BaseRewardVO(10028,1)],[new NeedVO(10000,5800),new NeedVO(10006,256)],"scene/qzmj33.json");
         this._battles[this._battles.length] = new MissionBattleVO(2502,15500,87000,[new BaseRewardVO(10000,5900),new BaseRewardVO(10404,1)],[new NeedVO(10000,5800),new NeedVO(10007,257)],"scene/jzmj45-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2503,15500,87000,[new BaseRewardVO(10000,5900),new BaseRewardVO(10029,1)],[new NeedVO(10000,5800),new NeedVO(10003,258)],"scene/qzmj35.json");
         this._battles[this._battles.length] = new MissionBattleVO(2505,15500,87000,[new BaseRewardVO(10000,5900),new BaseRewardVO(10029,1)],[new NeedVO(10000,5800),new NeedVO(10004,259)],"scene/jzmj47-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2506,15500,87000,[new BaseRewardVO(10000,5900),new BaseRewardVO(10301,1)],[new NeedVO(10000,5800),new NeedVO(10005,260)],"scene/jzmj37-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2507,15500,87000,[new BaseRewardVO(10000,5900),new BaseRewardVO(10027,1)],[new NeedVO(10000,5800),new NeedVO(10006,261)],"scene/yzmj12-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2509,16000,88000,[new BaseRewardVO(10000,6000),new BaseRewardVO(10028,1)],[new NeedVO(10000,5900),new NeedVO(10006,262)],"scene/yzmj14-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2510,16000,88000,[new BaseRewardVO(10000,6000),new BaseRewardVO(10404,1)],[new NeedVO(10000,5900),new NeedVO(10007,263)],"scene/yzmj15-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2511,16000,88000,[new BaseRewardVO(10000,6000),new BaseRewardVO(10029,1)],[new NeedVO(10000,5900),new NeedVO(10003,264)],"scene/yzmj16-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2513,16000,88000,[new BaseRewardVO(10000,6000),new BaseRewardVO(10301,1)],[new NeedVO(10000,5900),new NeedVO(10004,265)],"scene/yzmj17-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2514,16000,88000,[new BaseRewardVO(10000,6000),new BaseRewardVO(10027,1)],[new NeedVO(10000,5900),new NeedVO(10005,266)],"scene/yzmj18-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2515,16000,88000,[new BaseRewardVO(10000,6000),new BaseRewardVO(10404,1)],[new NeedVO(10000,5900),new NeedVO(10006,267)],"scene/yzmj19-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2517,16000,88000,[new BaseRewardVO(10000,6000),new BaseRewardVO(10028,1)],[new NeedVO(10000,5900),new NeedVO(10007,268)],"scene/yzmj20-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2518,16000,88000,[new BaseRewardVO(10000,6000),new BaseRewardVO(10301,1)],[new NeedVO(10000,5900),new NeedVO(10003,269)],"scene/yzmj21-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2519,16000,88000,[new BaseRewardVO(10000,6000),new BaseRewardVO(10029,1)],[new NeedVO(10000,5900),new NeedVO(10004,270)],"scene/yzmj22-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2521,16500,89000,[new BaseRewardVO(10000,6100),new BaseRewardVO(10404,1)],[new NeedVO(10000,6000),new NeedVO(10006,271)],"scene/jzmj37-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2522,16500,89000,[new BaseRewardVO(10000,6100),new BaseRewardVO(10027,1)],[new NeedVO(10000,6000),new NeedVO(10007,272)],"scene/jzmj38-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2523,16500,89000,[new BaseRewardVO(10000,6100),new BaseRewardVO(10405,1)],[new NeedVO(10000,6000),new NeedVO(10006,273)],"scene/jzmj39-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2525,16500,89000,[new BaseRewardVO(10000,6100),new BaseRewardVO(10028,1)],[new NeedVO(10000,6000),new NeedVO(10007,274)],"scene/jzmj40-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2526,16500,89000,[new BaseRewardVO(10000,6100),new BaseRewardVO(10404,1)],[new NeedVO(10000,6000),new NeedVO(10006,275)],"scene/jzmj41-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2527,16500,89000,[new BaseRewardVO(10000,6100),new BaseRewardVO(10029,1)],[new NeedVO(10000,6000),new NeedVO(10007,276)],"scene/jzmj42-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2529,16500,89000,[new BaseRewardVO(10000,6100),new BaseRewardVO(10406,1)],[new NeedVO(10000,6000),new NeedVO(10006,277)],"scene/jzmj43-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2530,16500,89000,[new BaseRewardVO(10000,6100),new BaseRewardVO(10027,1)],[new NeedVO(10000,6000),new NeedVO(10007,278)],"scene/jzmj44-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2531,16500,89000,[new BaseRewardVO(10000,6100),new BaseRewardVO(10404,1)],[new NeedVO(10000,6000),new NeedVO(10006,279)],"scene/jzmj45-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2533,17000,90000,[new BaseRewardVO(10000,6200),new BaseRewardVO(10404,1)],[new NeedVO(10000,6100),new NeedVO(10003,280)],"scene/qzmj25.json");
         this._battles[this._battles.length] = new MissionBattleVO(2534,17000,90000,[new BaseRewardVO(10000,6200),new BaseRewardVO(10027,1)],[new NeedVO(10000,6100),new NeedVO(10004,281)],"scene/jzmj37-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2535,17000,90000,[new BaseRewardVO(10000,6200),new BaseRewardVO(10405,1)],[new NeedVO(10000,6100),new NeedVO(10005,282)],"scene/qzmj27.json");
         this._battles[this._battles.length] = new MissionBattleVO(2537,17000,90000,[new BaseRewardVO(10000,6200),new BaseRewardVO(10028,1)],[new NeedVO(10000,6100),new NeedVO(10006,283)],"scene/jzmj39-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2538,17000,90000,[new BaseRewardVO(10000,6200),new BaseRewardVO(10404,1)],[new NeedVO(10000,6100),new NeedVO(10007,284)],"scene/qzmj29.json");
         this._battles[this._battles.length] = new MissionBattleVO(2539,17000,90000,[new BaseRewardVO(10000,6200),new BaseRewardVO(10029,1)],[new NeedVO(10000,6100),new NeedVO(10003,285)],"scene/jzmj41-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2541,17000,90000,[new BaseRewardVO(10000,6200),new BaseRewardVO(10406,1)],[new NeedVO(10000,6100),new NeedVO(10004,286)],"scene/qzmj31.json");
         this._battles[this._battles.length] = new MissionBattleVO(2542,17000,90000,[new BaseRewardVO(10000,6200),new BaseRewardVO(10027,1)],[new NeedVO(10000,6100),new NeedVO(10005,287)],"scene/jzmj43-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2543,17000,90000,[new BaseRewardVO(10000,6200),new BaseRewardVO(10404,1)],[new NeedVO(10000,6100),new NeedVO(10006,288)],"scene/qzmj33.json");
         this._battles[this._battles.length] = new MissionBattleVO(2545,17500,91000,[new BaseRewardVO(10000,6300),new BaseRewardVO(10404,1)],[new NeedVO(10000,6200),new NeedVO(10003,289)],"scene/yzmj11-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2546,17500,91000,[new BaseRewardVO(10000,6300),new BaseRewardVO(10027,1)],[new NeedVO(10000,6200),new NeedVO(10004,290)],"scene/qzmj24.json");
         this._battles[this._battles.length] = new MissionBattleVO(2547,17500,91000,[new BaseRewardVO(10000,6300),new BaseRewardVO(10301,1)],[new NeedVO(10000,6200),new NeedVO(10005,291)],"scene/yzmj13-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2549,17500,91000,[new BaseRewardVO(10000,6300),new BaseRewardVO(10028,1)],[new NeedVO(10000,6200),new NeedVO(10006,292)],"scene/qzmj26.json");
         this._battles[this._battles.length] = new MissionBattleVO(2550,17500,91000,[new BaseRewardVO(10000,6300),new BaseRewardVO(10404,1)],[new NeedVO(10000,6200),new NeedVO(10007,293)],"scene/yzmj15-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2551,17500,91000,[new BaseRewardVO(10000,6300),new BaseRewardVO(10029,1)],[new NeedVO(10000,6200),new NeedVO(10003,294)],"scene/qzmj28.json");
         this._battles[this._battles.length] = new MissionBattleVO(2553,17500,91000,[new BaseRewardVO(10000,6300),new BaseRewardVO(10301,1)],[new NeedVO(10000,6200),new NeedVO(10004,295)],"scene/yzmj17-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2554,17500,91000,[new BaseRewardVO(10000,6300),new BaseRewardVO(10027,1)],[new NeedVO(10000,6200),new NeedVO(10005,296)],"scene/qzmj30.json");
         this._battles[this._battles.length] = new MissionBattleVO(2555,17500,91000,[new BaseRewardVO(10000,6300),new BaseRewardVO(10404,1)],[new NeedVO(10000,6200),new NeedVO(10006,297)],"scene/yzmj19-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2557,18000,92000,[new BaseRewardVO(10000,6400),new BaseRewardVO(10404,1)],[new NeedVO(10000,6300),new NeedVO(10003,298)],"scene/jzmj37-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2558,18000,92000,[new BaseRewardVO(10000,6400),new BaseRewardVO(10027,1)],[new NeedVO(10000,6300),new NeedVO(10004,299)],"scene/jzmj38-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2560,18000,92000,[new BaseRewardVO(10000,6400),new BaseRewardVO(10405,1)],[new NeedVO(10000,6300),new NeedVO(10005,300)],"scene/jzmj39-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2561,18000,92000,[new BaseRewardVO(10000,6400),new BaseRewardVO(10028,1)],[new NeedVO(10000,6300),new NeedVO(10006,301)],"scene/jzmj40-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2563,18000,92000,[new BaseRewardVO(10000,6400),new BaseRewardVO(10404,1)],[new NeedVO(10000,6300),new NeedVO(10007,302)],"scene/jzmj41-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2564,18000,92000,[new BaseRewardVO(10000,6400),new BaseRewardVO(10029,1)],[new NeedVO(10000,6300),new NeedVO(10003,303)],"scene/jzmj42-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2566,18500,93000,[new BaseRewardVO(10000,6500),new BaseRewardVO(10404,1)],[new NeedVO(10000,6400),new NeedVO(10003,304)],"scene/yzmj17-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2567,18500,93000,[new BaseRewardVO(10000,6500),new BaseRewardVO(10027,1)],[new NeedVO(10000,6400),new NeedVO(10004,305)],"scene/yzmj18-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2569,18500,93000,[new BaseRewardVO(10000,6500),new BaseRewardVO(10405,1)],[new NeedVO(10000,6400),new NeedVO(10005,306)],"scene/yzmj19-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2570,18500,93000,[new BaseRewardVO(10000,6500),new BaseRewardVO(10028,1)],[new NeedVO(10000,6400),new NeedVO(10006,307)],"scene/yzmj20-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2572,18500,93000,[new BaseRewardVO(10000,6500),new BaseRewardVO(10404,1)],[new NeedVO(10000,6400),new NeedVO(10007,308)],"scene/yzmj21-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2573,18500,93000,[new BaseRewardVO(10000,6500),new BaseRewardVO(10029,1)],[new NeedVO(10000,6400),new NeedVO(10003,309)],"scene/yzmj22-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2575,19000,94000,[new BaseRewardVO(10000,6600),new BaseRewardVO(10028,1)],[new NeedVO(10000,6500),new NeedVO(10006,310)],"scene/yzmj14-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2576,19000,94000,[new BaseRewardVO(10000,6600),new BaseRewardVO(10404,1)],[new NeedVO(10000,6500),new NeedVO(10007,311)],"scene/jzmj41-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2578,19000,94000,[new BaseRewardVO(10000,6600),new BaseRewardVO(10029,1)],[new NeedVO(10000,6500),new NeedVO(10003,312)],"scene/yzmj16-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2579,19000,94000,[new BaseRewardVO(10000,6600),new BaseRewardVO(10301,1)],[new NeedVO(10000,6500),new NeedVO(10004,313)],"scene/jzmj43-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2581,19000,94000,[new BaseRewardVO(10000,6600),new BaseRewardVO(10027,1)],[new NeedVO(10000,6500),new NeedVO(10005,314)],"scene/yzmj18-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2582,19000,94000,[new BaseRewardVO(10000,6600),new BaseRewardVO(10404,1)],[new NeedVO(10000,6500),new NeedVO(10006,315)],"scene/jzmj45-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2584,20000,95000,[new BaseRewardVO(10000,6700),new BaseRewardVO(10404,1)],[new NeedVO(10000,6600),new NeedVO(10003,316)],"scene/jzmj37-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2585,20000,95000,[new BaseRewardVO(10000,6700),new BaseRewardVO(10027,1)],[new NeedVO(10000,6600),new NeedVO(10004,317)],"scene/yzmj12-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2587,20000,95000,[new BaseRewardVO(10000,6700),new BaseRewardVO(10301,1)],[new NeedVO(10000,6600),new NeedVO(10005,318)],"scene/jzmj39-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2588,20000,95000,[new BaseRewardVO(10000,6700),new BaseRewardVO(10028,1)],[new NeedVO(10000,6600),new NeedVO(10006,319)],"scene/yzmj14-120.json");
         this._battles[this._battles.length] = new MissionBattleVO(2590,20000,95000,[new BaseRewardVO(10000,3900),new BaseRewardVO(10404,1)],[new NeedVO(10000,6600),new NeedVO(10007,320)],"scene/jzmj41-230.json");
         this._battles[this._battles.length] = new MissionBattleVO(2591,20000,95000,[new BaseRewardVO(10000,6700),new BaseRewardVO(10029,1)],[new NeedVO(10000,6600),new NeedVO(10003,321)],"scene/yzmj16-120.json");
      }
      
      private function initWave() : void
      {
         this._dic = new Vector.<WaveDataVO>();
         this._dic[this._dic.length] = new SecretWave2001().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2002().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2004().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2005().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2007().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2008().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2010().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2011().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2013().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2014().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2016().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2017().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2018().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2019().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2020().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2021().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2022().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2024().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2025().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2026().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2027().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2028().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2029().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2031().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2032().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2033().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2034().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2036().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2037().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2038().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2039().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2041().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2042().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2043().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2044().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2045().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2047().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2048().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2049().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2050().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2052().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2053().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2054().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2055().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2057().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2058().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2059().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2060().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2062().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2063().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2064().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2065().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2067().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2068().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2069().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2070().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2072().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2073().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2074().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2075().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2077().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2078().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2079().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2080().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2082().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2083().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2084().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2085().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2087().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2088().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2089().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2090().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2092().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2093().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2094().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2095().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2097().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2098().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2099().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2100().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2102().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2103().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2104().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2105().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2107().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2108().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2110().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2109().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2112().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2113().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2114().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2115().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2117().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2118().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2119().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2120().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2122().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2123().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2124().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2125().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2127().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2128().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2129().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2130().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2132().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2133().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2134().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2135().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2137().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2138().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2139().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2140().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2142().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2143().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2144().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2145().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2147().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2148().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2149().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2150().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2152().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2153().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2154().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2155().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2157().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2158().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2159().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2160().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2162().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2163().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2164().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2165().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2167().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2168().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2169().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2170().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2172().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2173().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2174().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2175().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2177().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2178().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2179().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2180().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2182().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2183().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2184().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2185().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2187().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2188().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2189().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2190().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2192().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2193().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2194().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2195().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2197().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2198().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2199().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2200().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2202().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2203().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2204().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2205().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2207().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2208().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2209().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2210().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2212().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2213().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2214().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2215().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2217().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2218().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2219().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2220().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2222().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2223().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2224().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2225().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2227().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2228().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2229().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2230().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2232().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2233().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2234().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2235().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2237().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2238().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2239().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2240().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2242().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2243().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2244().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2245().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2247().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2248().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2249().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2250().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2252().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2253().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2254().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2255().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2257().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2258().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2259().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2260().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2262().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2263().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2264().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2265().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2267().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2268().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2269().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2270().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2272().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2273().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2274().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2275().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2277().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2278().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2279().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2280().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2282().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2283().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2284().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2285().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2287().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2288().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2289().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2290().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2292().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2293().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2294().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2295().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2297().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2298().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2299().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2300().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2302().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2303().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2304().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2305().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2307().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2308().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2309().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2310().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2312().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2313().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2314().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2315().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2317().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2318().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2319().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2320().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2322().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2323().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2324().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2325().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2327().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2328().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2329().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2330().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2332().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2333().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2334().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2335().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2337().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2338().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2339().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2340().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2342().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2343().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2344().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2345().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2347().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2348().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2349().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2350().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2352().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2353().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2354().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2355().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2357().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2358().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2359().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2360().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2362().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2363().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2364().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2365().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2367().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2368().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2369().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2370().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2372().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2373().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2374().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2375().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2377().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2378().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2379().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2381().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2382().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2383().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2385().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2386().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2387().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2389().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2390().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2391().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2393().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2394().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2395().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2397().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2398().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2399().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2401().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2402().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2403().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2405().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2406().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2407().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2409().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2410().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2411().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2413().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2414().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2415().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2417().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2418().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2419().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2421().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2422().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2423().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2425().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2426().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2427().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2429().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2430().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2431().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2433().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2434().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2435().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2437().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2438().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2439().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2441().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2442().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2443().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2445().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2446().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2447().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2449().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2450().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2451().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2453().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2454().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2455().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2457().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2458().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2459().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2461().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2462().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2463().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2465().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2466().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2467().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2469().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2470().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2471().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2473().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2474().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2475().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2477().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2478().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2479().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2481().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2482().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2483().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2485().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2486().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2487().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2489().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2490().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2491().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2493().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2494().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2495().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2497().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2498().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2499().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2501().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2502().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2503().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2505().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2506().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2507().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2509().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2510().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2511().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2513().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2514().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2515().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2517().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2518().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2519().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2521().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2522().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2523().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2525().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2526().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2527().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2529().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2530().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2531().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2533().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2534().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2535().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2537().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2538().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2539().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2541().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2542().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2543().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2545().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2546().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2547().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2549().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2550().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2551().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2553().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2554().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2555().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2557().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2558().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2560().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2561().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2563().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2564().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2566().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2567().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2569().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2570().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2572().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2573().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2575().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2576().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2578().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2579().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2581().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2582().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2584().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2585().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2587().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2588().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2590().waveDataVO;
         this._dic[this._dic.length] = new SecretWave2591().waveDataVO;
      }
      
      public function findBattle(param1:int) : MissionBattleVO
      {
         var _loc2_:MissionBattleVO = null;
         for each(_loc2_ in this._battles)
         {
            if(_loc2_.mid == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findWave(param1:int) : WaveDataVO
      {
         var _loc2_:WaveDataVO = null;
         for each(_loc2_ in this._dic)
         {
            if(_loc2_.dataID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

