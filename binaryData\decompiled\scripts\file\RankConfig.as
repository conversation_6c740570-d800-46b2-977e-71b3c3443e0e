package file
{
   import mogames.gameData.rank.vo.RankHeroBRVO;
   import mogames.gameData.rank.vo.RankPetBRVO;
   import mogames.gameData.rank.vo.RankTotalBRVO;
   import mogames.gameData.rank.vo.RankVO;
   
   public class RankConfig
   {
      
      private static var _instance:RankConfig;
      
      private var _ranks:Array;
      
      public function RankConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : RankConfig
      {
         if(!_instance)
         {
            _instance = new RankConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._ranks = [];
         this._ranks[this._ranks.length] = new RankTotalBRVO(2318);
         this._ranks[this._ranks.length] = new RankHeroBRVO(1889,0);
         this._ranks[this._ranks.length] = new RankHeroBRVO(1890,1);
         this._ranks[this._ranks.length] = new RankHeroBRVO(1891,2);
         this._ranks[this._ranks.length] = new RankHeroBRVO(1892,3);
         this._ranks[this._ranks.length] = new RankPetBRVO(2006);
      }
      
      public function getRank(param1:int) : RankVO
      {
         return this._ranks[param1];
      }
      
      public function findRank(param1:int) : RankVO
      {
         var _loc2_:RankVO = null;
         for each(_loc2_ in this._ranks)
         {
            if(_loc2_.rid == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function get rankData0() : Array
      {
         var _loc1_:Array = [];
         var _loc2_:int = 0;
         while(_loc2_ < 5)
         {
            _loc1_[_loc1_.length] = this._ranks[_loc2_].rankData;
            _loc2_++;
         }
         return _loc1_;
      }
      
      public function get rankData1() : Array
      {
         var _loc1_:Array = [];
         var _loc2_:int = 5;
         var _loc3_:int = int(this._ranks.length);
         var _loc4_:int = _loc2_;
         while(_loc4_ < _loc3_)
         {
            _loc1_[_loc1_.length] = this._ranks[_loc4_].rankData;
            _loc4_++;
         }
         return _loc1_;
      }
   }
}

