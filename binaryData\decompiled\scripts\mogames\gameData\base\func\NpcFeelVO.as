package mogames.gameData.base.func
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   
   public class NpcFeelVO
   {
      
      private var _id:Oint = new Oint();
      
      private var _rewards:Array;
      
      private var _needs:Array;
      
      public function NpcFeelVO(param1:int, param2:Array, param3:Array)
      {
         super();
         MathUtil.saveINT(this._id,param1);
         this._needs = param2;
         this._rewards = param3;
      }
      
      public function get npcID() : int
      {
         return MathUtil.loadINT(this._id);
      }
      
      public function get rewards() : Array
      {
         return this._rewards;
      }
      
      public function get needs() : Array
      {
         return this._needs;
      }
   }
}

