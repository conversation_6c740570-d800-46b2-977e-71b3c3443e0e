package file
{
   import com.mogames.utils.MathUtil;
   import mogames.ConstData;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.fuben.vo.FubenVO;
   import mogames.gameData.good.GoodFactory;
   import mogames.gameData.good.bag.base.GameBagVO;
   import mogames.gameData.good.base.ConstBagVO;
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.RoleArgVO;
   
   public class BHDConfig
   {
      
      private static var _instance:BHDConfig;
      
      private var _bags:Array;
      
      public var argVO:RoleArgVO;
      
      public var waveData:WaveDataVO;
      
      public var interval:Number;
      
      public var speed:int;
      
      public function BHDConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : BHDConfig
      {
         if(!_instance)
         {
            _instance = new BHDConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         var _loc1_:OneWaveVO = null;
         this.interval = 3;
         this.speed = 90;
         this._bags = [];
         this._bags[this._bags.length] = {
            "rate":90,
            "bagID":11002
         };
         this._bags[this._bags.length] = {
            "rate":120,
            "bagID":11154
         };
         this._bags[this._bags.length] = {
            "rate":100,
            "bagID":11157
         };
         this._bags[this._bags.length] = {
            "rate":50,
            "bagID":11158
         };
         this._bags[this._bags.length] = {
            "rate":50,
            "bagID":11008
         };
         this._bags[this._bags.length] = {
            "rate":120,
            "bagID":11201
         };
         this._bags[this._bags.length] = {
            "rate":30,
            "bagID":11071
         };
         this._bags[this._bags.length] = {
            "rate":10,
            "bagID":11072
         };
         this._bags[this._bags.length] = {
            "rate":120,
            "bagID":11204
         };
         this._bags[this._bags.length] = {
            "rate":50,
            "bagID":11205
         };
         this._bags[this._bags.length] = {
            "rate":50,
            "bagID":11011
         };
         this._bags[this._bags.length] = {
            "rate":80,
            "bagID":11301
         };
         this._bags[this._bags.length] = {
            "rate":130,
            "bagID":11302
         };
         this._bags[this._bags.length] = {
            "rate":90,
            "bagID":11002
         };
         this.argVO = new RoleArgVO(166,12000,1001,100,30,10,250,130,null);
         this.waveData = new WaveDataVO(0);
         this.waveData.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(266,2500,200,60,30,25,150,100,{
            "atkPer":50,
            "keepTime":2
         })));
         _loc1_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(265,1800,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(20);
         _loc1_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(266,2500,200,60,30,25,150,100,{
            "rate":50,
            "defPer":20,
            "keepTime":2
         })));
         _loc1_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(265,1800,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(20);
         _loc1_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(266,2500,200,60,30,25,150,100,{
            "rate":50,
            "hurtBei":1.8
         })));
         _loc1_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(265,1800,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(20);
         _loc1_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(266,2500,200,60,30,25,150,100,{
            "rate":60,
            "keepTime":2
         })));
         _loc1_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(265,1800,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(20);
         _loc1_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(266,2500,200,60,30,25,150,100,{
            "rate":50,
            "defPer":20,
            "keepTime":2
         })));
         _loc1_.addEnemy(new WaveEnemyVO(14,new RoleArgVO(265,1800,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(20);
         _loc1_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(266,2500,200,60,30,25,150,100,{
            "atkPer":50,
            "keepTime":2
         })));
         _loc1_.addEnemy(new WaveEnemyVO(14,new RoleArgVO(265,1800,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(20);
         _loc1_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(266,2500,200,60,30,25,150,100,{
            "rate":60,
            "keepTime":2
         })));
         _loc1_.addEnemy(new WaveEnemyVO(16,new RoleArgVO(265,1800,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(20);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(266,2500,200,60,30,25,150,100,{
            "atkPer":50,
            "keepTime":2
         })));
         _loc1_.addEnemy(new WaveEnemyVO(16,new RoleArgVO(265,1800,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(20);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(266,2500,200,60,30,25,150,100,{
            "rate":200,
            "curePer":40
         })));
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(265,1800,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(20);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(266,2500,200,60,30,25,150,100,{
            "rate":50,
            "curePer":30
         })));
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(265,1800,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
      }
      
      public function get randBagVO() : GameBagVO
      {
         var _loc2_:Object = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._bags)
         {
            if(MathUtil.checkOdds(_loc2_.rate))
            {
               _loc1_[_loc1_.length] = _loc2_;
            }
         }
         if(_loc1_.length <= 0)
         {
            _loc1_ = this._bags.slice();
         }
         var _loc3_:Object = _loc1_[int(Math.random() * _loc1_.length)];
         return GoodFactory.newBagVO(GoodConfig.instance().findConstGood(_loc3_.bagID) as ConstBagVO);
      }
      
      public function newReward(param1:Boolean) : Array
      {
         if(!param1)
         {
            return this.loseReward;
         }
         return this.winReward;
      }
      
      private function get winReward() : Array
      {
         var _loc1_:int = 0;
         var _loc6_:int = 0;
         var _loc2_:int = Math.random() * 100 + 1;
         if(_loc2_ <= 20)
         {
            _loc1_ = ConstData.INT1.v;
         }
         else if(_loc2_ <= 65)
         {
            _loc1_ = ConstData.INT2.v;
         }
         else
         {
            _loc1_ = ConstData.INT3.v;
         }
         var _loc3_:Array = [];
         var _loc4_:FubenVO = FubenConfig.instance().findFuben(402);
         var _loc5_:Array = _loc4_.drops.slice(1);
         var _loc7_:int = 0;
         while(_loc7_ < _loc1_)
         {
            _loc6_ = Math.random() * _loc5_.length;
            _loc3_[_loc3_.length] = _loc5_[_loc6_];
            _loc5_.splice(_loc6_,1);
            _loc7_++;
         }
         return _loc3_;
      }
      
      private function get loseReward() : Array
      {
         return [new BaseRewardVO(10000,35000)];
      }
   }
}

