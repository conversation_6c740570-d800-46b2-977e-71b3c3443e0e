package com.mogames.comt
{
   import com.mogames.utils.MethodUtil;
   import flash.display.DisplayObject;
   import flash.display.SimpleButton;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class TabComt
   {
      
      private var _btns:Array;
      
      private var _downFuncs:Array;
      
      private var _checkFuncs:Array;
      
      private var _last:SimpleButton;
      
      private var _index:int;
      
      public function TabComt(param1:Array, param2:Array, param3:Array = null)
      {
         var _loc4_:SimpleButton = null;
         super();
         this._btns = param1;
         this._downFuncs = param2;
         this._checkFuncs = param3;
         for each(_loc4_ in this._btns)
         {
            _loc4_.addEventListener(MouseEvent.MOUSE_DOWN,this.onDown);
         }
         if(this._btns[0])
         {
            this._btns[0].addEventListener(Event.REMOVED_FROM_STAGE,this.onRemove);
         }
      }
      
      public function setFirstTab(param1:int) : void
      {
         if(param1 >= this._btns.length || param1 < 0)
         {
            return;
         }
         this.toggleBtn(this._btns[param1]);
      }
      
      public function changeBtn(param1:int) : void
      {
         if(param1 >= this._btns.length || param1 < 0)
         {
            return;
         }
         this._btns[param1].dispatchEvent(new MouseEvent(MouseEvent.MOUSE_DOWN));
      }
      
      public function resetTab() : void
      {
         if(!this._last)
         {
            return;
         }
         this._last.mouseEnabled = true;
         this._last.enabled = true;
         MethodUtil.toggle(this._last);
         this._last = null;
      }
      
      public function get selectIndex() : int
      {
         return this._index;
      }
      
      public function get selectName() : String
      {
         return this._btns[this._index].name;
      }
      
      private function onDown(param1:MouseEvent) : void
      {
         if(!this.checkDown(param1.currentTarget as SimpleButton))
         {
            return;
         }
         this.toggleBtn(param1.currentTarget as SimpleButton);
         this._downFuncs[this._index]();
      }
      
      private function toggleBtn(param1:SimpleButton) : void
      {
         if(this._last != null)
         {
            this._last.mouseEnabled = true;
            this._last.enabled = true;
            MethodUtil.toggle(this._last);
         }
         this._last = param1;
         this._last.mouseEnabled = false;
         this._last.enabled = false;
         this._last.parent.swapChildren(this._last,this.topBtn);
         MethodUtil.toggle(this._last);
         this._index = this._btns.indexOf(this._last);
      }
      
      private function onRemove(param1:Event) : void
      {
         var _loc2_:SimpleButton = null;
         param1.currentTarget.removeEventListener(Event.REMOVED_FROM_STAGE,this.onRemove);
         for each(_loc2_ in this._btns)
         {
            _loc2_.removeEventListener(MouseEvent.MOUSE_DOWN,this.onDown);
         }
         this._btns = null;
         this._downFuncs = null;
         this._checkFuncs = null;
         this._last = null;
      }
      
      private function get topBtn() : DisplayObject
      {
         var _loc3_:DisplayObject = null;
         var _loc5_:DisplayObject = null;
         var _loc1_:int = 0;
         var _loc2_:int = int(this._btns.length);
         var _loc4_:int = 0;
         while(_loc4_ < _loc2_)
         {
            _loc5_ = this._btns[_loc4_];
            if(_loc5_.parent.getChildIndex(_loc5_) > _loc1_)
            {
               _loc3_ = _loc5_;
               _loc1_ = _loc5_.parent.getChildIndex(_loc5_);
            }
            _loc4_++;
         }
         return _loc3_;
      }
      
      private function checkDown(param1:SimpleButton) : Boolean
      {
         if(!this._checkFuncs)
         {
            return true;
         }
         var _loc2_:int = int(this._btns.indexOf(param1));
         if(!this._checkFuncs[_loc2_])
         {
            return false;
         }
         return this._checkFuncs[_loc2_](_loc2_);
      }
   }
}

