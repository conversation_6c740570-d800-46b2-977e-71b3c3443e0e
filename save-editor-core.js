/**
 * 三国存档编辑器核心模块
 * 处理存档数据的解析、修改和验证
 */

class SaveEditor {
    constructor() {
        this.saveData = null;
        this.originalData = null;
        this.heroDatabase = this.initHeroDatabase();
        this.itemDatabase = this.initItemDatabase();
    }

    /**
     * 初始化英雄数据库
     */
    initHeroDatabase() {
        return {
            433: { name: "典韦", faction: "魏", type: "武将", rarity: 5 },
            390: { name: "关羽", faction: "蜀", type: "武将", rarity: 5 },
            358: { name: "赵云", faction: "蜀", type: "武将", rarity: 5 },
            379: { name: "吕布", faction: "群", type: "武将", rarity: 5 },
            548: { name: "鲁肃", faction: "吴", type: "谋士", rarity: 4 },
            392: { name: "太史慈", faction: "吴", type: "武将", rarity: 4 },
            422: { name: "魏延", faction: "蜀", type: "武将", rarity: 4 },
            457: { name: "马超", faction: "蜀", type: "武将", rarity: 5 },
            454: { name: "张辽", faction: "魏", type: "武将", rarity: 5 },
            455: { name: "华佗", faction: "群", type: "医师", rarity: 4 },
            594: { name: "荀攸", faction: "魏", type: "谋士", rarity: 5 },
            525: { name: "陆抗", faction: "吴", type: "武将", rarity: 4 },
            526: { name: "左慈", faction: "群", type: "方士", rarity: 4 }
        };
    }

    /**
     * 初始化物品数据库
     */
    initItemDatabase() {
        return {
            11071: { name: "嘲讽卷轴", type: "消耗品", rarity: 2 },
            11002: { name: "初级回春卷轴", type: "恢复道具", rarity: 1 },
            11012: { name: "中级行军酒", type: "增益道具", rarity: 2 },
            11005: { name: "初级治愈卷轴", type: "恢复道具", rarity: 1 },
            11003: { name: "中级回春卷轴", type: "恢复道具", rarity: 2 },
            11601: { name: "初级兽血卷轴", type: "增益道具", rarity: 2 },
            11101: { name: "初级禽卷轴", type: "特殊道具", rarity: 2 }
        };
    }

    /**
     * 加载存档数据
     */
    loadSave(jsonData) {
        try {
            this.saveData = typeof jsonData === 'string' ? JSON.parse(jsonData) : jsonData;
            this.originalData = JSON.parse(JSON.stringify(this.saveData)); // 深拷贝备份
            return { success: true, message: "存档加载成功" };
        } catch (error) {
            return { success: false, message: "存档格式错误: " + error.message };
        }
    }

    /**
     * 获取资源数据
     */
    getResources() {
        if (!this.saveData?.master?.values) {
            return null;
        }

        const values = this.saveData.master.values.split('H');
        return {
            silver: parseInt(values[0]) || 0,      // 银币
            prestige: parseInt(values[1]) || 0,    // 威望
            energy: parseInt(values[2]) || 0,      // 体力
            gold: parseInt(values[3]) || 0,        // 元宝
            resource4: parseInt(values[4]) || 0,   // 其他资源
            resource5: parseInt(values[5]) || 0,
            resource6: parseInt(values[6]) || 0,
            resource7: parseInt(values[7]) || 0
        };
    }

    /**
     * 更新资源数据
     */
    updateResources(resources) {
        if (!this.saveData?.master?.values) {
            return { success: false, message: "存档数据不完整" };
        }

        try {
            const values = this.saveData.master.values.split('H');
            
            // 验证资源数值
            const maxValues = {
                silver: 999999999,
                prestige: 999999999,
                energy: 999,
                gold: 999999
            };

            for (const [key, value] of Object.entries(resources)) {
                if (maxValues[key] && value > maxValues[key]) {
                    return { success: false, message: `${key}数值超出限制` };
                }
                if (value < 0) {
                    return { success: false, message: `${key}数值不能为负` };
                }
            }

            // 更新数值
            values[0] = resources.silver || values[0];
            values[1] = resources.prestige || values[1];
            values[2] = resources.energy || values[2];
            values[3] = resources.gold || values[3];

            this.saveData.master.values = values.join('H');
            return { success: true, message: "资源更新成功" };
        } catch (error) {
            return { success: false, message: "更新失败: " + error.message };
        }
    }

    /**
     * 获取英雄列表
     */
    getHeroes() {
        if (!this.saveData?.hero) {
            return [];
        }

        return this.saveData.hero.map((hero, index) => {
            const heroInfo = this.heroDatabase[hero.id] || { 
                name: `未知英雄${hero.id}`, 
                faction: "未知", 
                type: "未知", 
                rarity: 1 
            };

            const baseStats = hero.base ? hero.base.split('H') : ['0', '0', '0', '0', '0', '0', '0'];

            return {
                index: index,
                id: hero.id,
                name: heroInfo.name,
                faction: heroInfo.faction,
                type: heroInfo.type,
                rarity: heroInfo.rarity,
                level: parseInt(baseStats[0]) || 0,
                star: parseInt(baseStats[1]) || 0,
                experience: parseInt(baseStats[2]) || 0,
                officeID: hero.officeID || 0,
                equipment: hero.equip || []
            };
        });
    }

    /**
     * 更新英雄数据
     */
    updateHero(heroIndex, updates) {
        if (!this.saveData?.hero?.[heroIndex]) {
            return { success: false, message: "英雄不存在" };
        }

        try {
            const hero = this.saveData.hero[heroIndex];
            const baseStats = hero.base ? hero.base.split('H') : ['0', '0', '0', '0', '0', '0', '0'];

            // 更新基础属性
            if (updates.level !== undefined) {
                if (updates.level < 0 || updates.level > 200) {
                    return { success: false, message: "等级范围应在0-200之间" };
                }
                baseStats[0] = updates.level.toString();
            }

            if (updates.star !== undefined) {
                if (updates.star < 0 || updates.star > 10) {
                    return { success: false, message: "星级范围应在0-10之间" };
                }
                baseStats[1] = updates.star.toString();
            }

            if (updates.experience !== undefined) {
                if (updates.experience < 0) {
                    return { success: false, message: "经验值不能为负" };
                }
                baseStats[2] = updates.experience.toString();
            }

            hero.base = baseStats.join('H');
            return { success: true, message: "英雄更新成功" };
        } catch (error) {
            return { success: false, message: "更新失败: " + error.message };
        }
    }

    /**
     * 获取物品列表
     */
    getItems() {
        if (!this.saveData?.bagGood) {
            return [];
        }

        return this.saveData.bagGood.map((item, index) => {
            const parts = item.split('H');
            const count = parseInt(parts[0]) || 0;
            const itemId = parseInt(parts[1]) || 0;
            
            const itemInfo = this.itemDatabase[itemId] || {
                name: `未知物品${itemId}`,
                type: "未知",
                rarity: 1
            };

            return {
                index: index,
                id: itemId,
                name: itemInfo.name,
                type: itemInfo.type,
                rarity: itemInfo.rarity,
                count: count,
                rawData: item
            };
        });
    }

    /**
     * 添加物品
     */
    addItem(itemId, count = 1) {
        if (!this.saveData?.bagGood) {
            this.saveData.bagGood = [];
        }

        try {
            if (count <= 0) {
                return { success: false, message: "物品数量必须大于0" };
            }

            const itemData = `${count}H${itemId}`;
            this.saveData.bagGood.push(itemData);
            
            return { success: true, message: "物品添加成功" };
        } catch (error) {
            return { success: false, message: "添加失败: " + error.message };
        }
    }

    /**
     * 删除物品
     */
    removeItem(itemIndex) {
        if (!this.saveData?.bagGood?.[itemIndex]) {
            return { success: false, message: "物品不存在" };
        }

        try {
            this.saveData.bagGood.splice(itemIndex, 1);
            return { success: true, message: "物品删除成功" };
        } catch (error) {
            return { success: false, message: "删除失败: " + error.message };
        }
    }

    /**
     * 更新物品数量
     */
    updateItemCount(itemIndex, newCount) {
        if (!this.saveData?.bagGood?.[itemIndex]) {
            return { success: false, message: "物品不存在" };
        }

        try {
            if (newCount <= 0) {
                return this.removeItem(itemIndex);
            }

            const parts = this.saveData.bagGood[itemIndex].split('H');
            parts[0] = newCount.toString();
            this.saveData.bagGood[itemIndex] = parts.join('H');

            return { success: true, message: "物品数量更新成功" };
        } catch (error) {
            return { success: false, message: "更新失败: " + error.message };
        }
    }

    /**
     * 获取副本进度
     */
    getProgress() {
        return {
            fuben: this.saveData?.flag?.fuben || [],
            tasks: this.saveData?.task || {},
            talents: this.saveData?.talent || [],
            books: this.saveData?.book || []
        };
    }

    /**
     * 验证存档数据完整性
     */
    validateSave() {
        const errors = [];
        
        if (!this.saveData) {
            errors.push("存档数据为空");
            return errors;
        }

        // 检查必要字段
        const requiredFields = ['bagGood', 'hero', 'master'];
        requiredFields.forEach(field => {
            if (!this.saveData[field]) {
                errors.push(`缺少必要字段: ${field}`);
            }
        });

        // 检查资源数据格式
        if (this.saveData.master?.values) {
            const values = this.saveData.master.values.split('H');
            if (values.length < 4) {
                errors.push("资源数据格式不正确");
            }
        }

        return errors;
    }

    /**
     * 导出存档数据
     */
    exportSave() {
        if (!this.saveData) {
            return null;
        }
        return JSON.stringify(this.saveData, null, 2);
    }

    /**
     * 重置到原始数据
     */
    resetToOriginal() {
        if (this.originalData) {
            this.saveData = JSON.parse(JSON.stringify(this.originalData));
            return { success: true, message: "已重置到原始数据" };
        }
        return { success: false, message: "没有原始数据可重置" };
    }

    /**
     * 获取修改统计
     */
    getModificationStats() {
        if (!this.originalData || !this.saveData) {
            return null;
        }

        const stats = {
            resourcesChanged: false,
            heroesChanged: 0,
            itemsChanged: 0
        };

        // 检查资源是否修改
        if (this.originalData.master?.values !== this.saveData.master?.values) {
            stats.resourcesChanged = true;
        }

        // 检查英雄修改数量
        if (this.originalData.hero && this.saveData.hero) {
            stats.heroesChanged = this.saveData.hero.filter((hero, index) => {
                const original = this.originalData.hero[index];
                return !original || JSON.stringify(hero) !== JSON.stringify(original);
            }).length;
        }

        // 检查物品修改数量
        if (this.originalData.bagGood && this.saveData.bagGood) {
            const originalItems = JSON.stringify(this.originalData.bagGood.sort());
            const currentItems = JSON.stringify(this.saveData.bagGood.sort());
            if (originalItems !== currentItems) {
                stats.itemsChanged = Math.abs(this.saveData.bagGood.length - this.originalData.bagGood.length);
            }
        }

        return stats;
    }
}

// 如果在Node.js环境中，导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SaveEditor;
}

// 如果在浏览器环境中，添加到全局对象
if (typeof window !== 'undefined') {
    window.SaveEditor = SaveEditor;
}
