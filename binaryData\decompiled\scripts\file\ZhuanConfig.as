package file
{
   import mogames.gameData.base.vo.BaseRewardVO;
   
   public class ZhuanConfig
   {
      
      private static var _instance:ZhuanConfig;
      
      private var _list:Array;
      
      public function ZhuanConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : ZhuanConfig
      {
         if(!_instance)
         {
            _instance = new ZhuanConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = [new BaseRewardVO(11401,1),new BaseRewardVO(10303,1),new BaseRewardVO(11601,1),new BaseRewardVO(11601,1),new BaseRewardVO(11601,1),new BaseRewardVO(50001,1),new BaseRewardVO(50002,1),new BaseRewardVO(50003,1),new BaseRewardVO(50018,1),new BaseRewardVO(50010,1),new BaseRewardVO(50011,1),new BaseRewardVO(50015,1),new BaseRewardVO(50016,1),new BaseRewardVO(50022,1),new BaseRewardVO(50025,1),new BaseRewardVO(50026,1),new BaseRewardVO(50028,1),new BaseRewardVO(50029,1),new BaseRewardVO(50031,1),new BaseRewardVO(50031,1),new BaseRewardVO(50032,1),new BaseRewardVO(50035,1),new BaseRewardVO(50036,1),new BaseRewardVO(50037,1),new BaseRewardVO(50039,1),new BaseRewardVO(50041,1)];
      }
      
      public function get newRewards() : Array
      {
         var _loc3_:int = 0;
         var _loc1_:Array = this._list.slice();
         var _loc2_:Array = [];
         var _loc4_:int = 0;
         while(_loc4_ < 12)
         {
            _loc3_ = Math.random() * _loc1_.length;
            _loc2_[_loc4_] = _loc1_[_loc3_];
            _loc1_.splice(_loc3_,1);
            _loc4_++;
         }
         return _loc2_;
      }
   }
}

