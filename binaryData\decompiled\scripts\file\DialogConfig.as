package file
{
   import com.mogames.utils.TxtUtil;
   import flash.utils.Dictionary;
   import mogames.gameData.story.DialogVO;
   
   public class DialogConfig
   {
      
      private static var _instance:DialogConfig;
      
      private var _dic:Dictionary;
      
      public function DialogConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this._dic = new Dictionary();
         this.init();
      }
      
      public static function instance() : DialogConfig
      {
         if(!_instance)
         {
            _instance = new DialogConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._dic["STORY_START1"] = new Vector.<DialogVO>();
         this._dic["STORY_START1"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","未知错误？？？这是什么情况？刚开始玩游戏电脑就坏了？",{"music":"BGM_FUNNY0"}));
         this._dic["STORY_START1"].push(new DialogVO(0,"王大头","WANG_DA_TOU001","不可能吧，这可是花了我十块钱的巨款才淘回来的二手电脑啊！"));
         this._dic["STORY_START1"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","咦，不对，好像没宕机，鼠标还能操作！" + TxtUtil.setColor("点击按钮") + "试试看！"));
         this._dic["STORY_START2"] = new Vector.<DialogVO>();
         this._dic["STORY_START2"].push(new DialogVO(0,"王大头","WANG_DA_TOU007",TxtUtil.setColor("前往三国？") + "这又是什么意思？"));
         this._dic["STORY_START2"].push(new DialogVO(0,"王大头","WANG_DA_TOU008","动不了了.......什么情况啊！"));
         this._dic["STORY_START2"].push(new DialogVO(0,"王大头","WANG_DA_TOU001","啊————————————————————"));
         this._dic["STORY_ENTER"] = new Vector.<DialogVO>();
         this._dic["STORY_ENTER"].push(new DialogVO(0,"王大头","WANG_DA_TOU005","囧囧有神唔......我这是在哪里啊？刚才我在玩游戏......然后......"));
         this._dic["STORY_ENTER"].push(new DialogVO(0,"豆娃","DOU_WA001","然后你就到这个" + TxtUtil.setColor("游戏世界") + "里来了！",{"music":"BGM_FUNNY0"}));
         this._dic["STORY_ENTER"].push(new DialogVO(0,"王大头","WANG_DA_TOU008","啥？咦，你是谁？看你绿不溜秋的，看着像4399的豆娃！"));
         this._dic["STORY_ENTER"].push(new DialogVO(0,"豆娃","DOU_WA005","呀呼！在下正是豆娃，现在是这游戏的GM（游戏管理员）！",{"sound":"AUDIO_YAHO"}));
         this._dic["STORY_ENTER"].push(new DialogVO(0,"王大头","WANG_DA_TOU002","这一定是在做梦......（掐）哎呦，好疼！救命啊！我要回去！我还年轻，我还没泡过妞啊！"));
         this._dic["STORY_ENTER"].push(new DialogVO(0,"豆娃","DOU_WA002","想回去很简单，只要你" + TxtUtil.setColor("打通这个游戏") + "就可以了！"));
         this._dic["STORY_ENTER"].push(new DialogVO(0,"王大头","WANG_DA_TOU003","额......打通这个游戏？"));
         this._dic["STORY_ENTER"].push(new DialogVO(0,"豆娃","DOU_WA003","你现在身处的" + TxtUtil.setColor("游戏世界") + "里生存着三国各个时期的人物，现今地方各自为政，时局非常混乱。"));
         this._dic["STORY_ENTER"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","你意思是诸葛亮还有可能跟张角打一架？"));
         this._dic["STORY_ENTER"].push(new DialogVO(0,"豆娃","DOU_WA003","哈哈哈，有可能呢！只要你" + TxtUtil.setColor("攻占所有州县一统天下") + "，然后" + TxtUtil.setColor("消灭所有黑暗势力") + "就可以从这游戏世界出去了！"));
         this._dic["STORY_ENTER"].push(new DialogVO(0,"王大头","WANG_DA_TOU003","你说这很简单？.....现在手头上无兵无将，一统个毛线！"));
         this._dic["STORY_ENTER"].push(new DialogVO(0,"豆娃","DOU_WA005","嘟嘟嘟！我是GM，在游戏里给你点帮助的权限还是有的！",{"sound":"AUDIO_DUDU"}));
         this._dic["STORY_ENTER"].push(new DialogVO(0,"王大头","WANG_DA_TOU010","（抱大腿）豆娃大哥哥，帮帮人家啦！"));
         this._dic["STORY_ENTER"].push(new DialogVO(0,"豆娃","DOU_WA002","哎呀，赶紧把手撒开！首先你得有个自己的主城！" + TxtUtil.setColor("大都") + "城刚刚被乱党攻陷了，你速度前往，这正好是你的机会！"));
         this._dic["STORY_ENTER"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","意思是让我消灭乱党，乘机夺权......？"));
         this._dic["STORY_ENTER"].push(new DialogVO(0,"豆娃","DOU_WA001","一点就通！送你一个将和一些士兵！让他跟你先干一波！"));
         this._dic["STORY_ENTER"].push(new DialogVO(0,"关平","GUAN_PING","关平参见主公！！！",{"effect":"FIRST_FREE_HERO"}));
         this._dic["STORY_ENTER"].push(new DialogVO(0,"王大头","WANG_DA_TOU001","哎妈呀，从哪冒出来的，吓我一跳！",{
            "sound":"AUDIO_XIA",
            "effect":"SHAKE0"
         }));
         this._dic["STORY_ENTER"].push(new DialogVO(0,"关平","GUAN_PING","让主公受惊了！"));
         this._dic["STORY_ENTER"].push(new DialogVO(0,"王大头","WANG_DA_TOU006","没事没事！（被叫主公的感觉还挺爽）咳咳，嗯好，现在速速随我前往大都！"));
         this._dic["STORY_ENTER"].push(new DialogVO(0,"关平","GUAN_PING","遵命！属下这就为主公开路！"));
         this._dic["GUIDE_MISSION1"] = new Vector.<DialogVO>();
         this._dic["GUIDE_MISSION1"].push(new DialogVO(0,"乱党头子","ICON_ENEMY","大都让你掌管了这么久，现在也该轮到我了吧！"));
         this._dic["GUIDE_MISSION1"].push(new DialogVO(0,"大都主公","ICON_MASTER","乱臣贼子，你是不会有好下场的！"));
         this._dic["GUIDE_MISSION1"].push(new DialogVO(0,"乱党头子","ICON_ENEMY","哼！大都已经尽入我手了！！！"));
         this._dic["GUIDE_MISSION1"].push(new DialogVO(0,"大都主公","ICON_MASTER","额啊————————————————————"));
         this._dic["GUIDE_MISSION2"] = new Vector.<DialogVO>();
         this._dic["GUIDE_MISSION2"].push(new DialogVO(0,"乱党头子","ICON_ENEMY","嗯？！城外好像来了一队人马！（大声询问）来者何人？"));
         this._dic["GUIDE_MISSION2"].push(new DialogVO(0,"王大头","WANG_DA_TOU001","啊？我......我只是是路过的......"));
         this._dic["GUIDE_MISSION2"].push(new DialogVO(0,"乱党头子","ICON_ENEMY","什么东西！众人给我出城灭了这帮不明来历的家伙！"));
         this._dic["GUIDE_MISSION2"].push(new DialogVO(0,"王大头","WANG_DA_TOU001","他们要来了，咋办！咋办！！咋办！！！"));
         this._dic["GUIDE_MISSION2"].push(new DialogVO(0,"豆娃","DOU_WA002","刚不是给你兵和将了么！一群乌合之众很好对付的！"));
         this._dic["GUIDE_MISSION3"] = new Vector.<DialogVO>();
         this._dic["GUIDE_MISSION3"].push(new DialogVO(0,"乱党头子","ICON_ENEMY","什么？先锋部队被击退了？！"));
         this._dic["GUIDE_MISSION3"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","赶快投降吧，小爷我可不是那么好惹的！"));
         this._dic["GUIDE_MISSION3"].push(new DialogVO(0,"乱党头子","ICON_ENEMY","口出狂言之辈，我的大军还没出来呢！全军听令，出城迎战！"));
         this._dic["GUIDE_MISSION3"].push(new DialogVO(0,"王大头","WANG_DA_TOU005","不好！好像把对面头头惹恼了！"));
         this._dic["GUIDE_MISSION3"].push(new DialogVO(0,"豆娃","DOU_WA003","别紧张，给你个战斗物品" + TxtUtil.setColor("陨石卷轴") + "！可以对" + TxtUtil.setColor("敌方士兵") + "造成大量伤害！"));
         this._dic["GUIDE_MISSION3"].push(new DialogVO(0,"王大头","WANG_DA_TOU005","好吧，果然新手关全是套路！"));
         this._dic["GUIDE_BATTLE0"] = new Vector.<DialogVO>();
         this._dic["GUIDE_BATTLE0"].push(new DialogVO(0,"豆娃","DOU_WA003","好，那我先带你认识下战场里的各个界面吧。"));
         this._dic["GUIDE_BATTLE1"] = new Vector.<DialogVO>();
         this._dic["GUIDE_BATTLE1"].push(new DialogVO(0,"豆娃","DOU_WA003","怎么样，各个界面都熟悉了吗？"));
         this._dic["GUIDE_BATTLE1"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","我是什么人，过目不忘，谢谢！"));
         this._dic["GUIDE_BATTLE1"].push(new DialogVO(0,"豆娃","DOU_WA003","哈哈哈！那下面布置出战武将吧！用" + TxtUtil.setColor("鼠标点击兵营") + "。注意，" + TxtUtil.setColor("兵营不能布置谋士职业的将领") + "！"));
         this._dic["GUIDE_BATTLE1"].push(new DialogVO(0,"豆娃","DOU_WA003","而主营恰恰相反，" + TxtUtil.setColor("主营只能布置谋士职业的将领") + "！"));
         this._dic["GUIDE_BATTLE2"] = new Vector.<DialogVO>();
         this._dic["GUIDE_BATTLE2"].push(new DialogVO(0,"豆娃","DOU_WA003","接下来我们训练些士兵出来！不过训练士兵需要消耗" + TxtUtil.setColor("木头和食物") + "。"));
         this._dic["GUIDE_BATTLE2"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","那如何获得木头和食物啊？"));
         this._dic["GUIDE_BATTLE2"].push(new DialogVO(0,"豆娃","DOU_WA003","很简单，把" + TxtUtil.setColor("鼠标移动到树木或者果树上") + "就会自动采集了！你先去采集100个木头和食物过来。"));
         this._dic["GUIDE_BATTLE2"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","小事一桩，看我的！"));
         this._dic["GUIDE_BATTLE3"] = new Vector.<DialogVO>();
         this._dic["GUIDE_BATTLE3"].push(new DialogVO(0,"豆娃","DOU_WA004","嗯嗯，小伙子完成的不错！"));
         this._dic["GUIDE_BATTLE3"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","那必须的！小爷我可是游戏高手！"));
         this._dic["GUIDE_BATTLE3"].push(new DialogVO(0,"豆娃","DOU_WA003","又吹牛......下面用" + TxtUtil.setColor("鼠标点击兵营") + "训练3个士兵！" + TxtUtil.setColor("士兵属性与武将等级有关！")));
         this._dic["GUIDE_BATTLE4"] = new Vector.<DialogVO>();
         this._dic["GUIDE_BATTLE4"].push(new DialogVO(0,"豆娃","DOU_WA003","三个小兵已经出来了，很容易吧！"));
         this._dic["GUIDE_BATTLE4"].push(new DialogVO(0,"王大头","WANG_DA_TOU005","才三个小兵.......感觉还是太虚啊！"));
         this._dic["GUIDE_BATTLE4"].push(new DialogVO(0,"豆娃","DOU_WA002","笨啊！你可以再多训练些士兵出来嘛！"));
         this._dic["GUIDE_BATTLE4"].push(new DialogVO(0,"王大头","WANG_DA_TOU005","智商被碾压了......"));
         this._dic["GUIDE_BATTLE4"].push(new DialogVO(0,"豆娃","DOU_WA003","速度！那些乱党要出来了！下面看你的咯！一举拿下大都城！"));
         this._dic["GUIDE_BATTLE4"].push(new DialogVO(0,"王大头","WANG_DA_TOU005","没法子了，硬着头皮上吧......"));
         this._dic["GUIDE_BATTLE_FAIL"] = new Vector.<DialogVO>();
         this._dic["GUIDE_BATTLE_FAIL"].push(new DialogVO(0,"乱党头子","ICON_ENEMY","哈哈，一帮无能之辈还妄想跟我斗！"));
         this._dic["GUIDE_BATTLE_FAIL"].push(new DialogVO(0,"豆娃","DOU_WA002","这么简单的战斗都能失败......"));
         this._dic["GUIDE_BATTLE_FAIL"].push(new DialogVO(0,"王大头","WANG_DA_TOU006","我只是一时大意了呢~~~那现在我怎么办？"));
         this._dic["GUIDE_BATTLE_FAIL"].push(new DialogVO(0,"豆娃","DOU_WA002","能咋办，幸亏是新手关，可以给你读档重来。"));
         this._dic["GUIDE_BATTLE_FAIL"].push(new DialogVO(0,"王大头","WANG_DA_TOU006","嘿嘿，那就好那就好！"));
         this._dic["MAIN_ENTER"] = new Vector.<DialogVO>();
         this._dic["MAIN_ENTER"].push(new DialogVO(0,"豆娃","DOU_WA004","不错不错，乱党已经都被消灭了！现在这大都城就是你的了！"));
         this._dic["MAIN_ENTER"].push(new DialogVO(0,"王大头","WANG_DA_TOU004","也就是说现在我是大都的主公了？"));
         this._dic["MAIN_ENTER"].push(new DialogVO(0,"豆娃","DOU_WA001","哈哈，是的啊！快别流口水了，进城吧！"));
         this._dic["MAIN_ENTER"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","众人听令，随本大爷......不，随本主公进城！"));
         this._dic["MAIN_GUIDE0"] = new Vector.<DialogVO>();
         this._dic["MAIN_GUIDE0"].push(new DialogVO(0,"王大头","WANG_DA_TOU010","哇，没想到这大都外头看着不咋地，里面这么繁华啊！"));
         this._dic["MAIN_GUIDE0"].push(new DialogVO(0,"豆娃","DOU_WA001","那当然了，说了要帮你可不能亏待你！"));
         this._dic["MAIN_GUIDE0"].push(new DialogVO(0,"王大头","WANG_DA_TOU004","嘿嘿嘿，一想到我是这里的老大就激动！"));
         this._dic["MAIN_GUIDE0"].push(new DialogVO(0,"豆娃","DOU_WA003","不过别忘了你是身处在游戏中，你想要回去就得好好利用这座主城哦！"));
         this._dic["MAIN_GUIDE0"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","说的是，说的是！"));
         this._dic["MAIN_GUIDE0"].push(new DialogVO(0,"豆娃","DOU_WA003","那我先带你逛逛，大概熟悉一下主城里的基本功能吧！"));
         this._dic["MAIN_GUIDE1"] = new Vector.<DialogVO>();
         this._dic["MAIN_GUIDE1"].push(new DialogVO(0,"豆娃","DOU_WA003","主城里有许多" + TxtUtil.setColor("建筑") + "，各个建筑的功能是不一样的，你先大概熟悉下。"));
         this._dic["MAIN_GUIDE1"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","好，老夫这就看一看。"));
         this._dic["MAIN_GUIDE2"] = new Vector.<DialogVO>();
         this._dic["MAIN_GUIDE2"].push(new DialogVO(0,"豆娃","DOU_WA003","各个建筑都有所了解了吧，具体功能你可以随后自己再琢磨下。"));
         this._dic["MAIN_GUIDE2"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","凭我的聪明才智，不在话下！"));
         this._dic["MAIN_GUIDE2"].push(new DialogVO(0,"豆娃","DOU_WA001","真爱吹牛......下面我教你一些基本操作吧！"));
         this._dic["MAIN_GUIDE2"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","放马过来，我已经饥渴难耐！"));
         this._dic["MAIN_GUIDE2"].push(new DialogVO(0,"豆娃","DOU_WA003","所谓工欲善其事，必先利其器，给武将装备上一把趁手的兵器吧！"));
         this._dic["MAIN_GUIDE3"] = new Vector.<DialogVO>();
         this._dic["MAIN_GUIDE3"].push(new DialogVO(0,"豆娃","DOU_WA003","嗯嗯，完成的不错。"));
         this._dic["MAIN_GUIDE3"].push(new DialogVO(0,"豆娃","DOU_WA003","我们再看一下" + TxtUtil.setColor("行囊") + "。行囊可以用来配置战场可用的物品，还可以配置你学会的技能。"));
         this._dic["MAIN_GUIDE3"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","那要如何配置呢？"));
         this._dic["MAIN_GUIDE3"].push(new DialogVO(0,"豆娃","DOU_WA003","不要着急，随我来。"));
         this._dic["MAIN_GUIDE_END0"] = new Vector.<DialogVO>();
         this._dic["MAIN_GUIDE_END0"].push(new DialogVO(0,"豆娃","DOU_WA003","不错不错，很有悟性，确实是一教就会！"));
         this._dic["MAIN_GUIDE_END0"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","那当然，我也不是浪得虚名的！"));
         this._dic["MAIN_GUIDE_END0"].push(new DialogVO(0,"豆娃","DOU_WA003","好了，现在教你也教的差不多了，下面你可以出城开始一统大业了！"));
         this._dic["MAIN_GUIDE_END0"].push(new DialogVO(0,"王大头","WANG_DA_TOU005","不过我一个人还是有点虚啊！"));
         this._dic["MAIN_GUIDE_END0"].push(new DialogVO(0,"豆娃","DOU_WA004","有点主公气概好不好！你也不是一个人呀！会有越来越多的武将投靠你的！而且我也会时不时出来指点你呢！"));
         this._dic["MAIN_GUIDE_END0"].push(new DialogVO(0,"豆娃","DOU_WA004","记住哦：" + TxtUtil.setColor("攻占所有州县一统天下，消灭黑暗势力！") + "那样你就可以离开游戏世界了！"));
         this._dic["MAIN_GUIDE_END0"].push(new DialogVO(0,"王大头","WANG_DA_TOU009","没办法，事到如今也只能靠自己了！出城！"));
         this._dic["MAIN_GUIDE_END1"] = new Vector.<DialogVO>();
         this._dic["MAIN_GUIDE_END1"].push(new DialogVO(0,"豆娃","DOU_WA003","年轻人果然有这份魄力，都不需要我教！"));
         this._dic["MAIN_GUIDE_END1"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","必须的！你就等着我凯旋的消息吧！"));
         this._dic["MAIN_GUIDE_END1"].push(new DialogVO(0,"豆娃","DOU_WA004","哈哈哈！非常好！我看好你！"));
         this._dic["MAIN_GUIDE_END1"].push(new DialogVO(0,"豆娃","DOU_WA004","记住哦：" + TxtUtil.setColor("攻占所有州县一统天下，消灭黑暗势力！") + "那样你就可以离开游戏世界了！"));
         this._dic["MAIN_GUIDE_END1"].push(new DialogVO(0,"王大头","WANG_DA_TOU009","箭已开弦，一切就看我的了！出城！"));
         this._dic["GUIDE_EXTRA"] = new Vector.<DialogVO>();
         this._dic["GUIDE_EXTRA"].push(new DialogVO(0,"豆娃","DOU_WA004","恭喜恭喜，初战告捷哈！"));
         this._dic["GUIDE_EXTRA"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","嘿嘿，刚开始还有点紧张，现在开始慢慢调整了！"));
         this._dic["GUIDE_EXTRA"].push(new DialogVO(0,"豆娃","DOU_WA001","也不要高兴太早，后面的关卡会越来越厉害的！"));
         this._dic["GUIDE_EXTRA"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","不怕，我可以把将领等级慢慢提高上去，再换些更好的装备！"));
         this._dic["GUIDE_EXTRA"].push(new DialogVO(0,"豆娃","DOU_WA003","那你知道在哪里可以获得经验和装备呢？"));
         this._dic["GUIDE_EXTRA"].push(new DialogVO(0,"王大头","WANG_DA_TOU005","......母鸡（不知道）......"));
         this._dic["GUIDE_EXTRA"].push(new DialogVO(0,"豆娃","DOU_WA003","还是我来告诉你吧，那就是" + TxtUtil.setColor("战役模式") + "！剧情里面关卡通关后，战役里面对应的关卡也会解锁！"));
         this._dic["GUIDE_EXTRA"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","......感觉好厉害的样子......"));
         this._dic["GUIDE_EXTRA"].push(new DialogVO(0,"豆娃","DOU_WA003","战役关卡胜利后会获得" + TxtUtil.setColor("经验和各种装备") + "哦！要是剧情关卡过不去了，你就可以先到战役模式里面提升下实力！"));
         this._dic["GUIDE_EXTRA"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","原来如此！"));
         this._dic["GUIDE_EXTRA"].push(new DialogVO(0,"豆娃","DOU_WA003","不过另外需要提醒你的是，每次进入战役模式关卡需要" + TxtUtil.setColor("消耗10点粮草") + "，而且失败了会有惩罚的！"));
         this._dic["GUIDE_EXTRA"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","失败？我字典里没有失败二字！"));
         this._dic["GUIDE_EXTRA"].push(new DialogVO(0,"豆娃","DOU_WA001","哈哈哈，很好很好，那我们先去战役关卡里试炼一下吧！"));
         this._dic["GUIDE_EXTRA"].push(new DialogVO(0,"豆娃","DOU_WA003","对了，提醒你一下，战役关卡获得二星以上就可以使用" + TxtUtil.setColor("“扫荡”") + "功能了哦！"));
         this._dic["CHEN_XIAN_START"] = new Vector.<DialogVO>();
         this._dic["CHEN_XIAN_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","这座城看起来很大啊！貌似是个" + TxtUtil.setColor("郡城") + "！"));
         this._dic["CHEN_XIAN_START"].push(new DialogVO(0,"豆娃","DOU_WA004","是的，郡城占领后就可以在" + TxtUtil.setColor("内政") + "中对其" + TxtUtil.setColor("征收资源") + "了！"));
         this._dic["CHEN_XIAN_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU008","嘿嘿，怎们样，是不是看到我的英姿了？"));
         this._dic["CHEN_XIAN_START"].push(new DialogVO(0,"豆娃","DOU_WA005","瞧你那小样~！"));
         this._dic["CHEN_XIAN_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU001","快看，城外门口那边有不少平民！似乎是从城里逃出来的。"));
         this._dic["CHEN_XIAN_START"].push(new DialogVO(0,"夏侯渊","XIAHOU_YUAN","城外何人，胆敢前来犯我夏侯渊的城池？",{"music":"BGM_BOSS0"}));
         this._dic["CHEN_XIAN_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","哇，原来是夏侯渊，这么快就遇到名将了！尔等速速出城投降，归顺于我吧！"));
         this._dic["CHEN_XIAN_START"].push(new DialogVO(0,"夏侯渊","XIAHOU_YUAN","哼，你是个什么东西！这帮刁民，看有人来攻，就往外头逃！出城的统统给我宰了！"));
         this._dic["CHEN_XIAN_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU001","不好，这些平民要被抓了，我得保护他们！"));
         this._dic["CHEN_XIAN_START"].push(new DialogVO(0,"豆娃","DOU_WA003","敌军出城后会" + TxtUtil.setColor("优先攻击平民") + "，注意哦！"));
         this._dic["CHEN_XIAN_WIN"] = new Vector.<DialogVO>();
         this._dic["CHEN_XIAN_WIN"].push(new DialogVO(0,"夏侯渊","XIAHOU_YUAN","可恶，没想到今天会败在一个黄口小儿手里！"));
         this._dic["CHEN_XIAN_WIN"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","将军是条好汉，何不归顺于我，共谋天下呢？"));
         this._dic["CHEN_XIAN_WIN"].push(new DialogVO(0,"夏侯渊","XIAHOU_YUAN","哈哈哈，如今实乃我大意才输给了你，就你这点实力还想留住我，做梦！"));
         this._dic["CHEN_XIAN_WIN"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","好汉不吃眼前亏，别挣扎了~"));
         this._dic["CHEN_XIAN_WIN"].push(new DialogVO(0,"夏侯渊","XIAHOU_YUAN","呸！纵然城池丢了，你也擒不住我！众将士，随我冲出重围！"));
         this._dic["CHEN_XIAN_ESCAPE"] = new Vector.<DialogVO>();
         this._dic["CHEN_XIAN_ESCAPE"].push(new DialogVO(0,"王大头","WANG_DA_TOU009","我靠，被夏侯渊逃跑了！果然是个名将啊！"));
         this._dic["CHEN_XIAN_ESCAPE"].push(new DialogVO(0,"豆娃","DOU_WA003","你看，大意了吧！能助你一臂之力的人就让这么跑了。"));
         this._dic["CHEN_XIAN_ESCAPE"].push(new DialogVO(0,"王大头","WANG_DA_TOU009","喂喂喂，能不说风凉话么，真是的！"));
         this._dic["CHEN_XIAN_ESCAPE"].push(new DialogVO(0,"王大头","WANG_DA_TOU008","嗯？那是什么？地上有一本书......" + TxtUtil.setColor("《神光祈福技能书》") + "？",{"sound":"AUDIO_SURPRISE"}));
         this._dic["CHEN_XIAN_ESCAPE"].push(new DialogVO(0,"豆娃","DOU_WA001","哇，让你捡到宝了！这是本" + TxtUtil.setColor("主公技能书") + "！！！"));
         this._dic["CHEN_XIAN_ESCAPE"].push(new DialogVO(0,"王大头","WANG_DA_TOU010","哈哈哈，天助我也！突然有学习的动力了，赶紧看一下！"));
         this._dic["CHEN_XIAN_ESCAPE"].push(new DialogVO(0,"豆娃","DOU_WA003","学的真快！提醒你一下，主公技能只需要在" + TxtUtil.setColor("行囊") + "里配置下就可以用了，而且可以在" + TxtUtil.setColor("谋略府") + "里升级！",{"effect":"FIRST_LEARN_BOOK"}));
         this._dic["CHEN_XIAN_ESCAPE"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","收到，嘿嘿，虽然夏侯渊跑了，不过总算有所收获！收兵！"));
         this._dic["LU_XIAN_START"] = new Vector.<DialogVO>();
         this._dic["LU_XIAN_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","好像又是座郡城？"));
         this._dic["LU_XIAN_START"].push(new DialogVO(0,"豆娃","DOU_WA004","是的，这里是鲁县！！"));
         this._dic["LU_XIAN_START"].push(new DialogVO(0,"文聘","WEN_PIN","好你个于吉！全军出城，给我拿下于吉首级！"));
         this._dic["LU_XIAN_START"].push(new DialogVO(0,"于吉","ICON_YU_JI_HEAD","壮士，快快救我！帮我干了这个毛胡子，我愿辅佐你打天下！"));
         this._dic["LU_XIAN_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","（原来这老头是于吉啊，这下发达了）老先生莫慌，我这就来救你！"));
         this._dic["LU_XIAN_WIN"] = new Vector.<DialogVO>();
         this._dic["LU_XIAN_WIN"].push(new DialogVO(0,"于吉","ICON_YU_JI_HEAD","哎呀， 总算得救了！"));
         this._dic["LU_XIAN_WIN"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","老先生，这是怎么回事啊？"));
         this._dic["LU_XIAN_WIN"].push(new DialogVO(0,"于吉","ICON_YU_JI_HEAD","老夫略懂一些降妖伏魔之术，这文聘欲将我收入麾下，我不愿意，他然后就要抓我。"));
         this._dic["LU_XIAN_WIN"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","原来如此。老先生不用担心，就算你不愿辅佐我，我也不会强留。"));
         this._dic["LU_XIAN_WIN"].push(new DialogVO(0,"于吉","ICON_YU_JI_HEAD","小兄弟胸怀宽广，老夫甚是感激。这样吧，如果你有什么需求，随时来找老夫！听闻最近有个大都城正是风生水起，老夫准备去那看看。"));
         this._dic["LU_XIAN_WIN"].push(new DialogVO(0,"王大头","WANG_DA_TOU010","哈哈，太好了，那大都真是我的主城！"));
         this._dic["LU_XIAN_WIN"].push(new DialogVO(0,"于吉","ICON_YU_JI_HEAD","啊？这么说来，阁下正是最近江湖上传闻的大都主公？！太失敬了！"));
         this._dic["LU_XIAN_WIN"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","（嘿嘿，看来我小有名气了）不敢当不敢当！那我们大都见！"));
         this._dic["XU_CHANG_START"] = new Vector.<DialogVO>();
         this._dic["XU_CHANG_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","许昌......我记得是曹操的地盘。"));
         this._dic["XU_CHANG_START"].push(new DialogVO(0,"曹操","CAO_CAO","宁我负人，毋人负我！",{"music":"BGM_BOSS0"}));
         this._dic["XU_CHANG_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU010","哇！果然是曹孟德！幸会幸会！"));
         this._dic["XU_CHANG_START"].push(new DialogVO(0,"曹操","CAO_CAO","莫非尔就是最近连战连胜的小辈王大头？是否考虑入我营下，一统江山？"));
         this._dic["XU_CHANG_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU004","（曹操一代枭雄，能跟着他混也不错）可以可以，我......"));
         this._dic["XU_CHANG_START"].push(new DialogVO(0,"豆娃","DOU_WA003","打住！你的任务是要你一统天下，不是投靠别人！"));
         this._dic["XU_CHANG_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","我去......咳咳，多谢曹大人器重，不过我不想被他人左右！"));
         this._dic["XU_CHANG_START"].push(new DialogVO(0,"曹操","CAO_CAO","好一句不想被他人左右！既然你来到老夫的地盘，想必是想攻占我许昌。不过现你我实力悬殊，是不可能的。"));
         this._dic["XU_CHANG_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","不......不试试又怎么知道呢！"));
         this._dic["XU_CHANG_START"].push(new DialogVO(0,"曹操","CAO_CAO","甚好！若你能" + TxtUtil.setColor("不损一将") + "顶住老夫的部队，老夫就放你一条生路，否则老夫可不会手下留情！"));
         this._dic["XU_CHANG_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU005","曹操气势太强了......有点虚啊！"));
         this._dic["XU_CHANG_WIN"] = new Vector.<DialogVO>();
         this._dic["XU_CHANG_WIN"].push(new DialogVO(0,"王大头","WANG_DA_TOU005","呼呼......曹操的军队可太难缠了......"));
         this._dic["XU_CHANG_WIN"].push(new DialogVO(0,"曹操","CAO_CAO","有点实力，我曹孟德果然没看错人！"));
         this._dic["XU_CHANG_WIN"].push(new DialogVO(0,"王大头","WANG_DA_TOU001","（左顾右盼）咦，怎么曹操又在说话，刚刚出城的不是曹操？"));
         this._dic["XU_CHANG_WIN"].push(new DialogVO(0,"曹操","CAO_CAO","那只是老夫的替身罢了！！"));
         this._dic["XU_CHANG_WIN"].push(new DialogVO(0,"王大头","WANG_DA_TOU005","我说刚才曹操怎么让一个小兵给剁了~"));
         this._dic["XU_CHANG_WIN"].push(new DialogVO(0,"曹操","CAO_CAO","许昌今日且送予尔等，相信日后定有再交手之日，那时方才是真正的对决！老夫去也！"));
         this._dic["YANG_ZHAI_START"] = new Vector.<DialogVO>();
         this._dic["YANG_ZHAI_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU006","豆娃哥哥，这座城里应该有三国名人吧？"));
         this._dic["YANG_ZHAI_START"].push(new DialogVO(0,"豆娃","DOU_WA003","不错，此人乃“才策谋略，世之奇士”！"));
         this._dic["YANG_ZHAI_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","莫非是郭嘉？！"));
         this._dic["YANG_ZHAI_START"].push(new DialogVO(0,"豆娃","DOU_WA003","正是郭嘉，三国挺熟悉嘛！郭嘉在这个游戏里智力超群，而且善用技能，你要自己想办法克敌制胜哦！"));
         this._dic["YANG_ZHAI_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU005","等于没说......"));
         this._dic["YANG_ZHAI_START"].push(new DialogVO(0,"郭嘉","GUO_JIA","城外可是日前与我主公在许昌一战的王大头？我家主公对你甚是欣赏，此番派吾等再考验考验你！"));
         this._dic["YANG_ZHAI_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","奉孝啊，跟着曹操混不如跟我，曹操日后我也必将他招入麾下！"));
         this._dic["YANG_ZHAI_START"].push(new DialogVO(0,"郭嘉","GUO_JIA","哈哈，年轻人这么高傲！先赢过我再说吧！"));
         this._dic["YANG_ZHAI_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU001","咦.......等等，怎么没有资源？我靠，这怎么打！"));
         this._dic["YANG_ZHAI_START"].push(new DialogVO(0,"豆娃","DOU_WA003","别慌，你看战场上有很多箱子！" + TxtUtil.setColor("鼠标放到宝箱上会自动开锁") + "，里面可能就有资源！开锁速度与" + TxtUtil.setColor("撬锁等级") + "有关哦！"));
         this._dic["YANG_ZHAI_WIN"] = new Vector.<DialogVO>();
         this._dic["YANG_ZHAI_WIN"].push(new DialogVO(0,"郭嘉","GUO_JIA","不错不错，不愧是主公看中的人。"));
         this._dic["YANG_ZHAI_WIN"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","嘿嘿，怎么样，跟我混吧？"));
         this._dic["YANG_ZHAI_WIN"].push(new DialogVO(0,"郭嘉","GUO_JIA","想让我辅佐你？你跟我家主公不是一个档次的！他日我家主公必将与你决战，胜负尚未可知。"));
         this._dic["YANG_ZHAI_WIN"].push(new DialogVO(0,"王大头","WANG_DA_TOU009","就算不归顺于我，这次不会让你跑了，兄弟们，给我抓住郭嘉！"));
         this._dic["YANG_ZHAI_WIN"].push(new DialogVO(0,"郭嘉","GUO_JIA","想抓我奉孝哪那么容易！撤！"));
         this._dic["YANG_ZHAI_WIN"].push(new DialogVO(0,"王大头","WANG_DA_TOU005","......跑的这么快！",{"effect":"GUO_JIA_ESCAPE"}));
         this._dic["YANG_ZHAI_WIN"].push(new DialogVO(0,"豆娃","DOU_WA003","淡定，以后还会相见的！"));
         this._dic["SECRET_FIRST"] = new Vector.<DialogVO>();
         this._dic["SECRET_FIRST"].push(new DialogVO(0,"王大头","WANG_DA_TOU001","哎呀，这里是什么地方，阴森森的好吓人！"));
         this._dic["SECRET_FIRST"].push(new DialogVO(0,"豆娃","DOU_WA003","这里是游离于外面世界的地方，我们叫做秘境。"));
         this._dic["SECRET_FIRST"].push(new DialogVO(0,"王大头","WANG_DA_TOU002","还是赶紧走吧，我冷。"));
         this._dic["SECRET_FIRST"].push(new DialogVO(0,"豆娃","DOU_WA003","还记得你的任务么？一统天下，然后还要消灭黑暗势力！这秘境里就有需要你消灭的黑暗势力！"));
         this._dic["SECRET_FIRST"].push(new DialogVO(0,"王大头","WANG_DA_TOU005","黑暗势力.......到底是什么来头？"));
         this._dic["SECRET_FIRST"].push(new DialogVO(0,"豆娃","DOU_WA003","这里存在着一些其他的单位，比如" + TxtUtil.setColor("鬼怪单位") + "。它们一直破坏着游戏世界的平衡，而且它们的背后有股" + TxtUtil.setColor("更强大的力量") + "支撑着。"));
         this._dic["SECRET_FIRST"].push(new DialogVO(0,"王大头","WANG_DA_TOU001","什么？鬼怪？？？我滴妈妈呀！我好虚啊......"));
         this._dic["SECRET_FIRST"].push(new DialogVO(0,"豆娃","DOU_WA003","怕啥，之前的那股劲呢！你看你到现在已经战绩辉煌了！"));
         this._dic["SECRET_FIRST"].push(new DialogVO(0,"王大头","WANG_DA_TOU005","唉。看来我也没的选择了。"));
         this._dic["SECRET_FIRST"].push(new DialogVO(0,"豆娃","DOU_WA003","还有我在呢，放心！另外告诉你，这秘境里可是有很多宝贝的！一定让你如虎添翼！"));
         this._dic["FENG_GAO_START"] = new Vector.<DialogVO>();
         this._dic["FENG_GAO_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU010","兖州的实力也不怎么样么？前几座小城也太容易了，哈哈哈哈！"));
         this._dic["FENG_GAO_START"].push(new DialogVO(0,"王平","WANG_PING","哪来的黄毛小二在这里嘀嘀咕咕！我正在等个重要人物，别在这里碍事！速速离去！"));
         this._dic["FENG_GAO_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","喂~是在等爷我么？难道想拜入我的账下？"));
         this._dic["FENG_GAO_START"].push(new DialogVO(0,"王平","WANG_PING","小子少信口雌黄，不给你颜色看看，真当兖州无人了不成！让你见识见识火桶阵的厉害！"));
         this._dic["WU_YAN_START"] = new Vector.<DialogVO>();
         this._dic["WU_YAN_START"].push(new DialogVO(0,"张飞","ZHANG_FEI","燕人张翼德在此，是何鼠辈敢犯我城池？！"));
         this._dic["WU_YAN_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU006","（哇，居然是张飞！）张将军切勿动气，听说你特别好酒，战前不如你我先痛饮一翻，如何？来人，给张将军送酒！"));
         this._dic["WU_YAN_START"].push(new DialogVO(0,"张飞","ZHANG_FEI","有点意思，还从未见过在阵前请喝酒的！（咕咕......）好酒！好酒！！"));
         this._dic["WU_YAN_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU006","此酒乃杜康大师独家酿造，他现已拜入我账下！不知张将军是否可考虑一二，和我共创大业？"));
         this._dic["WU_YAN_START"].push(new DialogVO(0,"张飞","ZHANG_FEI","想用酒收买我？你先派三个最厉害的跟我比试比试，如能胜我，我便考虑考虑！"));
         this._dic["WU_YAN_WIN0"] = new Vector.<DialogVO>();
         this._dic["WU_YAN_WIN0"].push(new DialogVO(0,"张飞","ZHANG_FEI","我居然输了，怎么可能！"));
         this._dic["WU_YAN_WIN0"].push(new DialogVO(0,"王大头","WANG_DA_TOU006","张将军，不知我刚才的提议......？"));
         this._dic["WU_YAN_WIN0"].push(new DialogVO(0,"张飞","ZHANG_FEI","一定是刚才的酒有问题，所以我才输了！哼，卑鄙小人！"));
         this._dic["WU_YAN_WIN0"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","将军，我可不从来不干这种小人行径！你输了也别耍赖啊！"));
         this._dic["WU_YAN_WIN0"].push(new DialogVO(0,"张飞","ZHANG_FEI","我耍赖又如何！看我冲出重围！"));
         this._dic["WU_YAN_WIN1"] = new Vector.<DialogVO>();
         this._dic["WU_YAN_WIN1"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","还是被我抓住了吧！张将军，好汉不吃眼前亏啊。"));
         this._dic["WU_YAN_WIN1"].push(new DialogVO(0,"张飞","ZHANG_FEI","呸！要关要剐，悉听尊便！"));
         this._dic["WU_YAN_WIN1"].push(new DialogVO(0,"王大头","WANG_DA_TOU003","（真是粗汉子一根筋）算了，先把他押下去！"));
         this._dic["WU_YAN_WIN1"].push(new DialogVO(0,"王大头","WANG_DA_TOU008","咦，张飞身上怎么掉下来一本书？他还会看书......我靠，居然是" + TxtUtil.setColor("《冰冻结界技能书》") + "！",{"sound":"AUDIO_SURPRISE"}));
         this._dic["PU_YANG_START"] = new Vector.<DialogVO>();
         this._dic["PU_YANG_START"].push(new DialogVO(0,"潘璋","PAN_ZHANG","吾乃潘璋，听闻最近有一股势力近日迅速占领了豫州，看来就是你吧！现今想必也是想拿下这里了？"));
         this._dic["PU_YANG_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","你说的不错，不知这位潘将军何意？"));
         this._dic["PU_YANG_START"].push(new DialogVO(0,"潘璋","PAN_ZHANG","这就要看你的诚意了，倘若能把你掠来的钱财都给我，那就可以商量。"));
         this._dic["PU_YANG_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","（这潘璋果然是个爱财之人）真是贪心啊！无需商量，一战便能拿下你！"));
         this._dic["PU_YANG_START"].push(new DialogVO(0,"潘璋","PAN_ZHANG","也罢，箭阵准备！"));
         this._dic["REN_CHENG_START"] = new Vector.<DialogVO>();
         this._dic["REN_CHENG_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","敢问此城内的可是羊祜羊大将军？"));
         this._dic["REN_CHENG_START"].push(new DialogVO(0,"羊祜","YANG_GU","正是，来犯者认得老夫？"));
         this._dic["REN_CHENG_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU006","羊大人博学能文，清廉正直有所耳闻，何不拜入我账下，共谋大事？"));
         this._dic["REN_CHENG_START"].push(new DialogVO(0,"羊祜","YANG_GU","想让我拱手相让此城是不可能的，还是战场见高低吧！"));
         this._dic["REN_CHENG_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","那在下就得罪了！攻城！"));
         this._dic["CHANG_YI_START"] = new Vector.<DialogVO>();
         this._dic["CHANG_YI_START"].push(new DialogVO(0,"孙坚","SUN_JIAN","受故人所托让我在此等候与你，试试你进步了多少！"));
         this._dic["CHANG_YI_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","故人所托？难道我出现在这里是有人操控的？！"));
         this._dic["CHANG_YI_START"].push(new DialogVO(0,"孙坚","SUN_JIAN","毛头小子，战斗中别乱想。我提醒你一句，昌邑常年大雪弥漫，" + TxtUtil.setColor("地面会刺出冰柱") + "，你可小心了！而我方已经适应这天气，不会中招。"));
         this._dic["CHANG_YI_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU005","......看来这次又要吃亏了！"));
         this._dic["PHONE110"] = new Vector.<DialogVO>();
         this._dic["PHONE110"].push(new DialogVO(0,"警察","ICON_COMMON","你好，这里是110，请问需要什么帮助？"));
         this._dic["PHONE110"].push(new DialogVO(0,"王大头","WANG_DA_TOU001","（居然打通了！）警察叔叔救命啊，我被人绑架了！"));
         this._dic["PHONE110"].push(new DialogVO(0,"警察","ICON_COMMON","能告诉我你现在的具体位置吗？"));
         this._dic["PHONE110"].push(new DialogVO(0,"王大头","WANG_DA_TOU001","我现在在大都城，快点来啊！"));
         this._dic["PHONE110"].push(new DialogVO(0,"警察","ICON_COMMON","什么？大都城是哪里？"));
         this._dic["PHONE110"].push(new DialogVO(0,"王大头","WANG_DA_TOU001","就是我的主城.....对了对了，我我现在在一个电子游戏里面！"));
         this._dic["PHONE110"].push(new DialogVO(0,"警察","ICON_COMMON","电子游戏里面？你在开玩笑吗？"));
         this._dic["PHONE110"].push(new DialogVO(0,"王大头","WANG_DA_TOU001","是真的啊！我还跟4399的豆娃说话呢，而且周围都是三国的人！"));
         this._dic["PHONE110"].push(new DialogVO(0,"警察","ICON_COMMON","这位同志，拨打110开这样的玩笑是可以追究你的责任的，请不要有下次，再见！"));
         this._dic["PHONE_START"] = new Vector.<DialogVO>();
         this._dic["PHONE_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU008","咦，这不是我的手机么？怎么在国库里？"));
         this._dic["PHONE_START"].push(new DialogVO(0,"豆娃","DOU_WA005","淡定......你来的时候随身掉下来的，然后我就直接帮你放国库了。"));
         this._dic["PHONE_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","原来如此，不过手机在这里估计也没什么用了！"));
         this._dic["PHONE_START"].push(new DialogVO(0,"豆娃","DOU_WA003","那也不一定......"));
         this._dic["PHONE_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU004","难道三国里的人还会用手机？啊哈哈哈！"));
         this._dic["PHONE_START"].push(new DialogVO(0,"豆娃","DOU_WA003","天机不可泄露，自己慢慢参悟吧！"));
         this._dic["PHONE_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU005","神神叨叨的......"));
         this._dic["PHONE4399"] = new Vector.<DialogVO>();
         this._dic["PHONE4399"].push(new DialogVO(0,"？？？","ICON_COMMON","咕噜咕噜，正在传送......"));
         this._dic["PHONE4399"].push(new DialogVO(0,"王大头","WANG_DA_TOU008","什么玩意？"));
         this._dic["PHONE4399"].push(new DialogVO(0,"王大头","WANG_DA_TOU001","啊————————————————————"));
         this._dic["JU_XIAN_START"] = new Vector.<DialogVO>();
         this._dic["JU_XIAN_START"].push(new DialogVO(0,"马岱","MA_DAI","好一个郭达，吃我的用我的，把你供着，现在不想跟着我干了？"));
         this._dic["JU_XIAN_START"].push(new DialogVO(0,"郭达","ICON_GUO_DA_HEAD","呸，天天让老子起早摸黑的造兵器，都瘦了十几斤，早晚受累，还不如走为上计！"));
         this._dic["JU_XIAN_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","（原来郭达在这里！）郭达先生，速速过来，我护送你离开！"));
         this._dic["JU_XIAN_START"].push(new DialogVO(0,"马岱","MA_DAI","嗯？！竟然还有接应，看来你很早就有叛逃之心，既然不能为我所用，那留你又有何用！"));
         this._dic["JU_XIAN_START"].push(new DialogVO(0,"郭达","ICON_GUO_DA_HEAD","哼，怕你不成！小子，虽不知救我出何目的，不如先你我联手先灭了他！"));
         this._dic["JU_XIAN_WIN"] = new Vector.<DialogVO>();
         this._dic["JU_XIAN_WIN"].push(new DialogVO(0,"郭达","ICON_GUO_DA_HEAD","哈哈哈，你小子部下还是有点实力，看你们手中兵器像是我那徒儿所造，我也好久没见他了，能否带我去看看？"));
         this._dic["JU_XIAN_WIN"].push(new DialogVO(0,"王大头","WANG_DA_TOU006","当然可以，郭先生请随我来！"));
         this._dic["DONG_PING_START"] = new Vector.<DialogVO>();
         this._dic["DONG_PING_START"].push(new DialogVO(0,"鲍信","BAO_XIN","哼，城外的小贼，可也是想要七星圣灯的？"));
         this._dic["DONG_PING_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","哦？" + TxtUtil.setColor("七星圣灯") + "？好像哪里听说过，不知城主能否让我观摩观摩？"));
         this._dic["DONG_PING_START"].push(new DialogVO(0,"鲍信","BAO_XIN","哼，此物并不在城内，如何让你观摩？"));
         this._dic["DONG_PING_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","如果没有的话，为何大门紧闭，重兵把手？岂不是此地无银三百两？！"));
         this._dic["DONG_PING_START"].push(new DialogVO(0,"鲍信","BAO_XIN","你~你~好一个信口雌黄的小子，今天就让你见识见识我的厉害！"));
         this._dic["DONG_PING_MAI_FU"] = new Vector.<DialogVO>();
         this._dic["DONG_PING_MAI_FU"].push(new DialogVO(0,"鲍信","BAO_XIN","小子有点实力，不过吾也非等闲之辈！你中计了！出来吧！"));
         this._dic["DONG_PING_WIN"] = new Vector.<DialogVO>();
         this._dic["DONG_PING_WIN"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","呼，一时大意中埋伏了！还好我军骁勇善战！"));
         this._dic["DONG_PING_WIN"].push(new DialogVO(0,"鲍信","BAO_XIN","哼，成王败寇！七星圣灯就在城内，拿去吧！"));
         this._dic["DONG_PING_WIN"].push(new DialogVO(0,"王大头","WANG_DA_TOU004","果然有七星圣灯，称霸道路上又多了个保障，好东西呀！啊哈哈！"));
         this._dic["LIN_JI_START"] = new Vector.<DialogVO>();
         this._dic["LIN_JI_START"].push(new DialogVO(0,"张合","ZHANG_HE","大头老弟，吾在此等候多时了！吾朴刀精锐已养兵数日，这城可不好破！"));
         this._dic["LIN_JI_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU008","阁下是？我们好像没见过吧！"));
         this._dic["LIN_JI_START"].push(new DialogVO(0,"张合","ZHANG_HE","我们确实没见过，吾乃张合！早在曹公那听闻你的事情！"));
         this._dic["LIN_JI_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","原来如此，其实张将军投靠曹操，不如投靠我门下，共谋天下可好？"));
         this._dic["LIN_JI_START"].push(new DialogVO(0,"张合","ZHANG_HE","当年我投曹怕被郭图这小人诬陷，才出此下策，还好曹公不计前嫌重用我，而你默默无闻，手下虽有几位名将，但总体实力如何和曹公相比？"));
         this._dic["LIN_JI_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","既然如此，便让张将军看看我的实力如何！"));
         this._dic["LIN_JI_START"].push(new DialogVO(0,"张合","ZHANG_HE","好，我也不为难你，看你近战士兵颇有疲惫，你我用" + TxtUtil.setColor("远程士兵") + "一决高下如何？"));
         this._dic["LIN_JI_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","乐意奉陪！！"));
         this._dic["HUANG_XIAN_START"] = new Vector.<DialogVO>();
         this._dic["HUANG_XIAN_START"].push(new DialogVO(0,"曹洪","CAO_HONG","士别三日当刮目相看，大头兄，别来无恙啊！"));
         this._dic["HUANG_XIAN_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU008","又一个认识我的，你我见过面？"));
         this._dic["HUANG_XIAN_START"].push(new DialogVO(0,"曹洪","CAO_HONG","在下曹洪，当日许昌，吾也在城楼之上观战！"));
         this._dic["HUANG_XIAN_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","又是曹操的人，他到底想干嘛......又是他派来考验我的咯？"));
         this._dic["HUANG_XIAN_START"].push(new DialogVO(0,"曹洪","CAO_HONG","不错，主公让我试你调兵之能！吾会派出" + TxtUtil.setColor("精英首领") + "向你讨教！请！"));
         this._dic["HUANG_XIAN_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU009","哼哼，来吧，看我如何将你拿下！"));
         this._dic["PING_YUAN_START0"] = new Vector.<DialogVO>();
         this._dic["PING_YUAN_START0"].push(new DialogVO(0,"刘备","LIU_BEI","来者何人？敢犯我刘玄德城池？"));
         this._dic["PING_YUAN_START0"].push(new DialogVO(0,"王大头","WANG_DA_TOU008","原来是玄德啊！你结义兄弟关羽张飞已入我麾下，你何不一起归顺？"));
         this._dic["PING_YUAN_START0"].push(new DialogVO(0,"刘备","LIU_BEI","什么？我二弟三弟何在！"));
         this._dic["PING_YUAN_START0"].push(new DialogVO(0,"关羽","GUAN_YU","大哥，二弟关羽在此！"));
         this._dic["PING_YUAN_START0"].push(new DialogVO(0,"张飞","ZHANG_FEI","大哥，这大头主公是个明主，你就俺们一起吧！"));
         this._dic["PING_YUAN_START0"].push(new DialogVO(0,"刘备","LIU_BEI","二弟、三弟，你们这是为何？哎！"));
         this._dic["PING_YUAN_START0"].push(new DialogVO(0,"张飞","ZHANG_FEI","大哥，这事怪我，这小子用杜康美酒算计我，我又稀里糊涂地拉着二哥和这小子也结了义......不信你问二哥！"));
         this._dic["PING_YUAN_START0"].push(new DialogVO(0,"王大头","WANG_DA_TOU009","玄德，怎么样？考虑一下吧！"));
         this._dic["PING_YUAN_START0"].push(new DialogVO(0,"刘备","LIU_BEI","难道这就是天意？也罢，让我先摸摸你的实力！二弟三弟，你们且先退下！"));
         this._dic["PING_YUAN_START1"] = new Vector.<DialogVO>();
         this._dic["PING_YUAN_START1"].push(new DialogVO(0,"刘备","LIU_BEI","来者何人？敢犯我刘玄德城池？"));
         this._dic["PING_YUAN_START1"].push(new DialogVO(0,"王大头","WANG_DA_TOU006","原来是玄德啊！如今这天下战乱纷纷，你何不与我一起平定这乱世？"));
         this._dic["PING_YUAN_START1"].push(new DialogVO(0,"刘备","LIU_BEI","好意我心领了，我自有打算！况且如今我二弟三弟也尚不知在何处，想让我归顺于你，那是不可能的！"));
         this._dic["PING_YUAN_START1"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","我如今手下名将云集，杜康、于吉、郭达等大师也亦愿意助我一臂之力，玄德你真不考虑下？"));
         this._dic["PING_YUAN_START1"].push(new DialogVO(0,"刘备","LIU_BEI","哎，当年我也曾想招募过他们，可惜没你这机缘，难道都是天意如此么？！"));
         this._dic["PING_YUAN_START1"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","玄德不必自叹，你我城下相见也是机缘，还是考虑一下如何？"));
         this._dic["PING_YUAN_START1"].push(new DialogVO(0,"刘备","LIU_BEI","也罢，先让我摸摸你的实力！"));
         this._dic["PING_YUAN_WIN"] = new Vector.<DialogVO>();
         this._dic["PING_YUAN_WIN"].push(new DialogVO(0,"刘备","LIU_BEI","厉害，确实有些实力！"));
         this._dic["PING_YUAN_WIN"].push(new DialogVO(0,"王大头","WANG_DA_TOU006","玄德，你就不要瞻前顾后了，跟我一起吧！"));
         this._dic["PING_YUAN_WIN"].push(new DialogVO(0,"刘备","LIU_BEI","这......你容我" + TxtUtil.setColor("考虑四五日") + "，我会给你答复的，你到时候来" + TxtUtil.setColor("大都酒馆") + "找我吧！"));
         this._dic["PING_YUAN_WIN"].push(new DialogVO(0,"王大头","WANG_DA_TOU006","好吧，那一言为定，玄德慢走！"));
         this._dic["XIA_PI_START"] = new Vector.<DialogVO>();
         this._dic["XIA_PI_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","前方可是文长将军？"));
         this._dic["XIA_PI_START"].push(new DialogVO(0,"魏延","WEI_YAN","正是我，你不就是最近风头正盛的王大头吗？找我何事，想攻我城池？"));
         this._dic["XIA_PI_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","你家主公玄德叫我来招降你，文长，为我所用，咱一起打天下！"));
         this._dic["XIA_PI_START"].push(new DialogVO(0,"魏延","WEI_YAN","哼，就你，主公居然投靠于你。也罢，和我过几招让我看看你的能耐！"));
         this._dic["GUANG_LIN_START"] = new Vector.<DialogVO>();
         this._dic["GUANG_LIN_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","孟德！终于找到你了，三番五次试探于我，有何目的，今日我要将你擒拿！问个明白！"));
         this._dic["GUANG_LIN_START"].push(new DialogVO(0,"曹操","CAO_CAO","骄兵必败，你如此目中无人，迟早会尝到失败的滋味！"));
         this._dic["GUANG_LIN_START"].push(new DialogVO(0,"典韦","DIAN_WEI","我来拦住这厮，主公你先行离开。"));
         this._dic["GUANG_LIN_START"].push(new DialogVO(0,"曹操","CAO_CAO","恶来，全靠你了！"));
         this._dic["LE_CHENG_START"] = new Vector.<DialogVO>();
         this._dic["LE_CHENG_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","孟德，现在天下已经快要尽入我手，这次必要将你拿下！"));
         this._dic["LE_CHENG_START"].push(new DialogVO(0,"曹操","CAO_CAO","哈哈，年轻人，你还是真不知道老夫的良苦用心啊！也罢，如今你兵强马壮，让老夫最后来试试你吧！"));
         this._dic["LE_CHENG_START"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","切，这曹操老儿，这时候还要面子！众将士，一举拿下！"));
         this._dic["LE_CHENG_WIN"] = new Vector.<DialogVO>();
         this._dic["LE_CHENG_WIN"].push(new DialogVO(0,"曹操","CAO_CAO","王大头，果然我没看错人！哎！ 五年来，南蛮、羌、匈奴对我虎视眈眈，可吴蜀只出工，不出力，而你的出现正好解决了这个格局！"));
         this._dic["LE_CHENG_WIN"].push(new DialogVO(0,"王大头","WANG_DA_TOU008","孟德......你是战败开始胡言乱语么？"));
         this._dic["LE_CHENG_WIN"].push(new DialogVO(0,"曹操","CAO_CAO","三国鼎立以来，内斗不断，其实已是内忧外患！而你的出现不会引起吴蜀的重视，加上我的手下和你一起打江山，等吴蜀重视起来为时已晚！"));
         this._dic["LE_CHENG_WIN"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","你意思是故意让魏国的大将投降我的么......你为何要暗地助我一统天下？"));
         this._dic["LE_CHENG_WIN"].push(new DialogVO(0,"曹操","CAO_CAO","统一才会让国家更强大，而我只不过在履行一个预言：" + TxtUtil.setColor("非三国之人必能解救天下苍生") + "！！"));
         this._dic["LE_CHENG_WIN"].push(new DialogVO(0,"王大头","WANG_DA_TOU007","既然知道我非三国之人，那你肯定也知道我回去之路？"));
         this._dic["LE_CHENG_WIN"].push(new DialogVO(0,"曹操","CAO_CAO","知道，只要你能让" + TxtUtil.setColor("南蛮、羌、匈奴") + "打消入侵的念头，并去解救那些抵御外侵而失联的武将，我便告诉你预言后半句。"));
         this._dic["LE_CHENG_WIN"].push(new DialogVO(0,"王大头","WANG_DA_TOU011","好，一言为定！不知孟德大人，这算加入了么？"));
         this._dic["LE_CHENG_WIN"].push(new DialogVO(0,"曹操","CAO_CAO","哈哈，等你解救那些武将回来，我再考虑！听说你杜康这老鬼在" + TxtUtil.setColor("大都集市") + "？一别十年真想念那老鬼酿的那味道，我这就去找他，哈哈！"));
         this._dic["LE_CHENG_WIN"].push(new DialogVO(0,"王大头","WANG_DA_TOU010","孟德大人，请！叫上那老鬼我们不醉不归！嘿嘿！"));
      }
      
      public function findDialog(param1:String) : Vector.<DialogVO>
      {
         return this._dic[param1];
      }
   }
}

