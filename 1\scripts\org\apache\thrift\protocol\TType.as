package org.apache.thrift.protocol
{
   public class TType
   {
      
      public static const STOP:int = 0;
      
      public static const VOID:int = 1;
      
      public static const BOOL:int = 2;
      
      public static const BYTE:int = 3;
      
      public static const DOUBLE:int = 4;
      
      public static const I16:int = 6;
      
      public static const I32:int = 8;
      
      public static const I64:int = 10;
      
      public static const STRING:int = 11;
      
      public static const STRUCT:int = 12;
      
      public static const MAP:int = 13;
      
      public static const SET:int = 14;
      
      public static const LIST:int = 15;
      
      public function TType()
      {
         super();
      }
   }
}

