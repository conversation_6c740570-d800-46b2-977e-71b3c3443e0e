#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三国存档编辑器 - Python版本
支持GUI界面和命令行操作
"""

import json
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from datetime import datetime
import copy

class SaveEditor:
    def __init__(self):
        self.save_data = None
        self.original_data = None
        self.file_path = None
        
        # 英雄数据库
        self.hero_db = {
            433: {"name": "典韦", "faction": "魏", "type": "武将", "rarity": 5},
            390: {"name": "关羽", "faction": "蜀", "type": "武将", "rarity": 5},
            358: {"name": "赵云", "faction": "蜀", "type": "武将", "rarity": 5},
            379: {"name": "吕布", "faction": "群", "type": "武将", "rarity": 5},
            548: {"name": "鲁肃", "faction": "吴", "type": "谋士", "rarity": 4},
            392: {"name": "太史慈", "faction": "吴", "type": "武将", "rarity": 4},
            422: {"name": "魏延", "faction": "蜀", "type": "武将", "rarity": 4},
            457: {"name": "马超", "faction": "蜀", "type": "武将", "rarity": 5},
            454: {"name": "张辽", "faction": "魏", "type": "武将", "rarity": 5},
            455: {"name": "华佗", "faction": "群", "type": "医师", "rarity": 4},
        }
        
        # 物品数据库
        self.item_db = {
            11071: {"name": "嘲讽卷轴", "type": "消耗品", "rarity": 2},
            11002: {"name": "初级回春卷轴", "type": "恢复道具", "rarity": 1},
            11012: {"name": "中级行军酒", "type": "增益道具", "rarity": 2},
            11005: {"name": "初级治愈卷轴", "type": "恢复道具", "rarity": 1},
            11003: {"name": "中级回春卷轴", "type": "恢复道具", "rarity": 2},
            11601: {"name": "初级兽血卷轴", "type": "增益道具", "rarity": 2},
            11101: {"name": "初级禽卷轴", "type": "特殊道具", "rarity": 2},
        }

    def load_save(self, file_path):
        """加载存档文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.save_data = json.load(f)
            self.original_data = copy.deepcopy(self.save_data)
            self.file_path = file_path
            return True, "存档加载成功"
        except Exception as e:
            return False, f"加载失败: {str(e)}"

    def save_file(self, file_path=None):
        """保存存档文件"""
        if not self.save_data:
            return False, "没有数据可保存"
        
        save_path = file_path or self.file_path
        if not save_path:
            return False, "未指定保存路径"
        
        try:
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(self.save_data, f, ensure_ascii=False, indent=2)
            return True, "保存成功"
        except Exception as e:
            return False, f"保存失败: {str(e)}"

    def get_resources(self):
        """获取资源数据"""
        if not self.save_data or 'master' not in self.save_data:
            return None
        
        values = self.save_data['master'].get('values', '').split('H')
        return {
            'silver': int(values[0]) if len(values) > 0 and values[0].isdigit() else 0,
            'prestige': int(values[1]) if len(values) > 1 and values[1].isdigit() else 0,
            'energy': int(values[2]) if len(values) > 2 and values[2].isdigit() else 0,
            'gold': int(values[3]) if len(values) > 3 and values[3].isdigit() else 0,
        }

    def update_resources(self, silver=None, prestige=None, energy=None, gold=None):
        """更新资源数据"""
        if not self.save_data or 'master' not in self.save_data:
            return False, "存档数据不完整"
        
        try:
            values = self.save_data['master'].get('values', '').split('H')
            
            # 确保values列表足够长
            while len(values) < 15:
                values.append('0')
            
            if silver is not None:
                values[0] = str(max(0, min(999999999, int(silver))))
            if prestige is not None:
                values[1] = str(max(0, min(999999999, int(prestige))))
            if energy is not None:
                values[2] = str(max(0, min(999, int(energy))))
            if gold is not None:
                values[3] = str(max(0, min(999999, int(gold))))
            
            self.save_data['master']['values'] = 'H'.join(values)
            return True, "资源更新成功"
        except Exception as e:
            return False, f"更新失败: {str(e)}"

    def get_heroes(self):
        """获取英雄列表"""
        if not self.save_data or 'hero' not in self.save_data:
            return []
        
        heroes = []
        for i, hero in enumerate(self.save_data['hero']):
            hero_id = hero.get('id', 0)
            hero_info = self.hero_db.get(hero_id, {
                "name": f"未知英雄{hero_id}",
                "faction": "未知",
                "type": "未知",
                "rarity": 1
            })
            
            base_stats = hero.get('base', '0H0H0H0H0H0H0').split('H')
            
            heroes.append({
                'index': i,
                'id': hero_id,
                'name': hero_info['name'],
                'faction': hero_info['faction'],
                'type': hero_info['type'],
                'rarity': hero_info['rarity'],
                'level': int(base_stats[0]) if len(base_stats) > 0 and base_stats[0].isdigit() else 0,
                'star': int(base_stats[1]) if len(base_stats) > 1 and base_stats[1].isdigit() else 0,
                'experience': int(base_stats[2]) if len(base_stats) > 2 and base_stats[2].isdigit() else 0,
            })
        
        return heroes

    def update_hero(self, hero_index, level=None, star=None, experience=None):
        """更新英雄数据"""
        if not self.save_data or 'hero' not in self.save_data:
            return False, "存档数据不完整"
        
        if hero_index >= len(self.save_data['hero']):
            return False, "英雄索引超出范围"
        
        try:
            hero = self.save_data['hero'][hero_index]
            base_stats = hero.get('base', '0H0H0H0H0H0H0').split('H')
            
            # 确保base_stats列表足够长
            while len(base_stats) < 7:
                base_stats.append('0')
            
            if level is not None:
                base_stats[0] = str(max(0, min(200, int(level))))
            if star is not None:
                base_stats[1] = str(max(0, min(10, int(star))))
            if experience is not None:
                base_stats[2] = str(max(0, int(experience)))
            
            hero['base'] = 'H'.join(base_stats)
            return True, "英雄更新成功"
        except Exception as e:
            return False, f"更新失败: {str(e)}"

    def get_items(self):
        """获取物品列表"""
        if not self.save_data or 'bagGood' not in self.save_data:
            return []
        
        items = []
        for i, item in enumerate(self.save_data['bagGood']):
            parts = item.split('H')
            if len(parts) >= 2:
                count = int(parts[0]) if parts[0].isdigit() else 0
                item_id = int(parts[1]) if parts[1].isdigit() else 0
                
                item_info = self.item_db.get(item_id, {
                    "name": f"未知物品{item_id}",
                    "type": "未知",
                    "rarity": 1
                })
                
                items.append({
                    'index': i,
                    'id': item_id,
                    'name': item_info['name'],
                    'type': item_info['type'],
                    'rarity': item_info['rarity'],
                    'count': count,
                })
        
        return items

    def backup_save(self):
        """创建存档备份"""
        if not self.file_path:
            return False, "没有原始文件路径"
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{self.file_path}.backup_{timestamp}"
            
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(self.original_data, f, ensure_ascii=False, indent=2)
            
            return True, f"备份创建成功: {backup_path}"
        except Exception as e:
            return False, f"备份失败: {str(e)}"


class SaveEditorGUI:
    def __init__(self):
        self.editor = SaveEditor()
        self.root = tk.Tk()
        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        self.root.title("三国存档编辑器")
        self.root.geometry("800x600")
        
        # 创建菜单栏
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="打开存档", command=self.open_file)
        file_menu.add_command(label="保存存档", command=self.save_file)
        file_menu.add_command(label="另存为", command=self.save_as)
        file_menu.add_separator()
        file_menu.add_command(label="创建备份", command=self.create_backup)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建标签页
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 资源管理标签页
        self.create_resource_tab()
        
        # 英雄管理标签页
        self.create_hero_tab()
        
        # 物品管理标签页
        self.create_item_tab()
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def create_resource_tab(self):
        """创建资源管理标签页"""
        resource_frame = ttk.Frame(self.notebook)
        self.notebook.add(resource_frame, text="💰 资源管理")
        
        # 资源输入框
        ttk.Label(resource_frame, text="银币:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.silver_var = tk.StringVar()
        ttk.Entry(resource_frame, textvariable=self.silver_var, width=15).grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(resource_frame, text="威望:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.prestige_var = tk.StringVar()
        ttk.Entry(resource_frame, textvariable=self.prestige_var, width=15).grid(row=1, column=1, padx=5, pady=5)
        
        ttk.Label(resource_frame, text="体力:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.energy_var = tk.StringVar()
        ttk.Entry(resource_frame, textvariable=self.energy_var, width=15).grid(row=2, column=1, padx=5, pady=5)
        
        ttk.Label(resource_frame, text="元宝:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.gold_var = tk.StringVar()
        ttk.Entry(resource_frame, textvariable=self.gold_var, width=15).grid(row=3, column=1, padx=5, pady=5)
        
        # 更新按钮
        ttk.Button(resource_frame, text="更新资源", command=self.update_resources).grid(row=4, column=0, columnspan=2, pady=10)

    def create_hero_tab(self):
        """创建英雄管理标签页"""
        hero_frame = ttk.Frame(self.notebook)
        self.notebook.add(hero_frame, text="⚔️ 英雄管理")
        
        # 英雄列表
        columns = ('ID', '姓名', '阵营', '类型', '等级', '星级', '经验')
        self.hero_tree = ttk.Treeview(hero_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.hero_tree.heading(col, text=col)
            self.hero_tree.column(col, width=80)
        
        self.hero_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 英雄编辑框架
        edit_frame = ttk.LabelFrame(hero_frame, text="编辑英雄")
        edit_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(edit_frame, text="等级:").grid(row=0, column=0, padx=5, pady=2)
        self.hero_level_var = tk.StringVar()
        ttk.Entry(edit_frame, textvariable=self.hero_level_var, width=10).grid(row=0, column=1, padx=5, pady=2)
        
        ttk.Label(edit_frame, text="星级:").grid(row=0, column=2, padx=5, pady=2)
        self.hero_star_var = tk.StringVar()
        ttk.Entry(edit_frame, textvariable=self.hero_star_var, width=10).grid(row=0, column=3, padx=5, pady=2)
        
        ttk.Label(edit_frame, text="经验:").grid(row=0, column=4, padx=5, pady=2)
        self.hero_exp_var = tk.StringVar()
        ttk.Entry(edit_frame, textvariable=self.hero_exp_var, width=10).grid(row=0, column=5, padx=5, pady=2)
        
        ttk.Button(edit_frame, text="更新英雄", command=self.update_hero).grid(row=0, column=6, padx=5, pady=2)
        
        # 绑定选择事件
        self.hero_tree.bind('<<TreeviewSelect>>', self.on_hero_select)

    def create_item_tab(self):
        """创建物品管理标签页"""
        item_frame = ttk.Frame(self.notebook)
        self.notebook.add(item_frame, text="🎒 物品管理")
        
        # 物品列表
        columns = ('ID', '名称', '类型', '稀有度', '数量')
        self.item_tree = ttk.Treeview(item_frame, columns=columns, show='headings', height=20)
        
        for col in columns:
            self.item_tree.heading(col, text=col)
            self.item_tree.column(col, width=100)
        
        self.item_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def open_file(self):
        """打开存档文件"""
        file_path = filedialog.askopenfilename(
            title="选择存档文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        
        if file_path:
            success, message = self.editor.load_save(file_path)
            if success:
                self.load_data()
                self.status_var.set(f"已加载: {os.path.basename(file_path)}")
            else:
                messagebox.showerror("错误", message)

    def load_data(self):
        """加载数据到界面"""
        # 加载资源数据
        resources = self.editor.get_resources()
        if resources:
            self.silver_var.set(str(resources['silver']))
            self.prestige_var.set(str(resources['prestige']))
            self.energy_var.set(str(resources['energy']))
            self.gold_var.set(str(resources['gold']))
        
        # 加载英雄数据
        self.load_heroes()
        
        # 加载物品数据
        self.load_items()

    def load_heroes(self):
        """加载英雄数据到列表"""
        # 清空现有数据
        for item in self.hero_tree.get_children():
            self.hero_tree.delete(item)
        
        # 添加英雄数据
        heroes = self.editor.get_heroes()
        for hero in heroes:
            self.hero_tree.insert('', 'end', values=(
                hero['id'], hero['name'], hero['faction'], hero['type'],
                hero['level'], hero['star'], hero['experience']
            ))

    def load_items(self):
        """加载物品数据到列表"""
        # 清空现有数据
        for item in self.item_tree.get_children():
            self.item_tree.delete(item)
        
        # 添加物品数据
        items = self.editor.get_items()
        for item in items:
            self.item_tree.insert('', 'end', values=(
                item['id'], item['name'], item['type'], 
                item['rarity'], item['count']
            ))

    def update_resources(self):
        """更新资源"""
        try:
            silver = int(self.silver_var.get()) if self.silver_var.get() else None
            prestige = int(self.prestige_var.get()) if self.prestige_var.get() else None
            energy = int(self.energy_var.get()) if self.energy_var.get() else None
            gold = int(self.gold_var.get()) if self.gold_var.get() else None
            
            success, message = self.editor.update_resources(silver, prestige, energy, gold)
            if success:
                self.status_var.set(message)
            else:
                messagebox.showerror("错误", message)
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字")

    def on_hero_select(self, event):
        """英雄选择事件"""
        selection = self.hero_tree.selection()
        if selection:
            item = self.hero_tree.item(selection[0])
            values = item['values']
            if len(values) >= 7:
                self.hero_level_var.set(str(values[4]))
                self.hero_star_var.set(str(values[5]))
                self.hero_exp_var.set(str(values[6]))

    def update_hero(self):
        """更新英雄"""
        selection = self.hero_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个英雄")
            return
        
        try:
            hero_index = self.hero_tree.index(selection[0])
            level = int(self.hero_level_var.get()) if self.hero_level_var.get() else None
            star = int(self.hero_star_var.get()) if self.hero_star_var.get() else None
            experience = int(self.hero_exp_var.get()) if self.hero_exp_var.get() else None
            
            success, message = self.editor.update_hero(hero_index, level, star, experience)
            if success:
                self.load_heroes()  # 重新加载英雄列表
                self.status_var.set(message)
            else:
                messagebox.showerror("错误", message)
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字")

    def save_file(self):
        """保存文件"""
        success, message = self.editor.save_file()
        if success:
            self.status_var.set(message)
        else:
            messagebox.showerror("错误", message)

    def save_as(self):
        """另存为"""
        file_path = filedialog.asksaveasfilename(
            title="保存存档文件",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        
        if file_path:
            success, message = self.editor.save_file(file_path)
            if success:
                self.status_var.set(f"已保存: {os.path.basename(file_path)}")
            else:
                messagebox.showerror("错误", message)

    def create_backup(self):
        """创建备份"""
        success, message = self.editor.backup_save()
        if success:
            self.status_var.set(message)
        else:
            messagebox.showerror("错误", message)

    def run(self):
        """运行GUI"""
        self.root.mainloop()


if __name__ == "__main__":
    app = SaveEditorGUI()
    app.run()
