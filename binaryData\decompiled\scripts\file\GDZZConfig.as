package file
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class GDZZConfig
   {
      
      private static var _instance:GDZZConfig;
      
      public var waveData:WaveDataVO;
      
      public var showTime:int;
      
      public var needTime:int;
      
      public var keepTime:int;
      
      public var speed:int;
      
      public var atkPer:int;
      
      public var hpPer:int;
      
      public function GDZZConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : GDZZConfig
      {
         if(!_instance)
         {
            _instance = new GDZZConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         var _loc1_:OneWaveVO = null;
         this.showTime = 10;
         this.needTime = 4;
         this.keepTime = 8;
         this.speed = 55;
         this.hpPer = 0;
         this.atkPer = 10;
         this.waveData = new WaveDataVO(0);
         this.waveData.limitBR = new WaveLimitVO(3000,1.2,1.3);
         this.waveData.zhuBoss = new BossArgVO(528,300000,1100,120,50,50,150,150,new BossSkillData1(100,{
            "hurt":2050,
            "hpPer":10
         },8),0,12381,0);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(101,1000,150,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(102,1000,150,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(103,1000,150,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(104,1000,150,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(105,1000,150,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(106,1000,150,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(107,1000,150,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(108,1000,150,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(109,1000,150,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(110,1000,150,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(111,1000,150,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(112,1000,150,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
      }
   }
}

