package file
{
   import mogames.gameData.base.func.HeiShiVO;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.mall.vo.MallGoldVO;
   import mogames.gameData.mall.vo.MallMoneyVO;
   
   public class HeiShiConfig
   {
      
      private static var _instance:HeiShiConfig;
      
      public var list:Array;
      
      public function HeiShiConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : HeiShiConfig
      {
         if(!_instance)
         {
            _instance = new HeiShiConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.list = [];
         this.list[this.list.length] = new HeiShiVO(272,[new MallGoldVO(40000,new BaseRewardVO(10274,1)),new MallGoldVO(50000,new BaseRewardVO(10275,1)),new MallGoldVO(60000,new BaseRewardVO(10276,1)),new MallGoldVO(70000,new BaseRewardVO(10277,1)),new MallGoldVO(80000,new BaseRewardVO(10278,1)),new MallGoldVO(90000,new BaseRewardVO(10279,1)),new MallGoldVO(20000,new BaseRewardVO(68001,1)),new MallGoldVO(20000,new BaseRewardVO(68002,1)),new MallGoldVO(20000,new BaseRewardVO(10980,1)),new MallGoldVO(5000,new BaseRewardVO(10301,1)),new MallGoldVO(20000,new BaseRewardVO(10102,1)),new MallGoldVO(20000,new BaseRewardVO(10304,3)),new MallGoldVO(50000,new BaseRewardVO(10306,3)),new MallGoldVO(10000,new BaseRewardVO(10250,2)),new MallGoldVO(100000,new BaseRewardVO(10250,1))],[new MallMoneyVO(3677,15,new BaseRewardVO(11401,2)),new MallMoneyVO(3677,15,new BaseRewardVO(11401,3)),new MallMoneyVO(3677,15,new BaseRewardVO(11401,1)),new MallMoneyVO(3954,35,new BaseRewardVO(10853,1)),new MallMoneyVO(3955,100,new BaseRewardVO(10551
         ,1)),new MallMoneyVO(3955,100,new BaseRewardVO(10563,1)),new MallMoneyVO(3955,100,new BaseRewardVO(10572,1)),new MallMoneyVO(3955,100,new BaseRewardVO(10558,1)),new MallMoneyVO(3779,19,new BaseRewardVO(10508,1)),new MallMoneyVO(3779,19,new BaseRewardVO(10509,1))]);
         this.list[this.list.length] = new HeiShiVO(273,[new MallGoldVO(40000,new BaseRewardVO(10274,1)),new MallGoldVO(50000,new BaseRewardVO(10275,1)),new MallGoldVO(60000,new BaseRewardVO(10276,1)),new MallGoldVO(70000,new BaseRewardVO(10277,1)),new MallGoldVO(80000,new BaseRewardVO(10278,1)),new MallGoldVO(90000,new BaseRewardVO(10279,1)),new MallGoldVO(20000,new BaseRewardVO(68001,1)),new MallGoldVO(20000,new BaseRewardVO(68002,1)),new MallGoldVO(20000,new BaseRewardVO(10980,1)),new MallGoldVO(5000,new BaseRewardVO(10301,1)),new MallGoldVO(20000,new BaseRewardVO(10102,1)),new MallGoldVO(20000,new BaseRewardVO(10304,3)),new MallGoldVO(50000,new BaseRewardVO(10306,3)),new MallGoldVO(10000,new BaseRewardVO(10250,2)),new MallGoldVO(100000,new BaseRewardVO(10250,1)),new MallGoldVO(40000,new BaseRewardVO(10274,2)),new MallGoldVO(50000,new BaseRewardVO(10275,2)),new MallGoldVO(60000,new BaseRewardVO(10276,2)),new MallGoldVO(70000,new BaseRewardVO(10277,2)),new MallGoldVO(80000,new BaseRewardVO(10278
         ,2)),new MallGoldVO(90000,new BaseRewardVO(10279,2)),new MallGoldVO(20000,new BaseRewardVO(68001,2)),new MallGoldVO(20000,new BaseRewardVO(68002,2)),new MallGoldVO(20000,new BaseRewardVO(10980,2)),new MallGoldVO(5000,new BaseRewardVO(10301,2)),new MallGoldVO(20000,new BaseRewardVO(10102,2)),new MallGoldVO(20000,new BaseRewardVO(10304,5)),new MallGoldVO(50000,new BaseRewardVO(10306,5)),new MallGoldVO(10000,new BaseRewardVO(10250,3)),new MallGoldVO(100000,new BaseRewardVO(10250,2))],[new MallMoneyVO(3677,15,new BaseRewardVO(11401,2)),new MallMoneyVO(3677,15,new BaseRewardVO(11401,3)),new MallMoneyVO(3677,15,new BaseRewardVO(11401,1)),new MallMoneyVO(3954,35,new BaseRewardVO(10853,2)),new MallMoneyVO(3954,35,new BaseRewardVO(10853,1)),new MallMoneyVO(3955,100,new BaseRewardVO(10551,1)),new MallMoneyVO(3955,100,new BaseRewardVO(10563,1)),new MallMoneyVO(3955,100,new BaseRewardVO(10572,1)),new MallMoneyVO(3955,100,new BaseRewardVO(10558,1)),new MallMoneyVO(3779,19,new BaseRewardVO(10508,1)),new MallMoneyVO(3779
         ,19,new BaseRewardVO(10509,1))]);
         this.list[this.list.length] = new HeiShiVO(274,[new MallGoldVO(40000,new BaseRewardVO(10274,1)),new MallGoldVO(50000,new BaseRewardVO(10275,1)),new MallGoldVO(60000,new BaseRewardVO(10276,1)),new MallGoldVO(70000,new BaseRewardVO(10277,1)),new MallGoldVO(80000,new BaseRewardVO(10278,1)),new MallGoldVO(90000,new BaseRewardVO(10279,1)),new MallGoldVO(20000,new BaseRewardVO(68001,1)),new MallGoldVO(20000,new BaseRewardVO(68002,1)),new MallGoldVO(20000,new BaseRewardVO(10980,1)),new MallGoldVO(5000,new BaseRewardVO(10301,1)),new MallGoldVO(20000,new BaseRewardVO(10102,1)),new MallGoldVO(20000,new BaseRewardVO(10304,3)),new MallGoldVO(50000,new BaseRewardVO(10306,3)),new MallGoldVO(10000,new BaseRewardVO(10250,2)),new MallGoldVO(100000,new BaseRewardVO(10250,1)),new MallGoldVO(40000,new BaseRewardVO(10274,2)),new MallGoldVO(50000,new BaseRewardVO(10275,2)),new MallGoldVO(60000,new BaseRewardVO(10276,2)),new MallGoldVO(70000,new BaseRewardVO(10277,2)),new MallGoldVO(80000,new BaseRewardVO(10278
         ,2)),new MallGoldVO(90000,new BaseRewardVO(10279,2)),new MallGoldVO(20000,new BaseRewardVO(68001,2)),new MallGoldVO(20000,new BaseRewardVO(68002,2)),new MallGoldVO(20000,new BaseRewardVO(10980,2)),new MallGoldVO(5000,new BaseRewardVO(10301,2)),new MallGoldVO(20000,new BaseRewardVO(10102,2)),new MallGoldVO(20000,new BaseRewardVO(10304,5)),new MallGoldVO(50000,new BaseRewardVO(10306,5)),new MallGoldVO(10000,new BaseRewardVO(10250,3)),new MallGoldVO(100000,new BaseRewardVO(10250,2))],[new MallMoneyVO(3955,100,new BaseRewardVO(10551,2)),new MallMoneyVO(3955,100,new BaseRewardVO(10563,2)),new MallMoneyVO(3955,100,new BaseRewardVO(10558,2)),new MallMoneyVO(3955,100,new BaseRewardVO(10551,1)),new MallMoneyVO(3955,100,new BaseRewardVO(10563,1)),new MallMoneyVO(3955,100,new BaseRewardVO(10572,1)),new MallMoneyVO(3955,100,new BaseRewardVO(10558,1)),new MallMoneyVO(3677,15,new BaseRewardVO(11401,1)),new MallMoneyVO(3684,64,new BaseRewardVO(18840,1)),new MallMoneyVO(3684,64,new BaseRewardVO(18830,1))
         ,new MallMoneyVO(3673,84,new BaseRewardVO(10981,1)),new MallMoneyVO(3779,19,new BaseRewardVO(10508,1)),new MallMoneyVO(3779,19,new BaseRewardVO(10509,1)),new MallMoneyVO(3846,159,new BaseRewardVO(10985,1)),new MallMoneyVO(3870,19,new BaseRewardVO(10984,1)),new MallMoneyVO(3674,64,new BaseRewardVO(10982,1)),new MallMoneyVO(3953,99,new BaseRewardVO(10987,1)),new MallMoneyVO(3954,35,new BaseRewardVO(10853,1)),new MallMoneyVO(3677,15,new BaseRewardVO(11401,2)),new MallMoneyVO(3677,15,new BaseRewardVO(11401,3)),new MallMoneyVO(3684,64,new BaseRewardVO(18840,2)),new MallMoneyVO(3684,64,new BaseRewardVO(18830,2)),new MallMoneyVO(3673,84,new BaseRewardVO(10981,2)),new MallMoneyVO(3779,19,new BaseRewardVO(10508,2)),new MallMoneyVO(3779,19,new BaseRewardVO(10509,2)),new MallMoneyVO(3846,159,new BaseRewardVO(10985,2)),new MallMoneyVO(3870,19,new BaseRewardVO(10984,2)),new MallMoneyVO(3674,64,new BaseRewardVO(10982,2)),new MallMoneyVO(3953,99,new BaseRewardVO(10987,2)),new MallMoneyVO(3954,35,new BaseRewardVO(10853
         ,2))]);
         this.list[this.list.length] = new HeiShiVO(275,[new MallGoldVO(40000,new BaseRewardVO(10274,1)),new MallGoldVO(50000,new BaseRewardVO(10275,1)),new MallGoldVO(60000,new BaseRewardVO(10276,1)),new MallGoldVO(70000,new BaseRewardVO(10277,1)),new MallGoldVO(80000,new BaseRewardVO(10278,1)),new MallGoldVO(90000,new BaseRewardVO(10279,1)),new MallGoldVO(20000,new BaseRewardVO(68001,1)),new MallGoldVO(20000,new BaseRewardVO(68002,1)),new MallGoldVO(20000,new BaseRewardVO(10980,1)),new MallGoldVO(5000,new BaseRewardVO(10301,1)),new MallGoldVO(20000,new BaseRewardVO(10102,1)),new MallGoldVO(20000,new BaseRewardVO(10304,3)),new MallGoldVO(50000,new BaseRewardVO(10306,3)),new MallGoldVO(10000,new BaseRewardVO(10250,2)),new MallGoldVO(100000,new BaseRewardVO(10250,1)),new MallGoldVO(40000,new BaseRewardVO(10274,2)),new MallGoldVO(50000,new BaseRewardVO(10275,2)),new MallGoldVO(60000,new BaseRewardVO(10276,2)),new MallGoldVO(70000,new BaseRewardVO(10277,2)),new MallGoldVO(80000,new BaseRewardVO(10278
         ,2)),new MallGoldVO(90000,new BaseRewardVO(10279,2)),new MallGoldVO(20000,new BaseRewardVO(68001,2)),new MallGoldVO(20000,new BaseRewardVO(68002,2)),new MallGoldVO(20000,new BaseRewardVO(10980,2)),new MallGoldVO(5000,new BaseRewardVO(10301,2)),new MallGoldVO(20000,new BaseRewardVO(10102,2)),new MallGoldVO(20000,new BaseRewardVO(10304,5)),new MallGoldVO(50000,new BaseRewardVO(10306,5)),new MallGoldVO(10000,new BaseRewardVO(10250,3)),new MallGoldVO(100000,new BaseRewardVO(10250,2))],[new MallMoneyVO(3955,100,new BaseRewardVO(10551,2)),new MallMoneyVO(3955,100,new BaseRewardVO(10563,2)),new MallMoneyVO(3955,100,new BaseRewardVO(10558,2)),new MallMoneyVO(3955,100,new BaseRewardVO(10551,1)),new MallMoneyVO(3955,100,new BaseRewardVO(10563,1)),new MallMoneyVO(3955,100,new BaseRewardVO(10572,1)),new MallMoneyVO(3955,100,new BaseRewardVO(10558,1)),new MallMoneyVO(3677,15,new BaseRewardVO(11401,1)),new MallMoneyVO(3684,64,new BaseRewardVO(18840,1)),new MallMoneyVO(3684,64,new BaseRewardVO(18830,1))
         ,new MallMoneyVO(3673,84,new BaseRewardVO(10981,1)),new MallMoneyVO(3779,19,new BaseRewardVO(10508,1)),new MallMoneyVO(3779,19,new BaseRewardVO(10509,1)),new MallMoneyVO(3846,159,new BaseRewardVO(10985,1)),new MallMoneyVO(3870,19,new BaseRewardVO(10984,1)),new MallMoneyVO(3674,64,new BaseRewardVO(10982,1)),new MallMoneyVO(3953,99,new BaseRewardVO(10987,1)),new MallMoneyVO(3954,35,new BaseRewardVO(10853,1)),new MallMoneyVO(3677,15,new BaseRewardVO(11401,2)),new MallMoneyVO(3677,15,new BaseRewardVO(11401,3)),new MallMoneyVO(3684,64,new BaseRewardVO(18840,2)),new MallMoneyVO(3684,64,new BaseRewardVO(18830,2)),new MallMoneyVO(3673,84,new BaseRewardVO(10981,2)),new MallMoneyVO(3779,19,new BaseRewardVO(10508,2)),new MallMoneyVO(3779,19,new BaseRewardVO(10509,2)),new MallMoneyVO(3846,159,new BaseRewardVO(10985,2)),new MallMoneyVO(3870,19,new BaseRewardVO(10984,2)),new MallMoneyVO(3674,64,new BaseRewardVO(10982,2)),new MallMoneyVO(3953,99,new BaseRewardVO(10987,2)),new MallMoneyVO(3954,35,new BaseRewardVO(10853
         ,2))]);
         this.list[this.list.length] = new HeiShiVO(276,[new MallGoldVO(40000,new BaseRewardVO(10274,2)),new MallGoldVO(50000,new BaseRewardVO(10275,2)),new MallGoldVO(60000,new BaseRewardVO(10276,2)),new MallGoldVO(70000,new BaseRewardVO(10277,2)),new MallGoldVO(80000,new BaseRewardVO(10278,2)),new MallGoldVO(90000,new BaseRewardVO(10279,2)),new MallGoldVO(20000,new BaseRewardVO(68001,2)),new MallGoldVO(20000,new BaseRewardVO(68002,2)),new MallGoldVO(20000,new BaseRewardVO(10980,2)),new MallGoldVO(5000,new BaseRewardVO(10301,2)),new MallGoldVO(20000,new BaseRewardVO(10102,2)),new MallGoldVO(20000,new BaseRewardVO(10304,5)),new MallGoldVO(50000,new BaseRewardVO(10306,5)),new MallGoldVO(10000,new BaseRewardVO(10250,3)),new MallGoldVO(100000,new BaseRewardVO(10250,2))],[new MallMoneyVO(3955,100,new BaseRewardVO(10551,2)),new MallMoneyVO(3955,100,new BaseRewardVO(10563,2)),new MallMoneyVO(3955,100,new BaseRewardVO(10572,2)),new MallMoneyVO(3955,100,new BaseRewardVO(10558,2)),new MallMoneyVO(3955,100
         ,new BaseRewardVO(10551,1)),new MallMoneyVO(3955,100,new BaseRewardVO(10563,1)),new MallMoneyVO(3955,100,new BaseRewardVO(10572,1)),new MallMoneyVO(3955,100,new BaseRewardVO(10558,1)),new MallMoneyVO(3677,15,new BaseRewardVO(11401,1)),new MallMoneyVO(3684,64,new BaseRewardVO(18840,1)),new MallMoneyVO(3684,64,new BaseRewardVO(18830,1)),new MallMoneyVO(3673,84,new BaseRewardVO(10981,1)),new MallMoneyVO(3779,19,new BaseRewardVO(10508,1)),new MallMoneyVO(3779,19,new BaseRewardVO(10509,1)),new MallMoneyVO(3846,159,new BaseRewardVO(10985,1)),new MallMoneyVO(3870,19,new BaseRewardVO(10984,1)),new MallMoneyVO(3674,64,new BaseRewardVO(10982,1)),new MallMoneyVO(3953,99,new BaseRewardVO(10987,1)),new MallMoneyVO(3954,35,new BaseRewardVO(10853,1)),new MallMoneyVO(3677,15,new BaseRewardVO(11401,2)),new MallMoneyVO(3677,15,new BaseRewardVO(11401,3)),new MallMoneyVO(3684,64,new BaseRewardVO(18840,2)),new MallMoneyVO(3684,64,new BaseRewardVO(18830,2)),new MallMoneyVO(3673,84,new BaseRewardVO(10981,2)),new MallMoneyVO(3779
         ,19,new BaseRewardVO(10508,2)),new MallMoneyVO(3779,19,new BaseRewardVO(10509,2)),new MallMoneyVO(3846,159,new BaseRewardVO(10985,2)),new MallMoneyVO(3870,19,new BaseRewardVO(10984,2)),new MallMoneyVO(3674,64,new BaseRewardVO(10982,2)),new MallMoneyVO(3953,99,new BaseRewardVO(10987,2)),new MallMoneyVO(3954,35,new BaseRewardVO(10853,2))]);
         this.list[this.list.length] = new HeiShiVO(277,[new MallGoldVO(40000,new BaseRewardVO(10274,2)),new MallGoldVO(50000,new BaseRewardVO(10275,2)),new MallGoldVO(60000,new BaseRewardVO(10276,2)),new MallGoldVO(70000,new BaseRewardVO(10277,2)),new MallGoldVO(80000,new BaseRewardVO(10278,2)),new MallGoldVO(90000,new BaseRewardVO(10279,2)),new MallGoldVO(20000,new BaseRewardVO(68001,2)),new MallGoldVO(20000,new BaseRewardVO(68002,2)),new MallGoldVO(20000,new BaseRewardVO(10980,2)),new MallGoldVO(5000,new BaseRewardVO(10301,2)),new MallGoldVO(20000,new BaseRewardVO(10102,2)),new MallGoldVO(20000,new BaseRewardVO(10304,5)),new MallGoldVO(50000,new BaseRewardVO(10306,5)),new MallGoldVO(10000,new BaseRewardVO(10250,3)),new MallGoldVO(100000,new BaseRewardVO(10250,2))],[new MallMoneyVO(3955,100,new BaseRewardVO(10551,2)),new MallMoneyVO(3955,100,new BaseRewardVO(10563,2)),new MallMoneyVO(3955,100,new BaseRewardVO(10572,2)),new MallMoneyVO(3955,100,new BaseRewardVO(10558,2)),new MallMoneyVO(3955,100
         ,new BaseRewardVO(10551,1)),new MallMoneyVO(3955,100,new BaseRewardVO(10563,1)),new MallMoneyVO(3955,100,new BaseRewardVO(10572,1)),new MallMoneyVO(3955,100,new BaseRewardVO(10558,1)),new MallMoneyVO(3684,64,new BaseRewardVO(18840,2)),new MallMoneyVO(3684,64,new BaseRewardVO(18830,2)),new MallMoneyVO(3779,19,new BaseRewardVO(10508,1)),new MallMoneyVO(3779,19,new BaseRewardVO(10509,1)),new MallMoneyVO(3779,19,new BaseRewardVO(10508,2)),new MallMoneyVO(3779,19,new BaseRewardVO(10509,2))]);
      }
   }
}

