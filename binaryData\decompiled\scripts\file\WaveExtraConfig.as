package file
{
   import mogames.gameData.base.vo.BaseDropVO;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.base.vo.EquipRewardVO;
   import mogames.gameData.base.vo.NeedVO;
   import mogames.gameData.mission.base.MissionBattleVO;
   import mogames.gameData.mission.base.MissionStarVO;
   import mogames.gameData.mission.extra.*;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.require.base.ReqData;
   
   public class WaveExtraConfig
   {
      
      private static var _instance:WaveExtraConfig;
      
      private var _dic:Vector.<WaveDataVO>;
      
      private var _battles:Vector.<MissionBattleVO>;
      
      private var _stars:Vector.<MissionStarVO>;
      
      public function WaveExtraConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.initWave();
         this.initBattle();
      }
      
      public static function instance() : WaveExtraConfig
      {
         if(!_instance)
         {
            _instance = new WaveExtraConfig();
         }
         return _instance;
      }
      
      private function initBattle() : void
      {
         this._battles = new Vector.<MissionBattleVO>();
         this._battles[this._battles.length] = new MissionBattleVO(1001,129,58,[new BaseRewardVO(10000,200),new EquipRewardVO(20002,0),new BaseRewardVO(10027,1)],[new NeedVO(10000,50)],"scene/yz101.json");
         this._battles[this._battles.length] = new MissionBattleVO(1002,151,73,[new BaseRewardVO(10000,250),new EquipRewardVO(20402,0),new EquipRewardVO(20102,0)],[new NeedVO(10000,70)],"scene/yz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(1003,181,100,[new BaseRewardVO(10000,300),new EquipRewardVO(20202,0),new EquipRewardVO(20302,0)],[new NeedVO(10000,100)],"scene/yz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(1004,221,158,[new BaseRewardVO(10000,350),new BaseRewardVO(10901,1),new EquipRewardVO(23202,0)],[new NeedVO(10000,130)],"scene/yz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(1005,269,171,[new BaseRewardVO(10000,400),new EquipRewardVO(23002,0),new BaseRewardVO(10951,1)],[new NeedVO(10000,150)],"scene/yz105.json");
         this._battles[this._battles.length] = new MissionBattleVO(1006,301,207,[new BaseRewardVO(10000,450),new EquipRewardVO(23102,0),new BaseRewardVO(10028,1)],[new NeedVO(10000,170)],"scene/yz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(1007,309,259,[new BaseRewardVO(10000,500),new EquipRewardVO(23302,0),new BaseRewardVO(10201,1)],[new NeedVO(10000,190)],"scene/yz107.json");
         this._battles[this._battles.length] = new MissionBattleVO(1008,319,353,[new BaseRewardVO(10000,550),new BaseRewardVO(10902,1),new EquipRewardVO(23402,0)],[new NeedVO(10000,210)],"scene/yz108.json");
         this._battles[this._battles.length] = new MissionBattleVO(1009,385,417,[new BaseRewardVO(10000,600),new EquipRewardVO(21002,0),new BaseRewardVO(10029,1)],[new NeedVO(10000,230)],"scene/yz109.json");
         this._battles[this._battles.length] = new MissionBattleVO(1010,459,487,[new BaseRewardVO(10000,650),new EquipRewardVO(21102,0),new EquipRewardVO(21202,0)],[new NeedVO(10000,250),new NeedVO(10003,1)],"scene/yz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(1011,509,563,[new BaseRewardVO(10000,700),new EquipRewardVO(21302,0),new EquipRewardVO(21402,0)],[new NeedVO(10000,270),new NeedVO(10003,2)],"scene/yz111.json");
         this._battles[this._battles.length] = new MissionBattleVO(1012,560,644,[new BaseRewardVO(10000,750),new EquipRewardVO(25002,0),new EquipRewardVO(25102,0)],[new NeedVO(10000,290),new NeedVO(10003,3)],"scene/yz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(1013,612,731,[new BaseRewardVO(10000,800),new EquipRewardVO(22002,0),new BaseRewardVO(10952,1)],[new NeedVO(10000,310),new NeedVO(10003,4)],"scene/yz113.json");
         this._battles[this._battles.length] = new MissionBattleVO(1014,674,823,[new BaseRewardVO(10000,850),new EquipRewardVO(22102,0),new EquipRewardVO(22202,0)],[new NeedVO(10000,330),new NeedVO(10003,5)],"scene/yz114.json");
         this._battles[this._battles.length] = new MissionBattleVO(1015,726,921,[new BaseRewardVO(10000,900),new EquipRewardVO(22302,0),new EquipRewardVO(22402,0)],[new NeedVO(10000,350),new NeedVO(10003,6)],"scene/yz115.json");
         this._battles[this._battles.length] = new MissionBattleVO(1016,769,1025,[new BaseRewardVO(10000,950),new EquipRewardVO(25302,0),new EquipRewardVO(25402,0)],[new NeedVO(10000,370),new NeedVO(10003,7)],"scene/yz116.json");
         this._battles[this._battles.length] = new MissionBattleVO(1017,812,1134,[new BaseRewardVO(10000,1000),new EquipRewardVO(24002,0),new EquipRewardVO(25202,0)],[new NeedVO(10000,390),new NeedVO(10003,8)],"scene/yz117.json");
         this._battles[this._battles.length] = new MissionBattleVO(1018,856,1249,[new BaseRewardVO(10000,1050),new EquipRewardVO(24102,0),new EquipRewardVO(24202,0)],[new NeedVO(10000,410),new NeedVO(10003,9)],"scene/yz118.json");
         this._battles[this._battles.length] = new MissionBattleVO(1019,878,1313,[new BaseRewardVO(10000,1100),new EquipRewardVO(24302,0),new EquipRewardVO(24402,0)],[new NeedVO(10000,430),new NeedVO(10003,10)],"scene/yz1190.json");
         this._battles[this._battles.length] = new MissionBattleVO(1020,931,1363,[new BaseRewardVO(10000,1150),new BaseRewardVO(10602,1)],[new NeedVO(10000,450),new NeedVO(10003,11)],"scene/yanz101.json");
         this._battles[this._battles.length] = new MissionBattleVO(1021,983,1462,[new BaseRewardVO(10000,1200),new BaseRewardVO(12001,1),new BaseRewardVO(12002,1)],[new NeedVO(10000,470),new NeedVO(10004,11)],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(1022,1036,1550,[new BaseRewardVO(10000,1250),new BaseRewardVO(12003,1),new BaseRewardVO(12004,1)],[new NeedVO(10000,490),new NeedVO(10003,12)],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(1023,1089,1651,[new BaseRewardVO(10000,1300),new BaseRewardVO(12005,1),new BaseRewardVO(12016,1)],[new NeedVO(10000,510),new NeedVO(10004,12)],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(1024,1142,1754,[new BaseRewardVO(10000,1350),new BaseRewardVO(10611,1)],[new NeedVO(10000,530),new NeedVO(10003,13)],"scene/yanz105.json");
         this._battles[this._battles.length] = new MissionBattleVO(1025,1195,1858,[new BaseRewardVO(10000,1400),new BaseRewardVO(12017,1),new BaseRewardVO(12018,1)],[new NeedVO(10000,550),new NeedVO(10004,13)],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(1026,1248,1962,[new BaseRewardVO(10000,1450),new BaseRewardVO(12019,1),new BaseRewardVO(12020,1)],[new NeedVO(10000,570),new NeedVO(10005,5)],"scene/yanz107.json");
         this._battles[this._battles.length] = new MissionBattleVO(1027,1302,2068,[new BaseRewardVO(10000,1500),new BaseRewardVO(12006,1),new BaseRewardVO(10953,1)],[new NeedVO(10000,590),new NeedVO(10003,14)],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(1028,1355,2175,[new BaseRewardVO(10000,1550),new BaseRewardVO(12007,1),new BaseRewardVO(10601,1)],[new NeedVO(10000,610),new NeedVO(10004,14)],"scene/yanz109.json");
         this._battles[this._battles.length] = new MissionBattleVO(1029,1408,2284,[new BaseRewardVO(10000,1600),new BaseRewardVO(12009,1),new BaseRewardVO(12010,1)],[new NeedVO(10000,630),new NeedVO(10005,6)],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(1030,1462,2393,[new BaseRewardVO(10000,1650),new BaseRewardVO(10903,1),new BaseRewardVO(12008,1)],[new NeedVO(10000,650),new NeedVO(10003,15)],"scene/yanz111.json");
         this._battles[this._battles.length] = new MissionBattleVO(1031,1516,2502,[new BaseRewardVO(10000,1700),new BaseRewardVO(10610,1),new BaseRewardVO(12011,1)],[new NeedVO(10000,670),new NeedVO(10004,15)],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(1032,1569,2613,[new BaseRewardVO(10000,1750),new BaseRewardVO(12012,1),new BaseRewardVO(12013,1)],[new NeedVO(10000,690),new NeedVO(10005,7)],"scene/yanz113.json");
         this._battles[this._battles.length] = new MissionBattleVO(1033,1623,2725,[new BaseRewardVO(10000,1800),new BaseRewardVO(12014,1),new BaseRewardVO(12015,1)],[new NeedVO(10000,710),new NeedVO(10003,16)],"scene/yanz114.json");
         this._battles[this._battles.length] = new MissionBattleVO(1034,1677,2837,[new BaseRewardVO(10000,1850),new BaseRewardVO(10612,1)],[new NeedVO(10000,730),new NeedVO(10004,16)],"scene/yanz115-430.json");
         this._battles[this._battles.length] = new MissionBattleVO(1035,1731,2950,[new BaseRewardVO(10000,1900),new BaseRewardVO(12021,1),new BaseRewardVO(12022,1)],[new NeedVO(10000,750),new NeedVO(10005,8)],"scene/yanz116.json");
         this._battles[this._battles.length] = new MissionBattleVO(1036,1784,3064,[new BaseRewardVO(10000,1950),new BaseRewardVO(12023,1),new BaseRewardVO(12024,1)],[new NeedVO(10000,770),new NeedVO(10004,17)],"scene/yanz117.json");
         this._battles[this._battles.length] = new MissionBattleVO(1037,1838,3178,[new BaseRewardVO(10000,2000),new BaseRewardVO(12025,1)],[new NeedVO(10000,790),new NeedVO(10005,9)],"scene/yanz118.json");
         this._battles[this._battles.length] = new MissionBattleVO(1038,1936,3223,[new BaseRewardVO(10000,2050),new BaseRewardVO(12034,1)],[new NeedVO(10000,800),new NeedVO(10004,18)],"scene/qinz101-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1039,2037,3303,[new BaseRewardVO(10000,2060),new BaseRewardVO(12035,1),new BaseRewardVO(12032,1)],[new NeedVO(10000,810),new NeedVO(10005,9)],"scene/qinz102-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1040,2140,3383,[new BaseRewardVO(10000,2070),new BaseRewardVO(12031,1),new BaseRewardVO(10616,1)],[new NeedVO(10000,820),new NeedVO(10003,17)],"scene/qinz103-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1041,2245,3463,[new BaseRewardVO(10000,2080),new BaseRewardVO(12033,1),new BaseRewardVO(10954,1)],[new NeedVO(10000,830),new NeedVO(10004,19)],"scene/qinz104-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1042,2353,3543,[new BaseRewardVO(10000,2090),new BaseRewardVO(12040,1)],[new NeedVO(10000,840),new NeedVO(10005,10)],"scene/qinz105-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1043,2464,3622,[new BaseRewardVO(10000,2100),new BaseRewardVO(12036,1),new BaseRewardVO(12039,1)],[new NeedVO(10000,850),new NeedVO(10003,18)],"scene/qinz106-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1044,2577,3702,[new BaseRewardVO(10000,2110),new BaseRewardVO(12037,1),new BaseRewardVO(10617,1)],[new NeedVO(10000,860),new NeedVO(10004,20)],"scene/qinz107-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1045,2693,3782,[new BaseRewardVO(10000,2120),new BaseRewardVO(12038,1),new BaseRewardVO(10904,1)],[new NeedVO(10000,870),new NeedVO(10005,11)],"scene/qinz108-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1046,2811,3862,[new BaseRewardVO(10000,2130),new BaseRewardVO(12042,1)],[new NeedVO(10000,880),new NeedVO(10004,21)],"scene/qinz109-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1047,2932,3942,[new BaseRewardVO(10000,2140),new BaseRewardVO(12041,1),new BaseRewardVO(12044,1)],[new NeedVO(10000,890),new NeedVO(10005,12)],"scene/qinz110-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1048,3055,4022,[new BaseRewardVO(10000,2150),new BaseRewardVO(12045,1),new BaseRewardVO(12043,1)],[new NeedVO(10000,900),new NeedVO(10004,22)],"scene/qinz111-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1049,3181,4102,[new BaseRewardVO(10000,2160),new BaseRewardVO(10618,1),new EquipRewardVO(25004,0)],[new NeedVO(10000,910),new NeedVO(10005,6)],"scene/qinz112-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1050,3310,4182,[new BaseRewardVO(10000,2170),new BaseRewardVO(12046,1),new EquipRewardVO(25104,0)],[new NeedVO(10000,920),new NeedVO(10003,19)],"scene/qinz113-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1051,3441,4261,[new BaseRewardVO(10000,2180),new BaseRewardVO(12047,1),new BaseRewardVO(12049,1)],[new NeedVO(10000,930),new NeedVO(10004,23)],"scene/qinz114-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1052,3574,4341,[new BaseRewardVO(10000,2190),new BaseRewardVO(12048,1),new BaseRewardVO(12050,1)],[new NeedVO(10000,940),new NeedVO(10005,13)],"scene/qinz115-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1053,3710,4421,[new BaseRewardVO(10000,2200),new BaseRewardVO(10619,1),new EquipRewardVO(25204,0)],[new NeedVO(10000,950),new NeedVO(10003,20)],"scene/qinz116-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1054,3849,4501,[new BaseRewardVO(10000,2210),new BaseRewardVO(12055,1),new BaseRewardVO(12051,1)],[new NeedVO(10000,960),new NeedVO(10004,24)],"scene/qinz117-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1055,3990,4581,[new BaseRewardVO(10000,2220),new BaseRewardVO(10620,1),new EquipRewardVO(25404,0)],[new NeedVO(10000,970),new NeedVO(10005,14)],"scene/qinz118-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1056,4134,4661,[new BaseRewardVO(10000,2230),new BaseRewardVO(12054,1),new BaseRewardVO(12052,1)],[new NeedVO(10000,980),new NeedVO(10004,25)],"scene/qinz119-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1057,4280,4741,[new BaseRewardVO(10000,2240),new BaseRewardVO(12053,1),new EquipRewardVO(25304,0)],[new NeedVO(10000,990),new NeedVO(10005,15)],"scene/qinz120-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1058,4426,4821,[new BaseRewardVO(10000,2050),new BaseRewardVO(10621,1)],[new NeedVO(10000,1000),new NeedVO(10004,26)],"scene/yanz101.json");
         this._battles[this._battles.length] = new MissionBattleVO(1059,4572,4901,[new BaseRewardVO(10000,2060),new BaseRewardVO(12065,1)],[new NeedVO(10000,1100),new NeedVO(10005,16)],"scene/qinz102-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1060,4718,4981,[new BaseRewardVO(10000,2070),new BaseRewardVO(12064,1)],[new NeedVO(10000,1200),new NeedVO(10003,21)],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(1061,4864,5061,[new BaseRewardVO(10000,2080),new BaseRewardVO(12062,1)],[new NeedVO(10000,1300),new NeedVO(10004,27)],"scene/qinz104-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1062,5010,5141,[new BaseRewardVO(10000,2090),new BaseRewardVO(10905,1)],[new NeedVO(10000,1400),new NeedVO(10005,17)],"scene/yanz105.json");
         this._battles[this._battles.length] = new MissionBattleVO(1063,5156,5221,[new BaseRewardVO(10000,2100),new BaseRewardVO(12061,1)],[new NeedVO(10000,1500),new NeedVO(10003,22)],"scene/qinz106-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1064,5302,5301,[new BaseRewardVO(10000,2110),new BaseRewardVO(12063,1)],[new NeedVO(10000,1600),new NeedVO(10004,28)],"scene/yanz107.json");
         this._battles[this._battles.length] = new MissionBattleVO(1065,5448,5381,[new BaseRewardVO(10000,2120),new BaseRewardVO(10622,1)],[new NeedVO(10000,1700),new NeedVO(10005,18)],"scene/qinz108-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1066,5594,5461,[new BaseRewardVO(10000,2130),new BaseRewardVO(12080,1)],[new NeedVO(10000,1800),new NeedVO(10004,29)],"scene/yanz109.json");
         this._battles[this._battles.length] = new MissionBattleVO(1067,5740,5541,[new BaseRewardVO(10000,2140),new BaseRewardVO(10955,1)],[new NeedVO(10000,1900),new NeedVO(10005,19)],"scene/qinz110-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1068,5886,5621,[new BaseRewardVO(10000,2150),new BaseRewardVO(12079,1)],[new NeedVO(10000,2000),new NeedVO(10004,30)],"scene/yanz111.json");
         this._battles[this._battles.length] = new MissionBattleVO(1069,6032,5701,[new BaseRewardVO(10000,2160),new BaseRewardVO(12077,1)],[new NeedVO(10000,2100),new NeedVO(10005,20)],"scene/qinz112-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1070,6178,5781,[new BaseRewardVO(10000,2170),new BaseRewardVO(12076,1)],[new NeedVO(10000,2200),new NeedVO(10003,23)],"scene/yanz113.json");
         this._battles[this._battles.length] = new MissionBattleVO(1071,6324,5861,[new BaseRewardVO(10000,2180),new BaseRewardVO(12078,1)],[new NeedVO(10000,2300),new NeedVO(10004,31)],"scene/qinz114-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1072,6470,5941,[new BaseRewardVO(10000,2190),new BaseRewardVO(10623,1)],[new NeedVO(10000,2400),new NeedVO(10005,21)],"scene/qinz115-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1073,6616,6021,[new BaseRewardVO(10000,2200),new BaseRewardVO(12070,1)],[new NeedVO(10000,2500),new NeedVO(10003,24)],"scene/qinz116-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1074,6762,6101,[new BaseRewardVO(10000,2210),new BaseRewardVO(12069,1)],[new NeedVO(10000,2600),new NeedVO(10004,32)],"scene/yanz117.json");
         this._battles[this._battles.length] = new MissionBattleVO(1075,6908,6181,[new BaseRewardVO(10000,2220),new BaseRewardVO(12067,1)],[new NeedVO(10000,2700),new NeedVO(10005,22)],"scene/qinz118-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1076,7054,6261,[new BaseRewardVO(10000,2230),new BaseRewardVO(12066,1)],[new NeedVO(10000,2800),new NeedVO(10004,33)],"scene/yanz118.json");
         this._battles[this._battles.length] = new MissionBattleVO(1077,7200,6341,[new BaseRewardVO(10000,2240),new BaseRewardVO(12068,1)],[new NeedVO(10000,2900),new NeedVO(10005,23)],"scene/qinz120-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1078,7346,6421,[new BaseRewardVO(10000,2250),new BaseRewardVO(10401,1)],[new NeedVO(10000,3000),new NeedVO(10003,25)],"scene/yangz101-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1079,7492,6504,[new BaseRewardVO(10000,2260),new BaseRewardVO(10407,1)],[new NeedVO(10000,3100),new NeedVO(10004,25)],"scene/yangz102-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1080,7638,6574,[new BaseRewardVO(10000,2270),new BaseRewardVO(12072,1)],[new NeedVO(10000,3200),new NeedVO(10005,25)],"scene/yangz103-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1081,7784,6670,[new BaseRewardVO(10000,2280),new BaseRewardVO(12075,1)],[new NeedVO(10000,3300),new NeedVO(10003,26)],"scene/yangz104-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1082,7930,6753,[new BaseRewardVO(10000,2290),new BaseRewardVO(10624,1)],[new NeedVO(10000,3400),new NeedVO(10004,26)],"scene/yangz105-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1083,8076,6836,[new BaseRewardVO(10000,2300),new BaseRewardVO(12074,1)],[new NeedVO(10000,3500),new NeedVO(10005,26)],"scene/yangz106-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1084,8222,6919,[new BaseRewardVO(10000,2310),new BaseRewardVO(12071,1)],[new NeedVO(10000,3600),new NeedVO(10003,27)],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1085,8368,7002,[new BaseRewardVO(10000,2320),new BaseRewardVO(12073,1)],[new NeedVO(10000,3700),new NeedVO(10004,27)],"scene/yangz108-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1086,8514,7085,[new BaseRewardVO(10000,2330),new EquipRewardVO(25405,1)],[new NeedVO(10000,3800),new NeedVO(10005,27)],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1087,8660,7168,[new BaseRewardVO(10000,2340),new BaseRewardVO(10906,1)],[new NeedVO(10000,3900),new NeedVO(10003,28)],"scene/yangz110-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1088,8806,7251,[new BaseRewardVO(10000,2350),new EquipRewardVO(25305,1)],[new NeedVO(10000,4000),new NeedVO(10004,28)],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1089,8952,7334,[new BaseRewardVO(10000,2360),new EquipRewardVO(25005,1)],[new NeedVO(10000,4100),new NeedVO(10005,28)],"scene/yangz112-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1090,9098,7417,[new BaseRewardVO(10000,2370),new EquipRewardVO(25105,1)],[new NeedVO(10000,4200),new NeedVO(10003,29)],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1091,9244,7500,[new BaseRewardVO(10000,2380),new EquipRewardVO(25205,1)],[new NeedVO(10000,4300),new NeedVO(10004,29)],"scene/yangz114-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1092,9390,7583,[new BaseRewardVO(10000,2390),new BaseRewardVO(10625,1)],[new NeedVO(10000,4400),new NeedVO(10005,29)],"scene/yangz115-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1093,9536,7666,[new BaseRewardVO(10000,2400),new BaseRewardVO(12081,1)],[new NeedVO(10000,4500),new NeedVO(10003,30)],"scene/yangz116-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1094,9682,7749,[new BaseRewardVO(10000,2410),new BaseRewardVO(12082,1)],[new NeedVO(10000,4600),new NeedVO(10004,30)],"scene/yangz117-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1095,9828,7832,[new BaseRewardVO(10000,2420),new BaseRewardVO(12085,1)],[new NeedVO(10000,4700),new NeedVO(10005,30)],"scene/yangz118-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1096,9974,7915,[new BaseRewardVO(10000,2430),new BaseRewardVO(12084,1)],[new NeedVO(10000,4800),new NeedVO(10003,31)],"scene/yangz119-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1097,10120,7998,[new BaseRewardVO(10000,2440),new BaseRewardVO(12083,1)],[new NeedVO(10000,4900),new NeedVO(10004,31)],"scene/yangz120-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1098,10266,8081,[new BaseRewardVO(10000,2450),new BaseRewardVO(10626,1)],[new NeedVO(10000,3000),new NeedVO(10003,25)],"scene/qinz101-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1099,10412,8164,[new BaseRewardVO(10000,2460),new BaseRewardVO(12090,1)],[new NeedVO(10000,3100),new NeedVO(10004,25)],"scene/yangz102-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1100,10558,8247,[new BaseRewardVO(10000,2470),new BaseRewardVO(12089,1)],[new NeedVO(10000,3200),new NeedVO(10005,25)],"scene/qinz103-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1101,10704,8330,[new BaseRewardVO(10000,2480),new BaseRewardVO(12087,1)],[new NeedVO(10000,3300),new NeedVO(10003,26)],"scene/yangz104-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1102,10850,8413,[new BaseRewardVO(10000,2490),new BaseRewardVO(10907,1)],[new NeedVO(10000,3400),new NeedVO(10004,26)],"scene/qinz105-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1103,10966,8496,[new BaseRewardVO(10000,2500),new BaseRewardVO(12086,1)],[new NeedVO(10000,3500),new NeedVO(10005,26)],"scene/yangz106-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1104,11142,8579,[new BaseRewardVO(10000,2510),new BaseRewardVO(12088,1)],[new NeedVO(10000,3600),new NeedVO(10003,27)],"scene/qinz107-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1105,11288,8662,[new BaseRewardVO(10000,2520),new BaseRewardVO(10627,1)],[new NeedVO(10000,3700),new NeedVO(10004,27)],"scene/yangz108-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1106,11434,8745,[new BaseRewardVO(10000,2530),new BaseRewardVO(12105,1)],[new NeedVO(10000,3800),new NeedVO(10005,27)],"scene/qinz109-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1107,11580,8828,[new BaseRewardVO(10000,2540),new BaseRewardVO(10956,1)],[new NeedVO(10000,3900),new NeedVO(10003,28)],"scene/yangz110-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1108,11726,8911,[new BaseRewardVO(10000,2550),new BaseRewardVO(12104,1)],[new NeedVO(10000,4000),new NeedVO(10004,28)],"scene/qinz111-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1109,11872,8994,[new BaseRewardVO(10000,2560),new BaseRewardVO(12102,1)],[new NeedVO(10000,4100),new NeedVO(10005,28)],"scene/yangz112-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1110,12018,9077,[new BaseRewardVO(10000,2570),new BaseRewardVO(12101,1)],[new NeedVO(10000,4200),new NeedVO(10003,29)],"scene/qinz113-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1111,12164,9160,[new BaseRewardVO(10000,2580),new BaseRewardVO(12103,1)],[new NeedVO(10000,4300),new NeedVO(10004,29)],"scene/yangz114-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1112,12310,9243,[new BaseRewardVO(10000,2590),new BaseRewardVO(10628,1)],[new NeedVO(10000,4400),new NeedVO(10005,29)],"scene/qinz115-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1113,12456,9326,[new BaseRewardVO(10000,2600),new BaseRewardVO(12095,1)],[new NeedVO(10000,4500),new NeedVO(10003,30)],"scene/yangz116-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1114,12602,9409,[new BaseRewardVO(10000,2610),new BaseRewardVO(12094,1)],[new NeedVO(10000,4600),new NeedVO(10004,30)],"scene/qinz117-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1115,12748,9492,[new BaseRewardVO(10000,2620),new BaseRewardVO(12092,1)],[new NeedVO(10000,4700),new NeedVO(10005,30)],"scene/yangz118-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1116,12894,9575,[new BaseRewardVO(10000,2630),new BaseRewardVO(12091,1)],[new NeedVO(10000,4800),new NeedVO(10003,31)],"scene/qinz119-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1117,13040,9658,[new BaseRewardVO(10000,2640),new BaseRewardVO(12093,1)],[new NeedVO(10000,4900),new NeedVO(10004,31)],"scene/yangz120-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1118,13186,9741,[new BaseRewardVO(10000,2650),new BaseRewardVO(10401,1)],[new NeedVO(10000,5000),new NeedVO(10003,32)],"scene/yangz101-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1119,13332,9824,[new BaseRewardVO(10000,2660),new BaseRewardVO(10408,1)],[new NeedVO(10000,5100),new NeedVO(10004,32)],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(1120,13478,9907,[new BaseRewardVO(10000,2670),new BaseRewardVO(12097,1)],[new NeedVO(10000,5200),new NeedVO(10005,32)],"scene/yangz103-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1121,13624,9990,[new BaseRewardVO(10000,2680),new BaseRewardVO(12100,1)],[new NeedVO(10000,5300),new NeedVO(10003,33)],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(1122,13770,10073,[new BaseRewardVO(10000,2690),new BaseRewardVO(10629,1)],[new NeedVO(10000,5400),new NeedVO(10004,33)],"scene/yangz105-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1123,13916,10156,[new BaseRewardVO(10000,2700),new BaseRewardVO(12099,1)],[new NeedVO(10000,5500),new NeedVO(10005,33)],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(1124,14062,10239,[new BaseRewardVO(10000,2710),new BaseRewardVO(12096,1)],[new NeedVO(10000,5600),new NeedVO(10003,34)],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1125,14208,10322,[new BaseRewardVO(10000,2720),new BaseRewardVO(12098,1)],[new NeedVO(10000,5700),new NeedVO(10004,34)],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(1126,14354,10405,[new BaseRewardVO(10000,2730),new EquipRewardVO(25406,1)],[new NeedVO(10000,5800),new NeedVO(10005,34)],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1127,14500,10488,[new BaseRewardVO(10000,2740),new BaseRewardVO(10907,1)],[new NeedVO(10000,5900),new NeedVO(10003,35)],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(1128,14646,10571,[new BaseRewardVO(10000,2750),new EquipRewardVO(25306,1)],[new NeedVO(10000,6000),new NeedVO(10004,35)],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1129,14792,10654,[new BaseRewardVO(10000,2760),new EquipRewardVO(25006,1)],[new NeedVO(10000,6100),new NeedVO(10005,35)],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(1130,14938,10737,[new BaseRewardVO(10000,2770),new EquipRewardVO(25106,1)],[new NeedVO(10000,6200),new NeedVO(10003,36)],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1131,15230,10820,[new BaseRewardVO(10000,2780),new EquipRewardVO(25206,1)],[new NeedVO(10000,6300),new NeedVO(10004,36)],"scene/yanz114.json");
         this._battles[this._battles.length] = new MissionBattleVO(1132,15376,10903,[new BaseRewardVO(10000,2790),new BaseRewardVO(10630,1)],[new NeedVO(10000,6400),new NeedVO(10005,36)],"scene/yangz115-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1133,15522,10986,[new BaseRewardVO(10000,2800),new BaseRewardVO(12106,1)],[new NeedVO(10000,6500),new NeedVO(10003,37)],"scene/yanz116.json");
         this._battles[this._battles.length] = new MissionBattleVO(1134,15668,11069,[new BaseRewardVO(10000,2810),new BaseRewardVO(12107,1)],[new NeedVO(10000,6600),new NeedVO(10004,37)],"scene/yangz117-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1135,15814,11152,[new BaseRewardVO(10000,2820),new BaseRewardVO(12110,1)],[new NeedVO(10000,6700),new NeedVO(10005,37)],"scene/yanz118.json");
         this._battles[this._battles.length] = new MissionBattleVO(1136,15960,11235,[new BaseRewardVO(10000,2830),new BaseRewardVO(12109,1)],[new NeedVO(10000,6800),new NeedVO(10003,38)],"scene/yangz119-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1137,16106,11318,[new BaseRewardVO(10000,2840),new BaseRewardVO(12108,1)],[new NeedVO(10000,6900),new NeedVO(10004,38)],"scene/jingz120.json");
         this._battles[this._battles.length] = new MissionBattleVO(1138,16252,11401,[new BaseRewardVO(10000,2850),new BaseDropVO(900,new BaseRewardVO(10631,1))],[new NeedVO(10000,7000),new NeedVO(10003,39)],"scene/yanz101.json");
         this._battles[this._battles.length] = new MissionBattleVO(1139,16398,11484,[new BaseRewardVO(10000,2860),new BaseDropVO(750,new BaseRewardVO(12135,1))],[new NeedVO(10000,7100),new NeedVO(10004,39)],"scene/yangz102-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1140,16544,11567,[new BaseRewardVO(10000,2870),new BaseDropVO(750,new BaseRewardVO(12131,1))],[new NeedVO(10000,7200),new NeedVO(10005,39)],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(1141,16690,11650,[new BaseRewardVO(10000,2880),new BaseDropVO(750,new BaseRewardVO(12132,1))],[new NeedVO(10000,7300),new NeedVO(10003,40)],"scene/yangz104-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1142,16836,11733,[new BaseRewardVO(10000,2890),new BaseDropVO(900,new BaseRewardVO(10632,1))],[new NeedVO(10000,7400),new NeedVO(10004,40)],"scene/yanz105.json");
         this._battles[this._battles.length] = new MissionBattleVO(1143,16982,11816,[new BaseRewardVO(10000,2900),new BaseDropVO(750,new BaseRewardVO(12134,1))],[new NeedVO(10000,7500),new NeedVO(10005,40)],"scene/yangz106-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1144,17128,11899,[new BaseRewardVO(10000,2910),new BaseDropVO(750,new BaseRewardVO(12133,1))],[new NeedVO(10000,7600),new NeedVO(10003,41)],"scene/yanz107.json");
         this._battles[this._battles.length] = new MissionBattleVO(1145,17274,11982,[new BaseRewardVO(10000,2920),new BaseDropVO(750,new BaseRewardVO(12149,1))],[new NeedVO(10000,7700),new NeedVO(10004,41)],"scene/yangz108-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1146,17420,12065,[new BaseRewardVO(10000,2930),new BaseDropVO(750,new BaseRewardVO(12150,1))],[new NeedVO(10000,7800),new NeedVO(10005,41)],"scene/yanz109.json");
         this._battles[this._battles.length] = new MissionBattleVO(1147,17566,12148,[new BaseRewardVO(10000,2940),new BaseDropVO(900,new BaseRewardVO(10634,1))],[new NeedVO(10000,7900),new NeedVO(10003,42)],"scene/yangz110-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1148,17712,12231,[new BaseRewardVO(10000,2950),new BaseDropVO(750,new BaseRewardVO(12146,1))],[new NeedVO(10000,8000),new NeedVO(10004,42)],"scene/yanz111.json");
         this._battles[this._battles.length] = new MissionBattleVO(1149,17858,12314,[new BaseRewardVO(10000,2960),new BaseDropVO(750,new BaseRewardVO(12147,1))],[new NeedVO(10000,8100),new NeedVO(10005,42)],"scene/yangz112-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1150,18004,12397,[new BaseRewardVO(10000,2970),new BaseDropVO(750,new BaseRewardVO(12148,1))],[new NeedVO(10000,8200),new NeedVO(10003,43)],"scene/yanz113.json");
         this._battles[this._battles.length] = new MissionBattleVO(1151,18150,12480,[new BaseRewardVO(10000,2980),new BaseDropVO(750,new BaseRewardVO(12139,1))],[new NeedVO(10000,8300),new NeedVO(10004,43)],"scene/yangz114-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1152,18296,12563,[new BaseRewardVO(10000,2990),new BaseDropVO(900,new BaseRewardVO(10633,1))],[new NeedVO(10000,8400),new NeedVO(10005,43)],"scene/yanz115-430.json");
         this._battles[this._battles.length] = new MissionBattleVO(1153,18442,12646,[new BaseRewardVO(10000,3000),new BaseDropVO(750,new BaseRewardVO(12140,1))],[new NeedVO(10000,8500),new NeedVO(10003,44)],"scene/yangz116-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1154,18588,12729,[new BaseRewardVO(10000,3010),new BaseDropVO(750,new BaseRewardVO(12136,1))],[new NeedVO(10000,8600),new NeedVO(10004,44)],"scene/yanz117.json");
         this._battles[this._battles.length] = new MissionBattleVO(1155,18734,12812,[new BaseRewardVO(10000,3020),new BaseDropVO(750,new BaseRewardVO(12137,1))],[new NeedVO(10000,8700),new NeedVO(10005,44)],"scene/yangz118-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1156,18880,12895,[new BaseRewardVO(10000,3030),new BaseDropVO(750,new BaseRewardVO(12138,1))],[new NeedVO(10000,8800),new NeedVO(10003,45)],"scene/yanz118.json");
         this._battles[this._battles.length] = new MissionBattleVO(1157,19026,12978,[new BaseRewardVO(10000,3040),new BaseDropVO(100,new BaseRewardVO(10980,1))],[new NeedVO(10000,8900),new NeedVO(10004,45)],"scene/qinz106-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1158,19172,13061,[new BaseRewardVO(10000,3050),new BaseDropVO(700,new BaseRewardVO(10635,1))],[new NeedVO(10000,9000),new NeedVO(10003,46)],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(1159,19318,13144,[new BaseRewardVO(10000,3060),new BaseDropVO(250,new BaseRewardVO(12144,1))],[new NeedVO(10000,9100),new NeedVO(10004,46)],"scene/yangz101-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1160,19464,13227,[new BaseRewardVO(10000,3070),new BaseDropVO(250,new BaseRewardVO(12142,1))],[new NeedVO(10000,9200),new NeedVO(10005,46)],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(1161,19610,13310,[new BaseRewardVO(10000,3080),new BaseDropVO(250,new BaseRewardVO(12141,1))],[new NeedVO(10000,9300),new NeedVO(10003,47)],"scene/yangz103-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1162,19756,13393,[new BaseRewardVO(10000,3090),new BaseDropVO(700,new BaseRewardVO(10636,1))],[new NeedVO(10000,9400),new NeedVO(10004,47)],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(1163,19902,13476,[new BaseRewardVO(10000,3100),new BaseDropVO(250,new BaseRewardVO(12145,1))],[new NeedVO(10000,9500),new NeedVO(10005,47)],"scene/yangz105-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1164,20048,13559,[new BaseRewardVO(10000,3110),new BaseDropVO(250,new BaseRewardVO(12143,1))],[new NeedVO(10000,9600),new NeedVO(10003,48)],"scene/qinz105-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1165,20194,13642,[new BaseRewardVO(10000,3120),new BaseDropVO(100,new BaseRewardVO(10980,1))],[new NeedVO(10000,9700),new NeedVO(10004,48)],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1166,20340,13725,[new BaseRewardVO(10000,3130),new BaseDropVO(250,new BaseRewardVO(12154,1))],[new NeedVO(10000,9800),new NeedVO(10005,48)],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(1167,20486,13808,[new BaseRewardVO(10000,3140),new BaseDropVO(450,new BaseRewardVO(10957,1))],[new NeedVO(10000,9900),new NeedVO(10003,49)],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1168,20632,13891,[new BaseRewardVO(10000,3150),new BaseDropVO(250,new BaseRewardVO(12152,1))],[new NeedVO(10000,10000),new NeedVO(10004,49)],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(1169,20778,13974,[new BaseRewardVO(10000,3160),new BaseDropVO(700,new BaseRewardVO(10637,1))],[new NeedVO(10000,10100),new NeedVO(10005,49)],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1170,20924,14057,[new BaseRewardVO(10000,3170),new BaseDropVO(250,new BaseRewardVO(12151,1))],[new NeedVO(10000,10200),new NeedVO(10003,50)],"scene/yanz114.json");
         this._battles[this._battles.length] = new MissionBattleVO(1171,21070,14140,[new BaseRewardVO(10000,3180),new BaseDropVO(250,new BaseRewardVO(12153,1))],[new NeedVO(10000,10300),new NeedVO(10004,50)],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1172,21216,14223,[new BaseRewardVO(10000,3190),new BaseDropVO(250,new BaseRewardVO(12155,1))],[new NeedVO(10000,10400),new NeedVO(10005,50)],"scene/yanz116.json");
         this._battles[this._battles.length] = new MissionBattleVO(1173,21362,14306,[new BaseRewardVO(10000,3200),new BaseDropVO(100,new EquipRewardVO(25007,1))],[new NeedVO(10000,10500),new NeedVO(10003,51)],"scene/yangz115-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1174,21508,14389,[new BaseRewardVO(10000,3210),new BaseDropVO(100,new EquipRewardVO(25107,1))],[new NeedVO(10000,10600),new NeedVO(10004,51)],"scene/yanz118.json");
         this._battles[this._battles.length] = new MissionBattleVO(1175,21654,14472,[new BaseRewardVO(10000,3220),new BaseDropVO(100,new EquipRewardVO(25207,1))],[new NeedVO(10000,10700),new NeedVO(10005,51)],"scene/yangz117-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1176,21800,14555,[new BaseRewardVO(10000,3230),new BaseDropVO(100,new EquipRewardVO(25307,1))],[new NeedVO(10000,10800),new NeedVO(10003,52)],"scene/qinz105-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1177,21946,14638,[new BaseRewardVO(10000,3240),new BaseDropVO(100,new EquipRewardVO(25407,1))],[new NeedVO(10000,10900),new NeedVO(10004,52)],"scene/qinz104-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1178,22902,14721,[new BaseRewardVO(10000,3250),new BaseDropVO(400,new BaseRewardVO(10203,1))],[new NeedVO(10000,11000),new NeedVO(10003,53)],"scene/qinz101-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1179,22238,14804,[new BaseRewardVO(10000,3260),new BaseDropVO(700,new BaseRewardVO(10638,1))],[new NeedVO(10000,11100),new NeedVO(10004,53)],"scene/qinz102-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1180,22384,14887,[new BaseRewardVO(10000,3270),new BaseDropVO(250,new BaseRewardVO(12165,1))],[new NeedVO(10000,11200),new NeedVO(10005,53)],"scene/qinz103-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1181,22530,14970,[new BaseRewardVO(10000,3280),new BaseDropVO(250,new BaseRewardVO(12161,1))],[new NeedVO(10000,11300),new NeedVO(10003,54)],"scene/qinz104-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1182,22676,15053,[new BaseRewardVO(10000,3290),new BaseDropVO(700,new BaseRewardVO(10639,1))],[new NeedVO(10000,11400),new NeedVO(10004,54)],"scene/qinz105-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1183,22822,15136,[new BaseRewardVO(10000,3300),new BaseDropVO(250,new BaseRewardVO(12162,1))],[new NeedVO(10000,11500),new NeedVO(10005,54)],"scene/qinz106-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1184,22986,15219,[new BaseRewardVO(10000,3310),new BaseDropVO(250,new BaseRewardVO(12164,1))],[new NeedVO(10000,11600),new NeedVO(10003,55)],"scene/qinz107-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1185,23114,15302,[new BaseRewardVO(10000,3320),new BaseDropVO(250,new BaseRewardVO(12163,1))],[new NeedVO(10000,11700),new NeedVO(10004,55)],"scene/qinz108-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1186,23260,15385,[new BaseRewardVO(10000,3330),new BaseDropVO(250,new BaseRewardVO(12179,1))],[new NeedVO(10000,11800),new NeedVO(10005,55)],"scene/qinz109-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1187,23406,15468,[new BaseRewardVO(10000,3340),new BaseDropVO(700,new BaseRewardVO(10641,1))],[new NeedVO(10000,11900),new NeedVO(10003,56)],"scene/qinz110-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1188,23552,15551,[new BaseRewardVO(10000,3350),new BaseDropVO(250,new BaseRewardVO(12180,1))],[new NeedVO(10000,12000),new NeedVO(10004,56)],"scene/qinz111-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1189,23698,15634,[new BaseRewardVO(10000,3360),new BaseDropVO(250,new BaseRewardVO(12176,1))],[new NeedVO(10000,12100),new NeedVO(10005,56)],"scene/qinz112-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1190,23844,15717,[new BaseRewardVO(10000,3370),new BaseDropVO(250,new BaseRewardVO(12177,1))],[new NeedVO(10000,12200),new NeedVO(10003,57)],"scene/qinz113-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1191,23990,15800,[new BaseRewardVO(10000,3380),new BaseDropVO(250,new BaseRewardVO(12178,1))],[new NeedVO(10000,12300),new NeedVO(10004,57)],"scene/qinz114-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1192,24136,15883,[new BaseRewardVO(10000,3390),new BaseDropVO(700,new BaseRewardVO(10640,1))],[new NeedVO(10000,12400),new NeedVO(10005,57)],"scene/qinz115-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1193,24282,15966,[new BaseRewardVO(10000,3400),new BaseDropVO(250,new BaseRewardVO(12169,1))],[new NeedVO(10000,12500),new NeedVO(10003,58)],"scene/qinz116-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1194,24428,16049,[new BaseRewardVO(10000,3410),new BaseDropVO(250,new BaseRewardVO(12170,1))],[new NeedVO(10000,12600),new NeedVO(10004,58)],"scene/qinz117-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1195,24574,16132,[new BaseRewardVO(10000,3420),new BaseDropVO(250,new BaseRewardVO(12166,1))],[new NeedVO(10000,12700),new NeedVO(10005,58)],"scene/qinz118-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1196,24720,16215,[new BaseRewardVO(10000,3430),new BaseDropVO(250,new BaseRewardVO(12167,1))],[new NeedVO(10000,12800),new NeedVO(10003,58)],"scene/qinz119-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1197,24866,16298,[new BaseRewardVO(10000,3440),new BaseDropVO(250,new BaseRewardVO(12168,1))],[new NeedVO(10000,12900),new NeedVO(10004,58)],"scene/qinz120-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1198,25012,16381,[new BaseRewardVO(10000,3450),new BaseDropVO(700,new BaseRewardVO(10642,1))],[new NeedVO(10000,13000),new NeedVO(10003,59)],"scene/qinz101-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1199,25158,16464,[new BaseRewardVO(10000,3460),new BaseDropVO(250,new BaseRewardVO(12174,1))],[new NeedVO(10000,13100),new NeedVO(10004,59)],"scene/yangz102-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1200,25304,16547,[new BaseRewardVO(10000,3470),new BaseDropVO(250,new BaseRewardVO(12172,1))],[new NeedVO(10000,13200),new NeedVO(10005,59)],"scene/qinz103-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1201,25450,16630,[new BaseRewardVO(10000,3480),new BaseDropVO(250,new BaseRewardVO(12171,1))],[new NeedVO(10000,13300),new NeedVO(10006,59)],"scene/yangz104-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1202,25596,16713,[new BaseRewardVO(10000,3490),new BaseDropVO(700,new BaseRewardVO(10643,1))],[new NeedVO(10000,13400),new NeedVO(10007,59)],"scene/qinz105-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1203,25742,16796,[new BaseRewardVO(10000,3500),new BaseDropVO(250,new BaseRewardVO(12175,1))],[new NeedVO(10000,13500),new NeedVO(10003,60)],"scene/yangz106-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1204,25888,16879,[new BaseRewardVO(10000,3510),new BaseDropVO(250,new BaseRewardVO(12173,1))],[new NeedVO(10000,13600),new NeedVO(10004,60)],"scene/qinz107-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1205,26034,16962,[new BaseRewardVO(10000,3520),new BaseDropVO(100,new BaseRewardVO(10980,1))],[new NeedVO(10000,13700),new NeedVO(10005,60)],"scene/yangz108-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1206,26180,17045,[new BaseRewardVO(10000,3530),new BaseDropVO(250,new BaseRewardVO(12184,1))],[new NeedVO(10000,13800),new NeedVO(10006,60)],"scene/qinz109-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1207,26326,17128,[new BaseRewardVO(10000,3540),new BaseDropVO(250,new BaseRewardVO(10958,1))],[new NeedVO(10000,13900),new NeedVO(10007,60)],"scene/yangz110-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1208,26472,17211,[new BaseRewardVO(10000,3550),new BaseDropVO(250,new BaseRewardVO(12182,1))],[new NeedVO(10000,14000),new NeedVO(10003,61)],"scene/qinz111-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1209,26618,17294,[new BaseRewardVO(10000,3560),new BaseDropVO(700,new BaseRewardVO(10644,1))],[new NeedVO(10000,14100),new NeedVO(10004,61)],"scene/yangz112-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1210,26764,17377,[new BaseRewardVO(10000,3570),new BaseDropVO(250,new BaseRewardVO(12181,1))],[new NeedVO(10000,14200),new NeedVO(10005,61)],"scene/qinz113-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1211,26910,17460,[new BaseRewardVO(10000,3580),new BaseDropVO(250,new BaseRewardVO(12183,1))],[new NeedVO(10000,14300),new NeedVO(10006,61)],"scene/yangz114-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1212,27056,17543,[new BaseRewardVO(10000,3590),new BaseDropVO(250,new BaseRewardVO(12185,1))],[new NeedVO(10000,14400),new NeedVO(10007,61)],"scene/qinz115-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1213,27202,17626,[new BaseRewardVO(10000,3600),new BaseDropVO(100,new EquipRewardVO(25008,1))],[new NeedVO(10000,14500),new NeedVO(10003,62)],"scene/yangz116-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1214,27348,17709,[new BaseRewardVO(10000,3610),new BaseDropVO(100,new EquipRewardVO(25108,1))],[new NeedVO(10000,14600),new NeedVO(10004,62)],"scene/qinz117-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1215,27494,17792,[new BaseRewardVO(10000,3620),new BaseDropVO(100,new EquipRewardVO(25208,1))],[new NeedVO(10000,14700),new NeedVO(10005,62)],"scene/yangz118-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1216,27640,17875,[new BaseRewardVO(10000,3630),new BaseDropVO(100,new EquipRewardVO(25308,1))],[new NeedVO(10000,14800),new NeedVO(10006,62)],"scene/qinz119-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1217,27786,17958,[new BaseRewardVO(10000,3640),new BaseDropVO(100,new EquipRewardVO(25408,1))],[new NeedVO(10000,14900),new NeedVO(10007,62)],"scene/yangz120-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1218,27932,18041,[new BaseRewardVO(10000,3650),new BaseDropVO(400,new BaseRewardVO(10202,1))],[new NeedVO(10000,15000),new NeedVO(10003,63)],"scene/yangz101-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1219,28078,18124,[new BaseRewardVO(10000,3660),new BaseDropVO(700,new BaseRewardVO(10645,1))],[new NeedVO(10000,15100),new NeedVO(10004,63)],"scene/qinz102-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1220,28224,18207,[new BaseRewardVO(10000,3670),new BaseDropVO(250,new BaseRewardVO(12205,1))],[new NeedVO(10000,15200),new NeedVO(10005,63)],"scene/yangz103-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1221,28370,18290,[new BaseRewardVO(10000,3680),new BaseDropVO(250,new BaseRewardVO(12201,1))],[new NeedVO(10000,15300),new NeedVO(10003,64)],"scene/qinz104-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1222,28516,18373,[new BaseRewardVO(10000,3690),new BaseDropVO(700,new BaseRewardVO(10646,1))],[new NeedVO(10000,15400),new NeedVO(10004,64)],"scene/yangz105-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1223,28662,18456,[new BaseRewardVO(10000,3700),new BaseDropVO(250,new BaseRewardVO(12202,1))],[new NeedVO(10000,15500),new NeedVO(10005,64)],"scene/qinz106-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1224,28808,18539,[new BaseRewardVO(10000,3710),new BaseDropVO(250,new BaseRewardVO(12204,1))],[new NeedVO(10000,15600),new NeedVO(10003,65)],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1225,28951,18622,[new BaseRewardVO(10000,3720),new BaseDropVO(250,new BaseRewardVO(12203,1))],[new NeedVO(10000,15700),new NeedVO(10004,65)],"scene/qinz108-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1226,29100,18705,[new BaseRewardVO(10000,3730),new BaseDropVO(250,new BaseRewardVO(12219,1))],[new NeedVO(10000,15800),new NeedVO(10005,65)],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1227,29246,18788,[new BaseRewardVO(10000,3740),new BaseDropVO(700,new BaseRewardVO(10648,1))],[new NeedVO(10000,15900),new NeedVO(10003,66)],"scene/qinz110-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1228,29392,18871,[new BaseRewardVO(10000,3750),new BaseDropVO(250,new BaseRewardVO(12220,1))],[new NeedVO(10000,16000),new NeedVO(10004,66)],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1229,29538,18954,[new BaseRewardVO(10000,3760),new BaseDropVO(250,new BaseRewardVO(12216,1))],[new NeedVO(10000,16100),new NeedVO(10005,66)],"scene/qinz112-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1230,29684,19037,[new BaseRewardVO(10000,3770),new BaseDropVO(250,new BaseRewardVO(12217,1))],[new NeedVO(10000,16200),new NeedVO(10003,67)],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1231,29830,19120,[new BaseRewardVO(10000,3780),new BaseDropVO(250,new BaseRewardVO(12218,1))],[new NeedVO(10000,16300),new NeedVO(10004,67)],"scene/qinz114-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1232,29976,19203,[new BaseRewardVO(10000,3790),new BaseDropVO(700,new BaseRewardVO(10647,1))],[new NeedVO(10000,16400),new NeedVO(10005,67)],"scene/yangz115-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1233,30122,19286,[new BaseRewardVO(10000,3800),new BaseDropVO(250,new BaseRewardVO(12209,1))],[new NeedVO(10000,16500),new NeedVO(10003,68)],"scene/qinz116-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1234,30268,19369,[new BaseRewardVO(10000,3810),new BaseDropVO(250,new BaseRewardVO(12210,1))],[new NeedVO(10000,16600),new NeedVO(10004,68)],"scene/yangz117-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1235,30414,19452,[new BaseRewardVO(10000,3820),new BaseDropVO(250,new BaseRewardVO(12206,1))],[new NeedVO(10000,16700),new NeedVO(10005,68)],"scene/qinz118-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1236,30560,19535,[new BaseRewardVO(10000,3830),new BaseDropVO(250,new BaseRewardVO(12207,1))],[new NeedVO(10000,16800),new NeedVO(10003,68)],"scene/yangz119-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(1237,30706,19618,[new BaseRewardVO(10000,3840),new BaseDropVO(250,new BaseRewardVO(12208,1))],[new NeedVO(10000,16900),new NeedVO(10004,68)],"scene/qinz120-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(1238,30852,19701,[new BaseRewardVO(10000,3850),new BaseDropVO(700,new BaseRewardVO(10649,1))],[new NeedVO(10000,17000),new NeedVO(10003,69)],"scene/yanz101.json");
         this._battles[this._battles.length] = new MissionBattleVO(1239,30998,19784,[new BaseRewardVO(10000,3860),new BaseDropVO(250,new BaseRewardVO(12214,1))],[new NeedVO(10000,17100),new NeedVO(10004,69)],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(1240,31144,19867,[new BaseRewardVO(10000,3870),new BaseDropVO(250,new BaseRewardVO(12212,1))],[new NeedVO(10000,17200),new NeedVO(10005,69)],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(1241,31290,19950,[new BaseRewardVO(10000,3880),new BaseDropVO(250,new BaseRewardVO(12211,1))],[new NeedVO(10000,17300),new NeedVO(10003,70)],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(1242,31436,20033,[new BaseRewardVO(10000,3890),new BaseDropVO(700,new BaseRewardVO(10650,1))],[new NeedVO(10000,17400),new NeedVO(10004,70)],"scene/yanz105.json");
         this._battles[this._battles.length] = new MissionBattleVO(1243,31582,20116,[new BaseRewardVO(10000,3900),new BaseDropVO(250,new BaseRewardVO(12215,1))],[new NeedVO(10000,17500),new NeedVO(10005,70)],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(1244,31728,20199,[new BaseRewardVO(10000,3910),new BaseDropVO(250,new BaseRewardVO(12213,1))],[new NeedVO(10000,17600),new NeedVO(10003,71)],"scene/yanz107.json");
         this._battles[this._battles.length] = new MissionBattleVO(1245,31874,20282,[new BaseRewardVO(10000,3920),new BaseDropVO(100,new BaseRewardVO(10980,1))],[new NeedVO(10000,17700),new NeedVO(10004,71)],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(1246,32020,20365,[new BaseRewardVO(10000,3930),new BaseDropVO(250,new BaseRewardVO(12224,1))],[new NeedVO(10000,17800),new NeedVO(10005,71)],"scene/yanz109.json");
         this._battles[this._battles.length] = new MissionBattleVO(1247,32166,20448,[new BaseRewardVO(10000,3940),new BaseDropVO(250,new BaseRewardVO(10701,1))],[new NeedVO(10000,17900),new NeedVO(10003,72)],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(1248,32312,20531,[new BaseRewardVO(10000,3950),new BaseDropVO(250,new BaseRewardVO(12222,1))],[new NeedVO(10000,18000),new NeedVO(10004,72)],"scene/yanz111.json");
         this._battles[this._battles.length] = new MissionBattleVO(1249,32458,20614,[new BaseRewardVO(10000,3960),new BaseDropVO(700,new BaseRewardVO(10651,1))],[new NeedVO(10000,18100),new NeedVO(10005,72)],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(1250,32604,20697,[new BaseRewardVO(10000,3970),new BaseDropVO(250,new BaseRewardVO(12221,1))],[new NeedVO(10000,18200),new NeedVO(10003,73)],"scene/yanz113.json");
         this._battles[this._battles.length] = new MissionBattleVO(1251,32750,20780,[new BaseRewardVO(10000,3980),new BaseDropVO(250,new BaseRewardVO(12223,1))],[new NeedVO(10000,18300),new NeedVO(10004,73)],"scene/yanz114.json");
         this._battles[this._battles.length] = new MissionBattleVO(1252,32896,20863,[new BaseRewardVO(10000,3990),new BaseDropVO(250,new BaseRewardVO(12225,1))],[new NeedVO(10000,18400),new NeedVO(10005,73)],"scene/yanz115-430.json");
         this._battles[this._battles.length] = new MissionBattleVO(1253,33042,20946,[new BaseRewardVO(10000,4000),new BaseDropVO(100,new EquipRewardVO(25009,1))],[new NeedVO(10000,18500),new NeedVO(10003,74)],"scene/yanz116.json");
         this._battles[this._battles.length] = new MissionBattleVO(1254,33188,21029,[new BaseRewardVO(10000,4010),new BaseDropVO(100,new EquipRewardVO(25109,1))],[new NeedVO(10000,18600),new NeedVO(10004,74)],"scene/yanz117.json");
         this._battles[this._battles.length] = new MissionBattleVO(1255,33334,21112,[new BaseRewardVO(10000,4020),new BaseDropVO(100,new EquipRewardVO(25409,1))],[new NeedVO(10000,18700),new NeedVO(10005,74)],"scene/yanz118.json");
         this._battles[this._battles.length] = new MissionBattleVO(1256,33480,21195,[new BaseRewardVO(10000,4030),new BaseDropVO(100,new EquipRewardVO(25309,1))],[new NeedVO(10000,18800),new NeedVO(10003,75)],"scene/qinz119-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(1257,33626,21278,[new BaseRewardVO(10000,4040),new BaseDropVO(100,new EquipRewardVO(25209,1))],[new NeedVO(10000,18900),new NeedVO(10004,75)],"scene/yangz120-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2001,33772,21361,[new BaseRewardVO(10000,4050),new BaseDropVO(700,new BaseRewardVO(10652,1))],[new NeedVO(10000,19000),new NeedVO(10003,76)],"scene/DH001-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2002,33918,21444,[new BaseRewardVO(10000,4060),new BaseDropVO(200,new BaseRewardVO(12234,1))],[new NeedVO(10000,19100),new NeedVO(10004,77)],"scene/DH002-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2003,34064,21527,[new BaseRewardVO(10000,4070),new BaseDropVO(200,new BaseRewardVO(12235,1))],[new NeedVO(10000,19200),new NeedVO(10005,78)],"scene/DH003-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2004,34210,21610,[new BaseRewardVO(10000,4080),new BaseDropVO(200,new BaseRewardVO(12232,1))],[new NeedVO(10000,19300),new NeedVO(10003,79)],"scene/DH004-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2005,34356,21693,[new BaseRewardVO(10000,4090),new BaseDropVO(200,new BaseRewardVO(12231,1))],[new NeedVO(10000,19400),new NeedVO(10004,80)],"scene/DH005-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2006,34502,21776,[new BaseRewardVO(10000,4100),new BaseDropVO(700,new BaseRewardVO(10653,1))],[new NeedVO(10000,19500),new NeedVO(10005,81)],"scene/DH006-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2007,34648,21859,[new BaseRewardVO(10000,4110),new BaseDropVO(200,new BaseRewardVO(12233,1))],[new NeedVO(10000,19600),new NeedVO(10003,82)],"scene/DH007-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2008,34794,21942,[new BaseRewardVO(10000,4120),new BaseDropVO(200,new BaseRewardVO(12249,1))],[new NeedVO(10000,19700),new NeedVO(10004,83)],"scene/DH008-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2009,34940,22025,[new BaseRewardVO(10000,4130),new BaseDropVO(200,new BaseRewardVO(12250,1))],[new NeedVO(10000,19800),new NeedVO(10005,84)],"scene/DH009-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2010,35086,22108,[new BaseRewardVO(10000,4140),new BaseDropVO(200,new BaseRewardVO(12246,1))],[new NeedVO(10000,19900),new NeedVO(10003,85)],"scene/DH010-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2011,35232,22191,[new BaseRewardVO(10000,4150),new BaseDropVO(200,new BaseRewardVO(12247,1))],[new NeedVO(10000,20000),new NeedVO(10004,86)],"scene/DH011-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2012,35378,22274,[new BaseRewardVO(10000,4160),new BaseDropVO(700,new BaseRewardVO(10655,1))],[new NeedVO(10000,20100),new NeedVO(10005,87)],"scene/DH012-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2013,35524,22357,[new BaseRewardVO(10000,4170),new BaseDropVO(200,new BaseRewardVO(12248,1))],[new NeedVO(10000,20200),new NeedVO(10003,88)],"scene/DH013-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2014,35670,22440,[new BaseRewardVO(10000,4180),new BaseDropVO(200,new BaseRewardVO(12239,1))],[new NeedVO(10000,20300),new NeedVO(10004,89)],"scene/DH014-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2015,35816,22523,[new BaseRewardVO(10000,4190),new BaseDropVO(200,new BaseRewardVO(12240,1))],[new NeedVO(10000,20400),new NeedVO(10005,90)],"scene/DH015-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2016,35962,22606,[new BaseRewardVO(10000,4200),new BaseDropVO(700,new BaseRewardVO(10654,1))],[new NeedVO(10000,20500),new NeedVO(10003,91)],"scene/DH016-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2017,36108,22689,[new BaseRewardVO(10000,4210),new BaseDropVO(200,new BaseRewardVO(12236,1))],[new NeedVO(10000,20600),new NeedVO(10004,92)],"scene/DH017-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2018,36254,22772,[new BaseRewardVO(10000,4220),new BaseDropVO(200,new BaseRewardVO(12237,1))],[new NeedVO(10000,20700),new NeedVO(10005,93)],"scene/DH018-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2019,36400,22855,[new BaseRewardVO(10000,4230),new BaseDropVO(200,new BaseRewardVO(12238,1))],[new NeedVO(10000,20800),new NeedVO(10006,94)],"scene/DH019-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2020,36546,22938,[new BaseRewardVO(10000,4240),new BaseDropVO(700,new BaseRewardVO(10715,1))],[new NeedVO(10000,20900),new NeedVO(10003,95)],"scene/DH015-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2021,36692,23021,[new BaseRewardVO(10000,4250),new BaseDropVO(200,new BaseRewardVO(12242,1))],[new NeedVO(10000,21000),new NeedVO(10004,96)],"scene/DH005-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2022,36838,23104,[new BaseRewardVO(10000,4260),new BaseDropVO(200,new BaseRewardVO(12244,1))],[new NeedVO(10000,21100),new NeedVO(10005,97)],"scene/DH014-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2023,36984,23187,[new BaseRewardVO(10000,4270),new BaseDropVO(200,new BaseRewardVO(12241,1))],[new NeedVO(10000,21200),new NeedVO(10003,98)],"scene/DH008-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2024,37130,23270,[new BaseRewardVO(10000,4280),new BaseDropVO(200,new BaseRewardVO(12245,1))],[new NeedVO(10000,21300),new NeedVO(10004,99)],"scene/DH011-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2025,37276,23353,[new BaseRewardVO(10000,4290),new BaseDropVO(700,new BaseRewardVO(10716,1))],[new NeedVO(10000,21400),new NeedVO(10005,100)],"scene/DH006-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2026,37422,23436,[new BaseRewardVO(10000,4300),new BaseDropVO(200,new BaseRewardVO(12243,1))],[new NeedVO(10000,21500),new NeedVO(10003,101)],"scene/DH013-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2027,37568,23519,[new BaseRewardVO(10000,4310),new BaseDropVO(200,new BaseRewardVO(12252,1))],[new NeedVO(10000,21600),new NeedVO(10004,102)],"scene/DH007-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2028,37714,23602,[new BaseRewardVO(10000,4320),new BaseDropVO(200,new BaseRewardVO(12254,1))],[new NeedVO(10000,21700),new NeedVO(10005,103)],"scene/DH012-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2029,37860,23685,[new BaseRewardVO(10000,4330),new BaseDropVO(200,new BaseRewardVO(12255,1))],[new NeedVO(10000,21800),new NeedVO(10003,104)],"scene/DH009-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2030,38006,23768,[new BaseRewardVO(10000,4340),new BaseDropVO(200,new BaseRewardVO(12251,1))],[new NeedVO(10000,21900),new NeedVO(10004,105)],"scene/DH010-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2031,38152,23851,[new BaseRewardVO(10000,4350),new BaseDropVO(700,new BaseRewardVO(10717,1))],[new NeedVO(10000,22000),new NeedVO(10005,106)],"scene/DH018-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2032,38298,23934,[new BaseRewardVO(10000,4360),new BaseDropVO(200,new BaseRewardVO(12253,1))],[new NeedVO(10000,22100),new NeedVO(10003,107)],"scene/DH002-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2033,38444,24017,[new BaseRewardVO(10000,4370),new BaseDropVO(100,new EquipRewardVO(25310,1))],[new NeedVO(10000,22200),new NeedVO(10004,108)],"scene/DH017-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2034,38590,24100,[new BaseRewardVO(10000,4380),new BaseDropVO(100,new EquipRewardVO(25010,1))],[new NeedVO(10000,22300),new NeedVO(10005,109)],"scene/DH003-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2035,38736,24183,[new BaseRewardVO(10000,4390),new BaseDropVO(100,new EquipRewardVO(25110,1))],[new NeedVO(10000,22400),new NeedVO(10003,110)],"scene/DH016-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2036,38882,24266,[new BaseRewardVO(10000,4400),new BaseDropVO(100,new EquipRewardVO(25410,1))],[new NeedVO(10000,22500),new NeedVO(10004,111)],"scene/DH016-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2037,39028,24349,[new BaseRewardVO(10000,4410),new BaseDropVO(100,new EquipRewardVO(25210,1))],[new NeedVO(10000,22600),new NeedVO(10005,112)],"scene/DH004-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2038,39174,24432,[new BaseRewardVO(10000,4420),new BaseDropVO(700,new BaseRewardVO(10722,1))],[new NeedVO(10000,22700),new NeedVO(10003,113)],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2039,39320,24515,[new BaseRewardVO(10000,4430),new BaseDropVO(200,new BaseRewardVO(12264,1))],[new NeedVO(10000,22800),new NeedVO(10004,114)],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2040,39466,24598,[new BaseRewardVO(10000,4440),new BaseDropVO(200,new BaseRewardVO(12265,1))],[new NeedVO(10000,22900),new NeedVO(10005,115)],"scene/DH022-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2041,39612,24681,[new BaseRewardVO(10000,4450),new BaseDropVO(200,new BaseRewardVO(12262,1))],[new NeedVO(10000,23000),new NeedVO(10003,116)],"scene/DH023-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2042,39758,24764,[new BaseRewardVO(10000,4460),new BaseDropVO(200,new BaseRewardVO(12261,1))],[new NeedVO(10000,23100),new NeedVO(10004,117)],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2043,39904,24847,[new BaseRewardVO(10000,4470),new BaseDropVO(700,new BaseRewardVO(10723,1))],[new NeedVO(10000,23200),new NeedVO(10005,118)],"scene/DH025-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2044,40050,24930,[new BaseRewardVO(10000,4480),new BaseDropVO(200,new BaseRewardVO(12263,1))],[new NeedVO(10000,23300),new NeedVO(10003,119)],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2045,40196,25013,[new BaseRewardVO(10000,4490),new BaseDropVO(200,new BaseRewardVO(12279,1))],[new NeedVO(10000,23400),new NeedVO(10004,120)],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2046,40342,25096,[new BaseRewardVO(10000,4500),new BaseDropVO(200,new BaseRewardVO(12280,1))],[new NeedVO(10000,23500),new NeedVO(10005,121)],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2047,40488,25179,[new BaseRewardVO(10000,4510),new BaseDropVO(200,new BaseRewardVO(12276,1))],[new NeedVO(10000,23600),new NeedVO(10003,122)],"scene/DH029-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2048,40634,25262,[new BaseRewardVO(10000,4520),new BaseDropVO(200,new BaseRewardVO(12277,1))],[new NeedVO(10000,23700),new NeedVO(10004,123)],"scene/DH030-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2049,40780,25345,[new BaseRewardVO(10000,4530),new BaseDropVO(700,new BaseRewardVO(10725,1))],[new NeedVO(10000,23800),new NeedVO(10005,124)],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2050,40926,25428,[new BaseRewardVO(10000,4540),new BaseDropVO(200,new BaseRewardVO(12278,1))],[new NeedVO(10000,23900),new NeedVO(10003,125)],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2051,41072,25511,[new BaseRewardVO(10000,4550),new BaseDropVO(200,new BaseRewardVO(12269,1))],[new NeedVO(10000,24000),new NeedVO(10004,126)],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2052,41218,25594,[new BaseRewardVO(10000,4560),new BaseDropVO(200,new BaseRewardVO(12270,1))],[new NeedVO(10000,24100),new NeedVO(10005,127)],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2053,41364,25677,[new BaseRewardVO(10000,4570),new BaseDropVO(200,new BaseRewardVO(12266,1))],[new NeedVO(10000,24200),new NeedVO(10003,128)],"scene/DH035-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2054,41510,25760,[new BaseRewardVO(10000,4580),new BaseDropVO(700,new BaseRewardVO(10724,1))],[new NeedVO(10000,24300),new NeedVO(10004,129)],"scene/DH036-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2055,41656,25843,[new BaseRewardVO(10000,4590),new BaseDropVO(200,new BaseRewardVO(12267,1))],[new NeedVO(10000,24400),new NeedVO(10005,130)],"scene/DH037-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2056,41802,25926,[new BaseRewardVO(10000,4600),new BaseDropVO(200,new BaseRewardVO(12268,1))],[new NeedVO(10000,24500),new NeedVO(10003,131)],"scene/DH038-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2057,41948,26009,[new BaseRewardVO(10000,4610),new BaseDropVO(700,new BaseRewardVO(10726,1))],[new NeedVO(10000,24600),new NeedVO(10003,132)],"scene/DH001-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2058,42094,26092,[new BaseRewardVO(10000,4620),new BaseDropVO(200,new BaseRewardVO(12272,1))],[new NeedVO(10000,24700),new NeedVO(10004,133)],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2059,42240,26175,[new BaseRewardVO(10000,4630),new BaseDropVO(200,new BaseRewardVO(12274,1))],[new NeedVO(10000,24800),new NeedVO(10005,134)],"scene/DH002-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2060,42386,26258,[new BaseRewardVO(10000,4640),new BaseDropVO(200,new BaseRewardVO(12271,1))],[new NeedVO(10000,24900),new NeedVO(10003,135)],"scene/DH023-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2061,42532,26341,[new BaseRewardVO(10000,4650),new BaseDropVO(200,new BaseRewardVO(12275,1))],[new NeedVO(10000,25000),new NeedVO(10004,136)],"scene/DH003-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2062,42678,26424,[new BaseRewardVO(10000,4660),new BaseDropVO(700,new BaseRewardVO(10727,1))],[new NeedVO(10000,25100),new NeedVO(10005,137)],"scene/DH025-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2063,42824,26507,[new BaseRewardVO(10000,4670),new BaseDropVO(200,new BaseRewardVO(12273,1))],[new NeedVO(10000,25200),new NeedVO(10003,138)],"scene/DH004-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2064,42970,26590,[new BaseRewardVO(10000,4680),new BaseDropVO(200,new BaseRewardVO(12282,1))],[new NeedVO(10000,25300),new NeedVO(10004,139)],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2065,43116,26673,[new BaseRewardVO(10000,4690),new BaseDropVO(200,new BaseRewardVO(12284,1))],[new NeedVO(10000,25400),new NeedVO(10005,140)],"scene/DH005-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2066,43262,26756,[new BaseRewardVO(10000,4700),new BaseDropVO(200,new BaseRewardVO(12285,1))],[new NeedVO(10000,25500),new NeedVO(10003,141)],"scene/DH029-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2067,43408,26839,[new BaseRewardVO(10000,4710),new BaseDropVO(200,new BaseRewardVO(12281,1))],[new NeedVO(10000,25600),new NeedVO(10004,142)],"scene/DH006-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2068,43554,26922,[new BaseRewardVO(10000,4720),new BaseDropVO(700,new BaseRewardVO(10728,1))],[new NeedVO(10000,25700),new NeedVO(10005,143)],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2069,43700,27005,[new BaseRewardVO(10000,4730),new BaseDropVO(200,new BaseRewardVO(12283,1))],[new NeedVO(10000,25800),new NeedVO(10003,144)],"scene/DH007-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2070,43846,27088,[new BaseRewardVO(10000,4740),new BaseDropVO(100,new EquipRewardVO(25311,1))],[new NeedVO(10000,25900),new NeedVO(10004,145)],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2071,43992,27171,[new BaseRewardVO(10000,4750),new BaseDropVO(100,new EquipRewardVO(25011,1))],[new NeedVO(10000,26000),new NeedVO(10005,146)],"scene/DH008-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2072,44138,27254,[new BaseRewardVO(10000,4760),new BaseDropVO(100,new EquipRewardVO(25111,1))],[new NeedVO(10000,26100),new NeedVO(10003,147)],"scene/DH035-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2073,44284,27337,[new BaseRewardVO(10000,4770),new BaseDropVO(100,new EquipRewardVO(25411,1))],[new NeedVO(10000,26200),new NeedVO(10004,148)],"scene/DH009-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2074,44430,27420,[new BaseRewardVO(10000,4780),new BaseDropVO(100,new EquipRewardVO(25211,1))],[new NeedVO(10000,26300),new NeedVO(10005,149)],"scene/DH037-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2075,44576,27503,[new BaseRewardVO(10000,4790),new BaseDropVO(400,new BaseRewardVO(10729,1))],[new NeedVO(10000,26400),new NeedVO(10003,150)],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2076,44722,27586,[new BaseRewardVO(10000,4800),new BaseDropVO(200,new BaseRewardVO(12294,1))],[new NeedVO(10000,26500),new NeedVO(10004,151)],"scene/DH011-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2077,44868,27669,[new BaseRewardVO(10000,4810),new BaseDropVO(200,new BaseRewardVO(12295,1))],[new NeedVO(10000,26600),new NeedVO(10005,152)],"scene/DH022-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2078,45014,27752,[new BaseRewardVO(10000,4820),new BaseDropVO(200,new BaseRewardVO(12292,1))],[new NeedVO(10000,26700),new NeedVO(10003,153)],"scene/DH012-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2079,45160,27835,[new BaseRewardVO(10000,4830),new BaseDropVO(200,new BaseRewardVO(12291,1))],[new NeedVO(10000,26800),new NeedVO(10004,154)],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2080,45306,27918,[new BaseRewardVO(10000,4840),new BaseDropVO(400,new BaseRewardVO(10730,1))],[new NeedVO(10000,26900),new NeedVO(10005,155)],"scene/DH013-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2081,45452,28001,[new BaseRewardVO(10000,4850),new BaseDropVO(200,new BaseRewardVO(12293,1))],[new NeedVO(10000,27000),new NeedVO(10003,156)],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2082,45598,28084,[new BaseRewardVO(10000,4860),new BaseDropVO(200,new BaseRewardVO(12309,1))],[new NeedVO(10000,27100),new NeedVO(10004,157)],"scene/DH014-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2083,45744,28167,[new BaseRewardVO(10000,4870),new BaseDropVO(200,new BaseRewardVO(12310,1))],[new NeedVO(10000,27200),new NeedVO(10005,158)],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2084,45890,28250,[new BaseRewardVO(10000,4880),new BaseDropVO(200,new BaseRewardVO(12306,1))],[new NeedVO(10000,27300),new NeedVO(10003,159)],"scene/DH015-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2085,46036,28333,[new BaseRewardVO(10000,4890),new BaseDropVO(200,new BaseRewardVO(12307,1))],[new NeedVO(10000,27400),new NeedVO(10004,160)],"scene/DH030-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2086,46182,28416,[new BaseRewardVO(10000,4900),new BaseDropVO(400,new BaseRewardVO(10731,1))],[new NeedVO(10000,27500),new NeedVO(10005,161)],"scene/DH016-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2087,46328,28499,[new BaseRewardVO(10000,4910),new BaseDropVO(200,new BaseRewardVO(12308,1))],[new NeedVO(10000,27600),new NeedVO(10003,162)],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2088,46474,28582,[new BaseRewardVO(10000,4920),new BaseDropVO(400,new BaseRewardVO(10732,1))],[new NeedVO(10000,27700),new NeedVO(10004,163)],"scene/DH017-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2089,46620,28665,[new BaseRewardVO(10000,4930),new BaseDropVO(200,new BaseRewardVO(12299,1))],[new NeedVO(10000,27800),new NeedVO(10005,164)],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2090,46766,28748,[new BaseRewardVO(10000,4940),new BaseDropVO(200,new BaseRewardVO(12300,1))],[new NeedVO(10000,27900),new NeedVO(10003,165)],"scene/DH018-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2091,46912,28831,[new BaseRewardVO(10000,4950),new BaseDropVO(200,new BaseRewardVO(12296,1))],[new NeedVO(10000,28000),new NeedVO(10004,166)],"scene/DH036-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2092,47058,28914,[new BaseRewardVO(10000,4960),new BaseDropVO(200,new BaseRewardVO(12297,1))],[new NeedVO(10000,28100),new NeedVO(10005,167)],"scene/DH019-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2093,47204,28997,[new BaseRewardVO(10000,4970),new BaseDropVO(200,new BaseRewardVO(12298,1))],[new NeedVO(10000,28200),new NeedVO(10003,168)],"scene/DH002-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2094,47350,29080,[new BaseRewardVO(10000,4980),new BaseDropVO(400,new BaseRewardVO(10733,1))],[new NeedVO(10000,28300),new NeedVO(10003,169)],"scene/DH001-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2095,47496,29163,[new BaseRewardVO(10000,4990),new BaseDropVO(200,new BaseRewardVO(12302,1))],[new NeedVO(10000,28400),new NeedVO(10004,170)],"scene/DH002-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2096,47642,29246,[new BaseRewardVO(10000,5000),new BaseDropVO(200,new BaseRewardVO(12304,1))],[new NeedVO(10000,28500),new NeedVO(10005,171)],"scene/DH003-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2097,47788,29329,[new BaseRewardVO(10000,5010),new BaseDropVO(200,new BaseRewardVO(12301,1))],[new NeedVO(10000,28600),new NeedVO(10003,172)],"scene/DH004-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2098,47934,29412,[new BaseRewardVO(10000,5020),new BaseDropVO(200,new BaseRewardVO(12305,1))],[new NeedVO(10000,28700),new NeedVO(10004,173)],"scene/DH005-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2099,48080,29495,[new BaseRewardVO(10000,5030),new BaseDropVO(400,new BaseRewardVO(10734,1))],[new NeedVO(10000,28800),new NeedVO(10005,174)],"scene/DH006-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2100,48226,29578,[new BaseRewardVO(10000,5040),new BaseDropVO(200,new BaseRewardVO(12303,1))],[new NeedVO(10000,28900),new NeedVO(10003,175)],"scene/DH007-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2101,48372,29661,[new BaseRewardVO(10000,5050),new BaseDropVO(200,new BaseRewardVO(12312,1))],[new NeedVO(10000,29000),new NeedVO(10004,176)],"scene/DH008-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2102,48518,29744,[new BaseRewardVO(10000,5060),new BaseDropVO(200,new BaseRewardVO(12314,1))],[new NeedVO(10000,29100),new NeedVO(10005,177)],"scene/DH009-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2103,48664,29827,[new BaseRewardVO(10000,5070),new BaseDropVO(200,new BaseRewardVO(12315,1))],[new NeedVO(10000,29200),new NeedVO(10006,178)],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2104,48810,29910,[new BaseRewardVO(10000,5080),new BaseDropVO(200,new BaseRewardVO(12311,1))],[new NeedVO(10000,29300),new NeedVO(10007,179)],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2105,48956,29993,[new BaseRewardVO(10000,5090),new BaseDropVO(400,new BaseRewardVO(10735,1))],[new NeedVO(10000,29400),new NeedVO(10004,180)],"scene/DH022-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2106,49102,30076,[new BaseRewardVO(10000,5100),new BaseDropVO(200,new BaseRewardVO(12313,1))],[new NeedVO(10000,29500),new NeedVO(10005,181)],"scene/DH023-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2107,49248,30159,[new BaseRewardVO(10000,5110),new BaseDropVO(100,new EquipRewardVO(25312,1))],[new NeedVO(10000,29600),new NeedVO(10003,182)],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2108,49394,30242,[new BaseRewardVO(10000,5120),new BaseDropVO(100,new EquipRewardVO(25012,1))],[new NeedVO(10000,29700),new NeedVO(10004,183)],"scene/DH025-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2109,49540,30325,[new BaseRewardVO(10000,5130),new BaseDropVO(100,new EquipRewardVO(25112,1))],[new NeedVO(10000,29800),new NeedVO(10005,184)],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2110,49686,30408,[new BaseRewardVO(10000,5140),new BaseDropVO(100,new EquipRewardVO(25412,1))],[new NeedVO(10000,29900),new NeedVO(10006,185)],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2111,49832,30491,[new BaseRewardVO(10000,5150),new BaseDropVO(100,new EquipRewardVO(25212,1))],[new NeedVO(10000,30000),new NeedVO(10007,186)],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2112,49978,30574,[new BaseRewardVO(10000,5150),new BaseDropVO(400,new BaseRewardVO(10739,1))],[new NeedVO(10000,30100),new NeedVO(10003,187)],"scene/yanz101.json");
         this._battles[this._battles.length] = new MissionBattleVO(2113,50124,30657,[new BaseRewardVO(10000,5160),new BaseDropVO(200,new BaseRewardVO(12324,1))],[new NeedVO(10000,30200),new NeedVO(10004,188)],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2114,50270,30740,[new BaseRewardVO(10000,5170),new BaseDropVO(200,new BaseRewardVO(12325,1))],[new NeedVO(10000,30300),new NeedVO(10005,189)],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(2115,50416,30823,[new BaseRewardVO(10000,5180),new BaseDropVO(200,new BaseRewardVO(12322,1))],[new NeedVO(10000,30400),new NeedVO(10003,190)],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2116,50562,30906,[new BaseRewardVO(10000,5190),new BaseDropVO(200,new BaseRewardVO(12321,1))],[new NeedVO(10000,30500),new NeedVO(10004,191)],"scene/yanz105.json");
         this._battles[this._battles.length] = new MissionBattleVO(2117,50708,30989,[new BaseRewardVO(10000,5200),new BaseDropVO(400,new BaseRewardVO(10740,1))],[new NeedVO(10000,30600),new NeedVO(10005,192)],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(2118,50854,31072,[new BaseRewardVO(10000,5210),new BaseDropVO(200,new BaseRewardVO(12323,1))],[new NeedVO(10000,30700),new NeedVO(10003,193)],"scene/yanz107.json");
         this._battles[this._battles.length] = new MissionBattleVO(2119,51000,31155,[new BaseRewardVO(10000,5220),new BaseDropVO(200,new BaseRewardVO(12339,1))],[new NeedVO(10000,30800),new NeedVO(10004,194)],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2120,51146,31238,[new BaseRewardVO(10000,5230),new BaseDropVO(200,new BaseRewardVO(12340,1))],[new NeedVO(10000,30900),new NeedVO(10005,195)],"scene/yanz109.json");
         this._battles[this._battles.length] = new MissionBattleVO(2121,51292,31321,[new BaseRewardVO(10000,5240),new BaseDropVO(200,new BaseRewardVO(12336,1))],[new NeedVO(10000,31000),new NeedVO(10003,196)],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(2122,51438,31404,[new BaseRewardVO(10000,5250),new BaseDropVO(200,new BaseRewardVO(12337,1))],[new NeedVO(10000,31100),new NeedVO(10004,197)],"scene/yanz111.json");
         this._battles[this._battles.length] = new MissionBattleVO(2123,51584,31487,[new BaseRewardVO(10000,5260),new BaseDropVO(400,new BaseRewardVO(10741,1))],[new NeedVO(10000,31200),new NeedVO(10005,198)],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(2124,51730,31570,[new BaseRewardVO(10000,5270),new BaseDropVO(200,new BaseRewardVO(12338,1))],[new NeedVO(10000,31300),new NeedVO(10003,199)],"scene/yanz113.json");
         this._battles[this._battles.length] = new MissionBattleVO(2125,51876,31653,[new BaseRewardVO(10000,5280),new BaseDropVO(400,new BaseRewardVO(10742,1))],[new NeedVO(10000,31400),new NeedVO(10004,200)],"scene/yanz114.json");
         this._battles[this._battles.length] = new MissionBattleVO(2126,52022,31736,[new BaseRewardVO(10000,5290),new BaseDropVO(200,new BaseRewardVO(12329,1))],[new NeedVO(10000,31500),new NeedVO(10005,201)],"scene/yanz115-430.json");
         this._battles[this._battles.length] = new MissionBattleVO(2127,52168,31819,[new BaseRewardVO(10000,5300),new BaseDropVO(200,new BaseRewardVO(12330,1))],[new NeedVO(10000,31600),new NeedVO(10003,202)],"scene/yanz116.json");
         this._battles[this._battles.length] = new MissionBattleVO(2128,52314,31902,[new BaseRewardVO(10000,5310),new BaseDropVO(200,new BaseRewardVO(12326,1))],[new NeedVO(10000,31700),new NeedVO(10004,203)],"scene/yanz117.json");
         this._battles[this._battles.length] = new MissionBattleVO(2129,52460,31985,[new BaseRewardVO(10000,5320),new BaseDropVO(200,new BaseRewardVO(12327,1))],[new NeedVO(10000,31800),new NeedVO(10005,204)],"scene/yanz118.json");
         this._battles[this._battles.length] = new MissionBattleVO(2130,52606,32068,[new BaseRewardVO(10000,5330),new BaseDropVO(200,new BaseRewardVO(12328,1))],[new NeedVO(10000,31900),new NeedVO(10003,205)],"scene/qinz119-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2131,52752,32151,[new BaseRewardVO(10000,5340),new BaseDropVO(400,new BaseRewardVO(10743,1))],[new NeedVO(10000,32000),new NeedVO(10003,206)],"scene/qinz101-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2132,52898,32234,[new BaseRewardVO(10000,5350),new BaseDropVO(200,new BaseRewardVO(12332,1))],[new NeedVO(10000,32100),new NeedVO(10004,207)],"scene/qinz102-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2133,53044,32317,[new BaseRewardVO(10000,5360),new BaseDropVO(200,new BaseRewardVO(12334,1))],[new NeedVO(10000,32200),new NeedVO(10005,208)],"scene/qinz103-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(2134,53190,32400,[new BaseRewardVO(10000,5370),new BaseDropVO(200,new BaseRewardVO(12331,1))],[new NeedVO(10000,32300),new NeedVO(10003,209)],"scene/qinz104-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2135,53336,32483,[new BaseRewardVO(10000,5380),new BaseDropVO(200,new BaseRewardVO(12335,1))],[new NeedVO(10000,32400),new NeedVO(10004,210)],"scene/qinz105-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2136,53482,32566,[new BaseRewardVO(10000,5390),new BaseDropVO(400,new BaseRewardVO(10744,1))],[new NeedVO(10000,32500),new NeedVO(10005,211)],"scene/qinz106-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(2137,53628,32649,[new BaseRewardVO(10000,5400),new BaseDropVO(200,new BaseRewardVO(12333,1))],[new NeedVO(10000,32600),new NeedVO(10003,212)],"scene/qinz107-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2138,53774,32732,[new BaseRewardVO(10000,5410),new BaseDropVO(200,new BaseRewardVO(12342,1))],[new NeedVO(10000,32700),new NeedVO(10004,213)],"scene/qinz108-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2139,53920,32815,[new BaseRewardVO(10000,5420),new BaseDropVO(200,new BaseRewardVO(12344,1))],[new NeedVO(10000,32800),new NeedVO(10005,214)],"scene/qinz109-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2140,54066,32898,[new BaseRewardVO(10000,5430),new BaseDropVO(200,new BaseRewardVO(12345,1))],[new NeedVO(10000,32900),new NeedVO(10006,215)],"scene/qinz110-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(2141,54212,32981,[new BaseRewardVO(10000,5440),new BaseDropVO(200,new BaseRewardVO(12341,1))],[new NeedVO(10000,33000),new NeedVO(10007,216)],"scene/qinz111-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2142,54358,33064,[new BaseRewardVO(10000,5450),new BaseDropVO(400,new BaseRewardVO(10745,1))],[new NeedVO(10000,33100),new NeedVO(10004,217)],"scene/qinz112-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(2143,54504,33147,[new BaseRewardVO(10000,5460),new BaseDropVO(200,new BaseRewardVO(12343,1))],[new NeedVO(10000,33200),new NeedVO(10005,218)],"scene/qinz113-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2144,54650,33230,[new BaseRewardVO(10000,5470),new BaseDropVO(100,new EquipRewardVO(25313,1))],[new NeedVO(10000,33300),new NeedVO(10003,219)],"scene/qinz114-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(2145,54796,33313,[new BaseRewardVO(10000,5480),new BaseDropVO(100,new EquipRewardVO(25013,1))],[new NeedVO(10000,33400),new NeedVO(10004,220)],"scene/qinz115-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2146,54942,33396,[new BaseRewardVO(10000,5490),new BaseDropVO(100,new EquipRewardVO(25113,1))],[new NeedVO(10000,33500),new NeedVO(10005,221)],"scene/qinz116-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(2147,55088,33479,[new BaseRewardVO(10000,5500),new BaseDropVO(100,new EquipRewardVO(25413,1))],[new NeedVO(10000,33600),new NeedVO(10006,222)],"scene/qinz117-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2148,55234,33562,[new BaseRewardVO(10000,5510),new BaseDropVO(100,new EquipRewardVO(25213,1))],[new NeedVO(10000,33700),new NeedVO(10007,223)],"scene/qinz118-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2149,55380,33645,[new BaseRewardVO(10000,5520),new BaseDropVO(400,new BaseRewardVO(10749,1))],[new NeedVO(10000,33800),new NeedVO(10003,224)],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2150,55526,33728,[new BaseRewardVO(10000,5530),new BaseDropVO(200,new BaseRewardVO(12354,1))],[new NeedVO(10000,33900),new NeedVO(10004,225)],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2151,55672,33811,[new BaseRewardVO(10000,5540),new BaseDropVO(200,new BaseRewardVO(12355,1))],[new NeedVO(10000,34000),new NeedVO(10005,226)],"scene/DH022-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2152,55818,33894,[new BaseRewardVO(10000,5550),new BaseDropVO(200,new BaseRewardVO(12352,1))],[new NeedVO(10000,34100),new NeedVO(10006,227)],"scene/DH023-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2153,55964,33977,[new BaseRewardVO(10000,5560),new BaseDropVO(200,new BaseRewardVO(12351,1))],[new NeedVO(10000,34200),new NeedVO(10007,228)],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2154,56110,34060,[new BaseRewardVO(10000,5570),new BaseDropVO(400,new BaseRewardVO(10750,1))],[new NeedVO(10000,34300),new NeedVO(10003,229)],"scene/DH025-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2155,56256,34143,[new BaseRewardVO(10000,5580),new BaseDropVO(200,new BaseRewardVO(12353,1))],[new NeedVO(10000,34400),new NeedVO(10004,230)],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2156,56402,34226,[new BaseRewardVO(10000,5590),new BaseDropVO(200,new BaseRewardVO(12359,1))],[new NeedVO(10000,34500),new NeedVO(10005,231)],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2157,56548,34309,[new BaseRewardVO(10000,5600),new BaseDropVO(200,new BaseRewardVO(12360,1))],[new NeedVO(10000,34600),new NeedVO(10006,232)],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2158,56694,34392,[new BaseRewardVO(10000,5610),new BaseDropVO(200,new BaseRewardVO(12356,1))],[new NeedVO(10000,34700),new NeedVO(10007,233)],"scene/DH029-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2159,56840,34475,[new BaseRewardVO(10000,5620),new BaseDropVO(200,new BaseRewardVO(12357,1))],[new NeedVO(10000,34800),new NeedVO(10003,234)],"scene/DH030-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2160,56986,34558,[new BaseRewardVO(10000,5630),new BaseDropVO(400,new BaseRewardVO(10751,1))],[new NeedVO(10000,34900),new NeedVO(10004,235)],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2161,57132,34641,[new BaseRewardVO(10000,5640),new BaseDropVO(200,new BaseRewardVO(12358,1))],[new NeedVO(10000,35000),new NeedVO(10005,236)],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2162,57278,34724,[new BaseRewardVO(10000,5650),new BaseDropVO(400,new BaseRewardVO(10752,1))],[new NeedVO(10000,35100),new NeedVO(10006,237)],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2163,57424,34807,[new BaseRewardVO(10000,5660),new BaseDropVO(200,new BaseRewardVO(12369,1))],[new NeedVO(10000,35200),new NeedVO(10007,238)],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2164,57570,34890,[new BaseRewardVO(10000,5670),new BaseDropVO(200,new BaseRewardVO(12370,1))],[new NeedVO(10000,35300),new NeedVO(10003,239)],"scene/DH035-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2165,57716,34973,[new BaseRewardVO(10000,5680),new BaseDropVO(200,new BaseRewardVO(12366,1))],[new NeedVO(10000,35400),new NeedVO(10004,240)],"scene/DH036-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2166,57862,35056,[new BaseRewardVO(10000,5690),new BaseDropVO(200,new BaseRewardVO(12367,1))],[new NeedVO(10000,35500),new NeedVO(10005,241)],"scene/DH037-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2167,58008,35139,[new BaseRewardVO(10000,5700),new BaseDropVO(200,new BaseRewardVO(12368,1))],[new NeedVO(10000,35600),new NeedVO(10006,242)],"scene/DH038-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2168,58154,35222,[new BaseRewardVO(10000,5710),new BaseDropVO(400,new BaseRewardVO(10753,1))],[new NeedVO(10000,35700),new NeedVO(10005,243)],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2169,58300,35305,[new BaseRewardVO(10000,5720),new BaseDropVO(200,new BaseRewardVO(12362,1))],[new NeedVO(10000,35800),new NeedVO(10006,244)],"scene/DH017-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2170,58446,35388,[new BaseRewardVO(10000,5730),new BaseDropVO(200,new BaseRewardVO(12364,1))],[new NeedVO(10000,35900),new NeedVO(10007,245)],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2171,58592,35471,[new BaseRewardVO(10000,5740),new BaseDropVO(200,new BaseRewardVO(12361,1))],[new NeedVO(10000,36000),new NeedVO(10005,246)],"scene/DH018-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2172,58738,35554,[new BaseRewardVO(10000,5750),new BaseDropVO(200,new BaseRewardVO(12365,1))],[new NeedVO(10000,36100),new NeedVO(10006,247)],"scene/DH036-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2173,58884,35637,[new BaseRewardVO(10000,5760),new BaseDropVO(400,new BaseRewardVO(10754,1))],[new NeedVO(10000,36200),new NeedVO(10007,248)],"scene/DH019-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2174,59030,35720,[new BaseRewardVO(10000,5770),new BaseDropVO(200,new BaseRewardVO(12363,1))],[new NeedVO(10000,36300),new NeedVO(10005,249)],"scene/DH002-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2175,59176,35803,[new BaseRewardVO(10000,5780),new BaseDropVO(200,new BaseRewardVO(12372,1))],[new NeedVO(10000,36400),new NeedVO(10006,250)],"scene/DH001-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2176,59322,35886,[new BaseRewardVO(10000,5790),new BaseDropVO(200,new BaseRewardVO(12374,1))],[new NeedVO(10000,36500),new NeedVO(10007,251)],"scene/DH002-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2177,59468,35969,[new BaseRewardVO(10000,5800),new BaseDropVO(200,new BaseRewardVO(12375,1))],[new NeedVO(10000,36600),new NeedVO(10005,252)],"scene/DH003-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2178,59614,36052,[new BaseRewardVO(10000,5810),new BaseDropVO(200,new BaseRewardVO(12371,1))],[new NeedVO(10000,36700),new NeedVO(10006,253)],"scene/DH004-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2179,59760,36135,[new BaseRewardVO(10000,5820),new BaseDropVO(400,new BaseRewardVO(10755,1))],[new NeedVO(10000,36800),new NeedVO(10007,254)],"scene/DH005-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2180,59906,36218,[new BaseRewardVO(10000,5830),new BaseDropVO(200,new BaseRewardVO(12373,1))],[new NeedVO(10000,36900),new NeedVO(10005,255)],"scene/DH006-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2181,60052,36301,[new BaseRewardVO(10000,5840),new BaseDropVO(100,new EquipRewardVO(25314,1))],[new NeedVO(10000,37000),new NeedVO(10006,256)],"scene/DH007-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2182,60198,36384,[new BaseRewardVO(10000,5850),new BaseDropVO(100,new EquipRewardVO(25014,1))],[new NeedVO(10000,37100),new NeedVO(10007,257)],"scene/DH008-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2183,60344,36467,[new BaseRewardVO(10000,5860),new BaseDropVO(100,new EquipRewardVO(25114,1))],[new NeedVO(10000,37200),new NeedVO(10005,258)],"scene/DH009-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2184,60490,36550,[new BaseRewardVO(10000,5870),new BaseDropVO(100,new EquipRewardVO(25414,1))],[new NeedVO(10000,37300),new NeedVO(10006,259)],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2185,60636,36633,[new BaseRewardVO(10000,5880),new BaseDropVO(100,new EquipRewardVO(25214,1))],[new NeedVO(10000,37400),new NeedVO(10007,260)],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2186,60782,37350,[new BaseRewardVO(10000,5890),new BaseDropVO(400,new BaseRewardVO(10769,1))],[new NeedVO(10000,37500),new NeedVO(10007,261)],"scene/yangz101-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2187,60928,37400,[new BaseRewardVO(10000,5900),new BaseDropVO(200,new BaseRewardVO(12424,1))],[new NeedVO(10000,37600),new NeedVO(10005,262)],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2188,61074,37450,[new BaseRewardVO(10000,5910),new BaseDropVO(200,new BaseRewardVO(12425,1))],[new NeedVO(10000,37700),new NeedVO(10006,263)],"scene/yangz103-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2189,61220,37500,[new BaseRewardVO(10000,5920),new BaseDropVO(200,new BaseRewardVO(12422,1))],[new NeedVO(10000,37800),new NeedVO(10007,264)],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2190,61366,37550,[new BaseRewardVO(10000,5930),new BaseDropVO(200,new BaseRewardVO(12421,1))],[new NeedVO(10000,37900),new NeedVO(10006,265)],"scene/yangz105-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2191,61512,37600,[new BaseRewardVO(10000,5940),new BaseDropVO(400,new BaseRewardVO(10770,1))],[new NeedVO(10000,38000),new NeedVO(10005,266)],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(2192,61658,37650,[new BaseRewardVO(10000,5950),new BaseDropVO(200,new BaseRewardVO(12423,1))],[new NeedVO(10000,38100),new NeedVO(10006,267)],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2193,61804,37700,[new BaseRewardVO(10000,5960),new BaseDropVO(200,new BaseRewardVO(12429,1))],[new NeedVO(10000,38200),new NeedVO(10007,268)],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2194,61950,37750,[new BaseRewardVO(10000,5970),new BaseDropVO(200,new BaseRewardVO(12430,1))],[new NeedVO(10000,38300),new NeedVO(10006,269)],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2195,62096,37800,[new BaseRewardVO(10000,5980),new BaseDropVO(200,new BaseRewardVO(12426,1))],[new NeedVO(10000,38400),new NeedVO(10005,270)],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(2196,62242,37850,[new BaseRewardVO(10000,5990),new BaseDropVO(200,new BaseRewardVO(12427,1))],[new NeedVO(10000,38500),new NeedVO(10006,271)],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2197,62388,37900,[new BaseRewardVO(10000,6000),new BaseDropVO(400,new BaseRewardVO(10771,1))],[new NeedVO(10000,38600),new NeedVO(10007,272)],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(2198,62534,37950,[new BaseRewardVO(10000,6010),new BaseDropVO(200,new BaseRewardVO(12428,1))],[new NeedVO(10000,38700),new NeedVO(10006,273)],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2199,62680,38000,[new BaseRewardVO(10000,6020),new BaseDropVO(400,new BaseRewardVO(10772,1))],[new NeedVO(10000,38800),new NeedVO(10005,274)],"scene/yanz114.json");
         this._battles[this._battles.length] = new MissionBattleVO(2200,62826,38050,[new BaseRewardVO(10000,6030),new BaseDropVO(200,new BaseRewardVO(12439,1))],[new NeedVO(10000,38900),new NeedVO(10006,275)],"scene/yangz115-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2201,62972,38100,[new BaseRewardVO(10000,6040),new BaseDropVO(200,new BaseRewardVO(12440,1))],[new NeedVO(10000,39000),new NeedVO(10007,276)],"scene/yanz116.json");
         this._battles[this._battles.length] = new MissionBattleVO(2202,63118,38150,[new BaseRewardVO(10000,6050),new BaseDropVO(200,new BaseRewardVO(12436,1))],[new NeedVO(10000,39100),new NeedVO(10006,277)],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2203,63264,38200,[new BaseRewardVO(10000,6060),new BaseDropVO(200,new BaseRewardVO(12437,1))],[new NeedVO(10000,39200),new NeedVO(10005,278)],"scene/yanz118.json");
         this._battles[this._battles.length] = new MissionBattleVO(2204,63410,38250,[new BaseRewardVO(10000,6070),new BaseDropVO(200,new BaseRewardVO(12438,1))],[new NeedVO(10000,39300),new NeedVO(10006,279)],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2205,63556,76600,[new BaseRewardVO(10000,6080),new BaseDropVO(400,new BaseRewardVO(10773,1))],[new NeedVO(10000,39400),new NeedVO(10005,280)],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2206,63702,76700,[new BaseRewardVO(10000,6090),new BaseDropVO(200,new BaseRewardVO(12432,1))],[new NeedVO(10000,39500),new NeedVO(10006,281)],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2207,63848,76800,[new BaseRewardVO(10000,6100),new BaseDropVO(200,new BaseRewardVO(12434,1))],[new NeedVO(10000,39600),new NeedVO(10007,282)],"scene/DH022-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2208,63994,76900,[new BaseRewardVO(10000,6110),new BaseDropVO(200,new BaseRewardVO(12431,1))],[new NeedVO(10000,39700),new NeedVO(10005,283)],"scene/DH023-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2209,64140,77000,[new BaseRewardVO(10000,6120),new BaseDropVO(200,new BaseRewardVO(12435,1))],[new NeedVO(10000,39800),new NeedVO(10006,284)],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2210,64286,77100,[new BaseRewardVO(10000,6130),new BaseDropVO(400,new BaseRewardVO(10774,1))],[new NeedVO(10000,39900),new NeedVO(10007,285)],"scene/DH025-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2211,64432,77200,[new BaseRewardVO(10000,6140),new BaseDropVO(200,new BaseRewardVO(12433,1))],[new NeedVO(10000,40000),new NeedVO(10005,286)],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2212,64578,77300,[new BaseRewardVO(10000,6150),new BaseDropVO(200,new BaseRewardVO(12442,1))],[new NeedVO(10000,40100),new NeedVO(10006,287)],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2213,64724,77400,[new BaseRewardVO(10000,6160),new BaseDropVO(200,new BaseRewardVO(12444,1))],[new NeedVO(10000,40200),new NeedVO(10007,288)],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2214,64870,77500,[new BaseRewardVO(10000,6170),new BaseDropVO(200,new BaseRewardVO(12445,1))],[new NeedVO(10000,40300),new NeedVO(10005,289)],"scene/DH029-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2215,65016,77600,[new BaseRewardVO(10000,6180),new BaseDropVO(200,new BaseRewardVO(12441,1))],[new NeedVO(10000,40400),new NeedVO(10006,290)],"scene/DH030-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2216,65162,77700,[new BaseRewardVO(10000,6190),new BaseDropVO(400,new BaseRewardVO(10775,1))],[new NeedVO(10000,40500),new NeedVO(10007,291)],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2217,65308,77800,[new BaseRewardVO(10000,6200),new BaseDropVO(200,new BaseRewardVO(12443,1))],[new NeedVO(10000,40600),new NeedVO(10005,292)],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2218,65454,77900,[new BaseRewardVO(10000,6210),new BaseDropVO(100,new EquipRewardVO(25315,1))],[new NeedVO(10000,40700),new NeedVO(10006,293)],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2219,65600,78000,[new BaseRewardVO(10000,6220),new BaseDropVO(100,new EquipRewardVO(25015,1))],[new NeedVO(10000,40800),new NeedVO(10007,294)],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2220,65746,78100,[new BaseRewardVO(10000,6230),new BaseDropVO(100,new EquipRewardVO(25115,1))],[new NeedVO(10000,40900),new NeedVO(10005,295)],"scene/DH035-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2221,65892,78200,[new BaseRewardVO(10000,6240),new BaseDropVO(100,new EquipRewardVO(25415,1))],[new NeedVO(10000,41000),new NeedVO(10006,296)],"scene/DH036-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2222,66038,78300,[new BaseRewardVO(10000,6250),new BaseDropVO(100,new EquipRewardVO(25215,1))],[new NeedVO(10000,41100),new NeedVO(10007,297)],"scene/DH037-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2223,66184,78400,[new BaseRewardVO(10000,6260)],[new NeedVO(10000,41200),new NeedVO(10005,298)],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2224,66330,78500,[new BaseRewardVO(10000,6270)],[new NeedVO(10000,41300),new NeedVO(10006,299)],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2225,66476,78600,[new BaseRewardVO(10000,6280)],[new NeedVO(10000,41400),new NeedVO(10007,300)],"scene/DH022-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2226,66622,78700,[new BaseRewardVO(10000,6290)],[new NeedVO(10000,41500),new NeedVO(10005,301)],"scene/DH023-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2227,66768,78800,[new BaseRewardVO(10000,6300),new BaseDropVO(400,new BaseRewardVO(10577,1))],[new NeedVO(10000,41600),new NeedVO(10006,302)],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2228,66914,78900,[new BaseRewardVO(10000,6310)],[new NeedVO(10000,41700),new NeedVO(10007,303)],"scene/DH025-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2229,67060,79000,[new BaseRewardVO(10000,6320)],[new NeedVO(10000,41800),new NeedVO(10005,304)],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2230,67206,79100,[new BaseRewardVO(10000,6330)],[new NeedVO(10000,41900),new NeedVO(10006,305)],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2231,67352,79200,[new BaseRewardVO(10000,6340)],[new NeedVO(10000,42000),new NeedVO(10007,306)],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2232,67498,79300,[new BaseRewardVO(10000,6350),new BaseDropVO(400,new BaseRewardVO(10578,1))],[new NeedVO(10000,42100),new NeedVO(10005,307)],"scene/DH029-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2233,67644,79400,[new BaseRewardVO(10000,6360)],[new NeedVO(10000,42200),new NeedVO(10006,308)],"scene/DH030-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2234,67790,79500,[new BaseRewardVO(10000,6370)],[new NeedVO(10000,42300),new NeedVO(10007,309)],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2235,67936,79600,[new BaseRewardVO(10000,6380)],[new NeedVO(10000,42400),new NeedVO(10005,310)],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2236,68082,79700,[new BaseRewardVO(10000,6390)],[new NeedVO(10000,42500),new NeedVO(10579,311)],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2237,68228,79800,[new BaseRewardVO(10000,6400),new BaseDropVO(400,new BaseRewardVO(10579,1))],[new NeedVO(10000,42600),new NeedVO(10007,312)],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2238,68374,79900,[new BaseRewardVO(10000,6410)],[new NeedVO(10000,42700),new NeedVO(10005,313)],"scene/yanz101.json");
         this._battles[this._battles.length] = new MissionBattleVO(2239,68520,80000,[new BaseRewardVO(10000,6420)],[new NeedVO(10000,42800),new NeedVO(10006,314)],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2240,68666,80100,[new BaseRewardVO(10000,6430)],[new NeedVO(10000,42900),new NeedVO(10007,315)],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(2241,68812,80200,[new BaseRewardVO(10000,6440)],[new NeedVO(10000,43000),new NeedVO(10005,316)],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2242,68958,80300,[new BaseRewardVO(10000,6450),new BaseDropVO(400,new BaseRewardVO(10581,1))],[new NeedVO(10000,43100),new NeedVO(10006,317)],"scene/yanz105.json");
         this._battles[this._battles.length] = new MissionBattleVO(2243,69104,80400,[new BaseRewardVO(10000,6460)],[new NeedVO(10000,43200),new NeedVO(10007,318)],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(2244,69250,80500,[new BaseRewardVO(10000,6470)],[new NeedVO(10000,43300),new NeedVO(10005,319)],"scene/yanz107.json");
         this._battles[this._battles.length] = new MissionBattleVO(2245,69396,80600,[new BaseRewardVO(10000,6480)],[new NeedVO(10000,43400),new NeedVO(10006,320)],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2246,69542,80700,[new BaseRewardVO(10000,6490)],[new NeedVO(10000,43500),new NeedVO(10007,321)],"scene/yanz109.json");
         this._battles[this._battles.length] = new MissionBattleVO(2247,69688,80800,[new BaseRewardVO(10000,6500),new BaseDropVO(400,new BaseRewardVO(10582,1))],[new NeedVO(10000,43600),new NeedVO(10005,322)],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(2248,69834,80900,[new BaseRewardVO(10000,6510)],[new NeedVO(10000,43700),new NeedVO(10006,323)],"scene/yanz111.json");
         this._battles[this._battles.length] = new MissionBattleVO(2249,69980,81000,[new BaseRewardVO(10000,6520)],[new NeedVO(10000,43800),new NeedVO(10007,324)],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(2250,70126,81100,[new BaseRewardVO(10000,6530)],[new NeedVO(10000,43900),new NeedVO(10005,325)],"scene/yanz113.json");
         this._battles[this._battles.length] = new MissionBattleVO(2251,70272,81200,[new BaseRewardVO(10000,6540)],[new NeedVO(10000,44000),new NeedVO(10006,326)],"scene/yanz114.json");
         this._battles[this._battles.length] = new MissionBattleVO(2252,70418,81300,[new BaseRewardVO(10000,6550),new BaseDropVO(400,new BaseRewardVO(10583,1))],[new NeedVO(10000,44100),new NeedVO(10007,327)],"scene/yanz115-430.json");
         this._battles[this._battles.length] = new MissionBattleVO(2253,70564,81400,[new BaseRewardVO(10000,6560)],[new NeedVO(10000,42700),new NeedVO(10005,328)],"scene/qinz119-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2254,70710,81500,[new BaseRewardVO(10000,6570)],[new NeedVO(10000,42800),new NeedVO(10006,329)],"scene/qinz101-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2255,70856,81600,[new BaseRewardVO(10000,6580)],[new NeedVO(10000,42900),new NeedVO(10007,330)],"scene/qinz102-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2256,71002,81700,[new BaseRewardVO(10000,6590)],[new NeedVO(10000,43000),new NeedVO(10005,331)],"scene/qinz103-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(2257,71148,81800,[new BaseRewardVO(10000,6600),new BaseDropVO(400,new BaseRewardVO(10585,1))],[new NeedVO(10000,43100),new NeedVO(10006,332)],"scene/qinz104-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2258,71294,81900,[new BaseRewardVO(10000,6610)],[new NeedVO(10000,43200),new NeedVO(10007,333)],"scene/qinz105-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2259,71440,82000,[new BaseRewardVO(10000,6620)],[new NeedVO(10000,43300),new NeedVO(10005,334)],"scene/qinz106-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(2260,71586,82100,[new BaseRewardVO(10000,6630)],[new NeedVO(10000,43400),new NeedVO(10006,335)],"scene/qinz107-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2261,71732,82200,[new BaseRewardVO(10000,6640)],[new NeedVO(10000,43500),new NeedVO(10007,336)],"scene/qinz108-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2262,71878,82300,[new BaseRewardVO(10000,6650),new BaseDropVO(400,new BaseRewardVO(10586,1))],[new NeedVO(10000,43600),new NeedVO(10005,337)],"scene/qinz109-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2263,72024,82400,[new BaseRewardVO(10000,6660)],[new NeedVO(10000,43700),new NeedVO(10006,338)],"scene/qinz110-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(2264,72170,82500,[new BaseRewardVO(10000,6670)],[new NeedVO(10000,43800),new NeedVO(10007,339)],"scene/qinz111-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2265,72316,82600,[new BaseRewardVO(10000,6680)],[new NeedVO(10000,43900),new NeedVO(10005,340)],"scene/qinz112-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(2266,72462,82700,[new BaseRewardVO(10000,6690)],[new NeedVO(10000,44000),new NeedVO(10006,341)],"scene/qinz113-160.json");
         this._battles[this._battles.length] = new MissionBattleVO(2267,72608,82800,[new BaseRewardVO(10000,6700),new BaseDropVO(400,new BaseRewardVO(10587,1))],[new NeedVO(10000,44100),new NeedVO(10007,342)],"scene/qinz114-130.json");
         this._battles[this._battles.length] = new MissionBattleVO(2268,72754,82900,[new BaseRewardVO(10000,6710)],[new NeedVO(10000,44200),new NeedVO(10005,343)],"scene/DH036-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2269,72900,83000,[new BaseRewardVO(10000,6720)],[new NeedVO(10000,44300),new NeedVO(10006,344)],"scene/DH037-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2270,73046,83100,[new BaseRewardVO(10000,6730)],[new NeedVO(10000,44400),new NeedVO(10007,345)],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2271,73192,83200,[new BaseRewardVO(10000,6740)],[new NeedVO(10000,44500),new NeedVO(10005,346)],"scene/DH011-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2272,73338,83300,[new BaseRewardVO(10000,6750),new BaseDropVO(400,new BaseRewardVO(10590,1))],[new NeedVO(10000,44600),new NeedVO(10006,347)],"scene/DH022-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2273,73484,83400,[new BaseRewardVO(10000,6760)],[new NeedVO(10000,44700),new NeedVO(10007,348)],"scene/DH012-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2274,73630,83500,[new BaseRewardVO(10000,6770)],[new NeedVO(10000,44800),new NeedVO(10005,349)],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2275,73776,83600,[new BaseRewardVO(10000,6780)],[new NeedVO(10000,44900),new NeedVO(10006,350)],"scene/DH013-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2276,73922,83700,[new BaseRewardVO(10000,6790)],[new NeedVO(10000,45000),new NeedVO(10007,351)],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2277,74068,83800,[new BaseRewardVO(10000,6800),new BaseDropVO(400,new BaseRewardVO(10591,1))],[new NeedVO(10000,45100),new NeedVO(10005,352)],"scene/DH014-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2278,74214,83900,[new BaseRewardVO(10000,6810)],[new NeedVO(10000,45200),new NeedVO(10006,353)],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2279,74360,84000,[new BaseRewardVO(10000,6820)],[new NeedVO(10000,45300),new NeedVO(10007,354)],"scene/DH015-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2280,74506,84100,[new BaseRewardVO(10000,6830)],[new NeedVO(10000,45400),new NeedVO(10005,355)],"scene/DH030-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2281,74652,84200,[new BaseRewardVO(10000,6840)],[new NeedVO(10000,45500),new NeedVO(10006,356)],"scene/DH016-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2282,74798,84300,[new BaseRewardVO(10000,6850),new BaseDropVO(400,new BaseRewardVO(10592,1))],[new NeedVO(10000,45600),new NeedVO(10007,357)],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2283,74944,84400,[new BaseRewardVO(10000,6860)],[new NeedVO(10000,44200),new NeedVO(10005,358)],"scene/DH003-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2284,75090,84500,[new BaseRewardVO(10000,6870)],[new NeedVO(10000,44300),new NeedVO(10006,359)],"scene/DH004-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2285,75236,84600,[new BaseRewardVO(10000,6880)],[new NeedVO(10000,44400),new NeedVO(10007,360)],"scene/DH005-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2286,75382,84700,[new BaseRewardVO(10000,6890)],[new NeedVO(10000,44500),new NeedVO(10005,361)],"scene/DH006-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2287,75528,84800,[new BaseRewardVO(10000,6900),new BaseDropVO(400,new BaseRewardVO(10594,1))],[new NeedVO(10000,44600),new NeedVO(10006,362)],"scene/DH007-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2288,75674,84900,[new BaseRewardVO(10000,6910)],[new NeedVO(10000,44700),new NeedVO(10007,363)],"scene/DH008-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2289,75820,85000,[new BaseRewardVO(10000,6920)],[new NeedVO(10000,44800),new NeedVO(10005,364)],"scene/DH009-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2290,75966,85100,[new BaseRewardVO(10000,6930)],[new NeedVO(10000,44900),new NeedVO(10006,365)],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2291,76112,85200,[new BaseRewardVO(10000,6940)],[new NeedVO(10000,45000),new NeedVO(10007,366)],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2292,76258,85300,[new BaseRewardVO(10000,6950),new BaseDropVO(400,new BaseRewardVO(10595,1))],[new NeedVO(10000,45100),new NeedVO(10005,367)],"scene/yangz101-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2293,76404,85400,[new BaseRewardVO(10000,6960)],[new NeedVO(10000,45200),new NeedVO(10006,368)],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2294,76550,85500,[new BaseRewardVO(10000,6970)],[new NeedVO(10000,45300),new NeedVO(10007,369)],"scene/yangz103-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2295,76696,85600,[new BaseRewardVO(10000,6980)],[new NeedVO(10000,45400),new NeedVO(10005,370)],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2296,76842,85700,[new BaseRewardVO(10000,6990)],[new NeedVO(10000,45500),new NeedVO(10006,371)],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2297,76988,85800,[new BaseRewardVO(10000,7000),new BaseDropVO(400,new BaseRewardVO(10596,1))],[new NeedVO(10000,45600),new NeedVO(10007,372)],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2298,77134,85900,[new BaseRewardVO(10000,7010)],[new NeedVO(10000,45700),new NeedVO(10005,373)],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2299,77280,86000,[new BaseRewardVO(10000,7020)],[new NeedVO(10000,45800),new NeedVO(10006,374)],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2300,77426,86100,[new BaseRewardVO(10000,7030)],[new NeedVO(10000,45900),new NeedVO(10007,375)],"scene/DH035-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2301,77572,86200,[new BaseRewardVO(10000,7040)],[new NeedVO(10000,46000),new NeedVO(10005,376)],"scene/DH036-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2302,77718,86300,[new BaseRewardVO(10000,7050),new BaseDropVO(400,new BaseRewardVO(10598,1))],[new NeedVO(10000,46100),new NeedVO(10006,377)],"scene/DH037-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2303,77864,86400,[new BaseRewardVO(10000,7060)],[new NeedVO(10000,46200),new NeedVO(10007,378)],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2304,78010,86500,[new BaseRewardVO(10000,7070)],[new NeedVO(10000,46300),new NeedVO(10005,379)],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2305,78156,86600,[new BaseRewardVO(10000,7080)],[new NeedVO(10000,46400),new NeedVO(10006,380)],"scene/DH022-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2306,78302,86700,[new BaseRewardVO(10000,7090)],[new NeedVO(10000,46500),new NeedVO(10007,381)],"scene/DH023-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2307,78448,86800,[new BaseRewardVO(10000,7100),new BaseDropVO(400,new BaseRewardVO(10599,1))],[new NeedVO(10000,46600),new NeedVO(10005,382)],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2308,78594,86900,[new BaseRewardVO(10000,7110)],[new NeedVO(10000,46700),new NeedVO(10006,383)],"scene/DH025-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2309,78740,87000,[new BaseRewardVO(10000,7120)],[new NeedVO(10000,46800),new NeedVO(10007,384)],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2310,78886,87100,[new BaseRewardVO(10000,7130)],[new NeedVO(10000,46900),new NeedVO(10005,385)],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2311,79032,87200,[new BaseRewardVO(10000,7140)],[new NeedVO(10000,47000),new NeedVO(10006,386)],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2312,79178,87300,[new BaseRewardVO(10000,7150),new BaseDropVO(400,new BaseRewardVO(10600,1))],[new NeedVO(10000,47100),new NeedVO(10007,387)],"scene/DH029-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2313,79324,87400,[new BaseRewardVO(10000,7160)],[new NeedVO(10000,47200),new NeedVO(10005,388)],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2314,79470,87500,[new BaseRewardVO(10000,7170)],[new NeedVO(10000,47300),new NeedVO(10006,389)],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2315,79616,87600,[new BaseRewardVO(10000,7180)],[new NeedVO(10000,47400),new NeedVO(10007,390)],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2316,79762,87700,[new BaseRewardVO(10000,7190)],[new NeedVO(10000,47500),new NeedVO(10005,391)],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(2317,79908,87800,[new BaseRewardVO(10000,7200),new BaseDropVO(400,new BaseRewardVO(10911,1))],[new NeedVO(10000,47600),new NeedVO(10006,392)],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2318,80054,87900,[new BaseRewardVO(10000,7210)],[new NeedVO(10000,47700),new NeedVO(10007,393)],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(2319,80200,88000,[new BaseRewardVO(10000,7220)],[new NeedVO(10000,47800),new NeedVO(10005,394)],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2320,80346,88100,[new BaseRewardVO(10000,7230)],[new NeedVO(10000,47900),new NeedVO(10006,395)],"scene/yanz114.json");
         this._battles[this._battles.length] = new MissionBattleVO(2321,80492,88200,[new BaseRewardVO(10000,7240)],[new NeedVO(10000,48000),new NeedVO(10007,396)],"scene/yangz115-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2322,80638,88300,[new BaseRewardVO(10000,7250),new BaseDropVO(400,new BaseRewardVO(10912,1))],[new NeedVO(10000,48100),new NeedVO(10005,397)],"scene/yanz116.json");
         this._battles[this._battles.length] = new MissionBattleVO(2323,80784,88400,[new BaseRewardVO(10000,7260)],[new NeedVO(10000,48200),new NeedVO(10006,398)],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2324,80930,88500,[new BaseRewardVO(10000,7270)],[new NeedVO(10000,48300),new NeedVO(10007,399)],"scene/yanz118.json");
         this._battles[this._battles.length] = new MissionBattleVO(2325,81076,88600,[new BaseRewardVO(10000,7280)],[new NeedVO(10000,48400),new NeedVO(10005,400)],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2326,81222,88700,[new BaseRewardVO(10000,7290)],[new NeedVO(10000,48500),new NeedVO(10006,401)],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2327,81368,88800,[new BaseRewardVO(10000,7300),new BaseDropVO(400,new BaseRewardVO(10913,1))],[new NeedVO(10000,48600),new NeedVO(10007,402)],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2328,81514,88900,[new BaseRewardVO(10000,7310)],[new NeedVO(10000,48700),new NeedVO(10005,403)],"scene/DH008-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2329,81660,89000,[new BaseRewardVO(10000,7320)],[new NeedVO(10000,48800),new NeedVO(10006,404)],"scene/DH009-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2330,81806,89100,[new BaseRewardVO(10000,7330)],[new NeedVO(10000,48900),new NeedVO(10007,405)],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2331,81952,89200,[new BaseRewardVO(10000,7340)],[new NeedVO(10000,49000),new NeedVO(10005,406)],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2332,82098,89300,[new BaseRewardVO(10000,7350),new BaseDropVO(150,new BaseRewardVO(10917,1))],[new NeedVO(10000,49100),new NeedVO(10006,407)],"scene/DH022-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2333,82244,89400,[new BaseRewardVO(10000,7360)],[new NeedVO(10000,49200),new NeedVO(10007,408)],"scene/DH023-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2334,82390,89500,[new BaseRewardVO(10000,7370)],[new NeedVO(10000,49300),new NeedVO(10005,409)],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2335,82536,89600,[new BaseRewardVO(10000,7380)],[new NeedVO(10000,49400),new NeedVO(10006,410)],"scene/DH025-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2336,82682,89700,[new BaseRewardVO(10000,7390)],[new NeedVO(10000,49500),new NeedVO(10007,411)],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2337,82828,89800,[new BaseRewardVO(10000,7400),new BaseDropVO(150,new BaseRewardVO(10918,1))],[new NeedVO(10000,49600),new NeedVO(10005,412)],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2338,82974,89900,[new BaseRewardVO(10000,7410)],[new NeedVO(10000,49700),new NeedVO(10006,413)],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2339,83120,90000,[new BaseRewardVO(10000,7420)],[new NeedVO(10000,49800),new NeedVO(10007,414)],"scene/yanz101.json");
         this._battles[this._battles.length] = new MissionBattleVO(2340,83266,90100,[new BaseRewardVO(10000,7430)],[new NeedVO(10000,49900),new NeedVO(10005,415)],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2341,83412,90200,[new BaseRewardVO(10000,7440)],[new NeedVO(10000,50000),new NeedVO(10006,416)],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(2342,83558,90300,[new BaseRewardVO(10000,7450),new BaseDropVO(150,new BaseRewardVO(10919,1))],[new NeedVO(10000,50100),new NeedVO(10007,417)],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2343,83704,90400,[new BaseRewardVO(10000,7460)],[new NeedVO(10000,50200),new NeedVO(10005,418)],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2344,83850,90500,[new BaseRewardVO(10000,7470)],[new NeedVO(10000,50300),new NeedVO(10006,419)],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2345,83996,90600,[new BaseRewardVO(10000,7480)],[new NeedVO(10000,50400),new NeedVO(10007,420)],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2346,84142,90700,[new BaseRewardVO(10000,7490)],[new NeedVO(10000,50500),new NeedVO(10005,421)],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2347,84288,90800,[new BaseRewardVO(10000,7500),new BaseDropVO(150,new BaseRewardVO(10927,1))],[new NeedVO(10000,50600),new NeedVO(10006,422)],"scene/DH035-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2348,84434,90900,[new BaseRewardVO(10000,7510)],[new NeedVO(10000,50700),new NeedVO(10007,423)],"scene/DH036-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2349,84580,91000,[new BaseRewardVO(10000,7520)],[new NeedVO(10000,50800),new NeedVO(10005,424)],"scene/DH037-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2350,84726,91100,[new BaseRewardVO(10000,7530)],[new NeedVO(10000,50900),new NeedVO(10006,425)],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2351,84872,91200,[new BaseRewardVO(10000,7540)],[new NeedVO(10000,51000),new NeedVO(10007,426)],"scene/DH011-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2352,85018,91300,[new BaseRewardVO(10000,7550),new BaseDropVO(150,new BaseRewardVO(10928,1))],[new NeedVO(10000,51100),new NeedVO(10005,427)],"scene/DH022-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2353,85164,91400,[new BaseRewardVO(10000,7560)],[new NeedVO(10000,51200),new NeedVO(10006,428)],"scene/DH012-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2354,85310,91500,[new BaseRewardVO(10000,7570)],[new NeedVO(10000,51300),new NeedVO(10007,429)],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2355,85456,91600,[new BaseRewardVO(10000,7580)],[new NeedVO(10000,51400),new NeedVO(10005,430)],"scene/DH013-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2356,85602,91700,[new BaseRewardVO(10000,7590)],[new NeedVO(10000,51500),new NeedVO(10006,431)],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2357,85748,91800,[new BaseRewardVO(10000,7600),new BaseDropVO(150,new BaseRewardVO(10929,1))],[new NeedVO(10000,51600),new NeedVO(10007,432)],"scene/DH014-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2358,85894,91900,[new BaseRewardVO(10000,7610)],[new NeedVO(10000,51700),new NeedVO(10005,433)],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2359,86040,92000,[new BaseRewardVO(10000,7620)],[new NeedVO(10000,51800),new NeedVO(10006,434)],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2360,86186,92100,[new BaseRewardVO(10000,7630)],[new NeedVO(10000,51900),new NeedVO(10007,435)],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2361,86332,92200,[new BaseRewardVO(10000,7640)],[new NeedVO(10000,52000),new NeedVO(10005,436)],"scene/DH029-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2362,86478,92300,[new BaseRewardVO(10000,7650),new BaseDropVO(200,new BaseRewardVO(10943,1))],[new NeedVO(10000,52100),new NeedVO(10006,437)],"scene/DH030-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2363,86624,92400,[new BaseRewardVO(10000,7660)],[new NeedVO(10000,52200),new NeedVO(10007,438)],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2364,86770,92500,[new BaseRewardVO(10000,7670)],[new NeedVO(10000,52300),new NeedVO(10005,439)],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2365,86916,92600,[new BaseRewardVO(10000,7680)],[new NeedVO(10000,52400),new NeedVO(10006,440)],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2366,87062,92700,[new BaseRewardVO(10000,7690)],[new NeedVO(10000,52500),new NeedVO(10007,441)],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2367,87208,92800,[new BaseRewardVO(10000,7700),new BaseDropVO(200,new BaseRewardVO(10944,1))],[new NeedVO(10000,52600),new NeedVO(10005,442)],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2368,87354,92900,[new BaseRewardVO(10000,7710)],[new NeedVO(10000,52700),new NeedVO(10006,443)],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2369,87500,93000,[new BaseRewardVO(10000,7720)],[new NeedVO(10000,52800),new NeedVO(10007,444)],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2370,87646,93100,[new BaseRewardVO(10000,7730)],[new NeedVO(10000,52900),new NeedVO(10005,445)],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(2371,87792,93200,[new BaseRewardVO(10000,7740)],[new NeedVO(10000,53000),new NeedVO(10006,446)],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2372,87938,93300,[new BaseRewardVO(10000,7750),new BaseDropVO(200,new BaseRewardVO(10945,1))],[new NeedVO(10000,53100),new NeedVO(10007,447)],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(2373,88084,93400,[new BaseRewardVO(10000,7760)],[new NeedVO(10000,53200),new NeedVO(10005,448)],"scene/DH007-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2374,88230,93500,[new BaseRewardVO(10000,7770)],[new NeedVO(10000,53300),new NeedVO(10006,449)],"scene/DH008-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2375,88376,93600,[new BaseRewardVO(10000,7780)],[new NeedVO(10000,53400),new NeedVO(10007,450)],"scene/DH009-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2376,88522,93700,[new BaseRewardVO(10000,7790)],[new NeedVO(10000,53500),new NeedVO(10005,451)],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2377,88668,93800,[new BaseRewardVO(10000,7800),new BaseDropVO(200,new BaseRewardVO(10961,1))],[new NeedVO(10000,53600),new NeedVO(10006,452)],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2378,88814,93900,[new BaseRewardVO(10000,7810)],[new NeedVO(10000,53700),new NeedVO(10007,453)],"scene/yangz101-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2379,88960,94000,[new BaseRewardVO(10000,7820)],[new NeedVO(10000,53800),new NeedVO(10005,454)],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2380,89106,94100,[new BaseRewardVO(10000,7830)],[new NeedVO(10000,53900),new NeedVO(10006,455)],"scene/yangz103-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2381,89252,94200,[new BaseRewardVO(10000,7840)],[new NeedVO(10000,54000),new NeedVO(10007,456)],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2382,89398,94300,[new BaseRewardVO(10000,7850),new BaseDropVO(200,new BaseRewardVO(10962,1))],[new NeedVO(10000,54100),new NeedVO(10005,457)],"scene/yangz105-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2383,89544,94400,[new BaseRewardVO(10000,7860)],[new NeedVO(10000,54200),new NeedVO(10006,458)],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(2384,89690,94500,[new BaseRewardVO(10000,7870)],[new NeedVO(10000,54300),new NeedVO(10007,459)],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2385,89836,94600,[new BaseRewardVO(10000,7880)],[new NeedVO(10000,54400),new NeedVO(10005,460)],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2386,89982,94700,[new BaseRewardVO(10000,7890)],[new NeedVO(10000,54500),new NeedVO(10006,461)],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2387,90128,94800,[new BaseRewardVO(10000,7900),new BaseDropVO(200,new BaseRewardVO(10963,1))],[new NeedVO(10000,54600),new NeedVO(10007,462)],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(2388,90274,94900,[new BaseRewardVO(10000,7910)],[new NeedVO(10000,54700),new NeedVO(10005,463)],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2389,90420,95000,[new BaseRewardVO(10000,7920)],[new NeedVO(10000,54800),new NeedVO(10006,464)],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(2390,90566,95100,[new BaseRewardVO(10000,7930)],[new NeedVO(10000,54900),new NeedVO(10007,465)],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2391,90712,95200,[new BaseRewardVO(10000,7940)],[new NeedVO(10000,55000),new NeedVO(10005,466)],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2392,90858,95300,[new BaseRewardVO(10000,7950),new BaseDropVO(200,new BaseRewardVO(10965,1))],[new NeedVO(10006,55100),new NeedVO(10012,467)],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2393,91004,95400,[new BaseRewardVO(10000,7960)],[new NeedVO(10000,55200),new NeedVO(10007,468)],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2394,91150,95500,[new BaseRewardVO(10000,7970)],[new NeedVO(10000,55300),new NeedVO(10005,469)],"scene/DH013-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2395,91296,95600,[new BaseRewardVO(10000,7980)],[new NeedVO(10000,55400),new NeedVO(10006,470)],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2396,91442,95700,[new BaseRewardVO(10000,7990)],[new NeedVO(10000,55500),new NeedVO(10007,471)],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2397,91588,95800,[new BaseRewardVO(10000,8000),new BaseDropVO(200,new BaseRewardVO(10966,1))],[new NeedVO(10005,55600),new NeedVO(10005,472)],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2398,91734,95900,[new BaseRewardVO(10000,8010)],[new NeedVO(10000,55700),new NeedVO(10006,473)],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(2399,91880,96000,[new BaseRewardVO(10000,8020)],[new NeedVO(10000,55800),new NeedVO(10007,474)],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2400,92026,96100,[new BaseRewardVO(10000,8030)],[new NeedVO(10000,55900),new NeedVO(10005,475)],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(2401,92172,96200,[new BaseRewardVO(10000,8040)],[new NeedVO(10000,56000),new NeedVO(10006,476)],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2402,92318,96300,[new BaseRewardVO(10000,8050),new BaseDropVO(200,new BaseRewardVO(10967,1))],[new NeedVO(10000,56100),new NeedVO(10007,477)],"scene/yanz114.json");
         this._battles[this._battles.length] = new MissionBattleVO(2403,92464,96400,[new BaseRewardVO(10000,8060)],[new NeedVO(10000,56200),new NeedVO(10005,478)],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2404,92610,96500,[new BaseRewardVO(10000,8070)],[new NeedVO(10000,56300),new NeedVO(10006,479)],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2405,92756,96600,[new BaseRewardVO(10000,8080)],[new NeedVO(10000,56400),new NeedVO(10007,480)],"scene/DH029-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2406,92902,96700,[new BaseRewardVO(10000,8090)],[new NeedVO(10000,56500),new NeedVO(10005,481)],"scene/DH030-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2407,93048,96800,[new BaseRewardVO(10000,8100),new BaseDropVO(200,new BaseRewardVO(10971,1))],[new NeedVO(10000,56600),new NeedVO(10006,482)],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2408,93194,96900,[new BaseRewardVO(10000,8110)],[new NeedVO(10000,56700),new NeedVO(10007,483)],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2409,93340,97000,[new BaseRewardVO(10000,8120)],[new NeedVO(10000,56800),new NeedVO(10005,484)],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2410,93486,97100,[new BaseRewardVO(10000,8130)],[new NeedVO(10000,56900),new NeedVO(10006,485)],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2411,93632,97200,[new BaseRewardVO(10000,8140)],[new NeedVO(10000,57000),new NeedVO(10007,486)],"scene/yanz101.json");
         this._battles[this._battles.length] = new MissionBattleVO(2412,93778,97300,[new BaseRewardVO(10000,8150),new BaseDropVO(200,new BaseRewardVO(10972,1))],[new NeedVO(10000,57100),new NeedVO(10005,487)],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2413,93924,97400,[new BaseRewardVO(10000,8160)],[new NeedVO(10000,57200),new NeedVO(10006,488)],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(2414,94070,97500,[new BaseRewardVO(10000,8170)],[new NeedVO(10000,57300),new NeedVO(10007,489)],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2415,94216,97600,[new BaseRewardVO(10000,8180)],[new NeedVO(10000,57400),new NeedVO(10005,490)],"scene/yanz105.json");
         this._battles[this._battles.length] = new MissionBattleVO(2416,94362,97700,[new BaseRewardVO(10000,8190)],[new NeedVO(10000,57500),new NeedVO(10006,491)],"scene/DH003-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2417,94508,97800,[new BaseRewardVO(10000,8200),new BaseDropVO(200,new BaseRewardVO(10973,1))],[new NeedVO(10000,57600),new NeedVO(10007,492)],"scene/yangz103-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2418,94654,97900,[new BaseRewardVO(10000,8210)],[new NeedVO(10000,57700),new NeedVO(10005,493)],"scene/DH029-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2419,94800,98000,[new BaseRewardVO(10000,8220)],[new NeedVO(10000,57800),new NeedVO(10006,494)],"scene/DH030-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2420,94946,98100,[new BaseRewardVO(10000,8230)],[new NeedVO(10000,57900),new NeedVO(10007,495)],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2421,95092,98200,[new BaseRewardVO(10000,8240)],[new NeedVO(10000,58000),new NeedVO(10005,496)],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2422,95238,98300,[new BaseRewardVO(10000,8250),new BaseDropVO(200,new BaseRewardVO(10976,1))],[new NeedVO(10000,58100),new NeedVO(10006,497)],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2423,95384,98400,[new BaseRewardVO(10000,8260)],[new NeedVO(10000,58200),new NeedVO(10007,498)],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2424,95530,98500,[new BaseRewardVO(10000,8270)],[new NeedVO(10000,58300),new NeedVO(10005,499)],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2425,95676,98600,[new BaseRewardVO(10000,8280)],[new NeedVO(10000,58400),new NeedVO(10006,500)],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2426,95822,98700,[new BaseRewardVO(10000,8290)],[new NeedVO(10000,58500),new NeedVO(10007,501)],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2427,95968,98800,[new BaseRewardVO(10000,8300),new BaseDropVO(200,new BaseRewardVO(10977,1))],[new NeedVO(10000,58600),new NeedVO(10005,502)],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(2428,96114,98900,[new BaseRewardVO(10000,8310)],[new NeedVO(10000,58700),new NeedVO(10006,503)],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2429,96260,99000,[new BaseRewardVO(10000,8320)],[new NeedVO(10000,58800),new NeedVO(10007,504)],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(2430,96406,99100,[new BaseRewardVO(10000,8330)],[new NeedVO(10000,58900),new NeedVO(10005,505)],"scene/yanz101.json");
         this._battles[this._battles.length] = new MissionBattleVO(2431,96552,99200,[new BaseRewardVO(10000,8340)],[new NeedVO(10000,59000),new NeedVO(10006,506)],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2432,96698,99300,[new BaseRewardVO(10000,8350),new BaseDropVO(200,new BaseRewardVO(10978,1))],[new NeedVO(10000,59100),new NeedVO(10007,507)],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(2433,96844,99400,[new BaseRewardVO(10000,8360)],[new NeedVO(10000,59200),new NeedVO(10005,508)],"scene/DH008-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2434,96990,99500,[new BaseRewardVO(10000,8370)],[new NeedVO(10000,59300),new NeedVO(10006,509)],"scene/DH009-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2435,97136,99600,[new BaseRewardVO(10000,8380)],[new NeedVO(10000,59400),new NeedVO(10007,510)],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2436,97282,99700,[new BaseRewardVO(10000,8390)],[new NeedVO(10000,59500),new NeedVO(10005,511)],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2437,97428,99800,[new BaseRewardVO(10000,8400),new BaseDropVO(200,new BaseRewardVO(10990,1))],[new NeedVO(10000,59600),new NeedVO(10006,512)],"scene/yangz101-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2438,97574,99900,[new BaseRewardVO(10000,8410)],[new NeedVO(10000,59700),new NeedVO(10007,513)],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2439,97720,100000,[new BaseRewardVO(10000,8420)],[new NeedVO(10000,59800),new NeedVO(10005,514)],"scene/yangz103-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2440,97866,100100,[new BaseRewardVO(10000,8430)],[new NeedVO(10000,59900),new NeedVO(10006,515)],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2441,98012,100200,[new BaseRewardVO(10000,8440)],[new NeedVO(10000,60000),new NeedVO(10007,516)],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2442,98158,100300,[new BaseRewardVO(10000,8450),new BaseDropVO(200,new BaseRewardVO(10991,1))],[new NeedVO(10000,60100),new NeedVO(10005,517)],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2443,98304,100400,[new BaseRewardVO(10000,8460)],[new NeedVO(10000,60200),new NeedVO(10006,518)],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2444,98450,100500,[new BaseRewardVO(10000,8470)],[new NeedVO(10000,60300),new NeedVO(10007,519)],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2445,98596,100600,[new BaseRewardVO(10000,8480)],[new NeedVO(10000,60400),new NeedVO(10005,520)],"scene/DH035-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2446,98742,100700,[new BaseRewardVO(10000,8490)],[new NeedVO(10000,60500),new NeedVO(10006,521)],"scene/DH036-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2447,98888,100800,[new BaseRewardVO(10000,8500),new BaseDropVO(200,new BaseRewardVO(10992,1))],[new NeedVO(10000,60600),new NeedVO(10007,522)],"scene/DH011-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2448,99034,100900,[new BaseRewardVO(10000,8510)],[new NeedVO(10000,60700),new NeedVO(10005,523)],"scene/yangz105-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2449,99180,101000,[new BaseRewardVO(10000,8520)],[new NeedVO(10000,60800),new NeedVO(10006,524)],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(2450,99326,101100,[new BaseRewardVO(10000,8530)],[new NeedVO(10000,60900),new NeedVO(10007,525)],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2451,99472,101200,[new BaseRewardVO(10000,8540),new BaseDropVO(200,new BaseRewardVO(10994,1))],[new NeedVO(10000,61000),new NeedVO(10005,526)],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2452,99618,101300,[new BaseRewardVO(10000,8550)],[new NeedVO(10000,61100),new NeedVO(10006,527)],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2453,99764,101400,[new BaseRewardVO(10000,8560)],[new NeedVO(10000,61200),new NeedVO(10007,528)],"scene/yanz106.json");
         this._battles[this._battles.length] = new MissionBattleVO(2454,99910,101500,[new BaseRewardVO(10000,8570)],[new NeedVO(10000,61300),new NeedVO(10005,529)],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2455,100056,101600,[new BaseRewardVO(10000,8580),new BaseDropVO(200,new BaseRewardVO(10995,1))],[new NeedVO(10000,61400),new NeedVO(10006,530)],"scene/yanz103.json");
         this._battles[this._battles.length] = new MissionBattleVO(2456,100202,101700,[new BaseRewardVO(10000,8590)],[new NeedVO(10000,61500),new NeedVO(10007,531)],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2457,100348,101800,[new BaseRewardVO(10000,8600)],[new NeedVO(10000,61600),new NeedVO(10005,532)],"scene/DH031-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2458,100494,101900,[new BaseRewardVO(10000,8610)],[new NeedVO(10000,61700),new NeedVO(10006,533)],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2459,100640,102000,[new BaseRewardVO(10000,8620),new BaseDropVO(200,new BaseRewardVO(10996,1))],[new NeedVO(10000,61800),new NeedVO(10007,534)],"scene/DH024-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2460,100786,102100,[new BaseRewardVO(10000,8630)],[new NeedVO(10000,61900),new NeedVO(10007,535)],"scene/DH025-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2461,100932,102200,[new BaseRewardVO(10000,8640)],[new NeedVO(10000,62000),new NeedVO(10005,536)],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2462,101078,102300,[new BaseRewardVO(10000,8650)],[new NeedVO(10000,62100),new NeedVO(10006,537)],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2463,101224,102400,[new BaseRewardVO(10000,8660),new BaseDropVO(200,new BaseRewardVO(10841,1))],[new NeedVO(10000,62200),new NeedVO(10007,538)],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2464,101370,102500,[new BaseRewardVO(10000,8670)],[new NeedVO(10000,62300),new NeedVO(10005,539)],"scene/DH029-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2465,101516,102600,[new BaseRewardVO(10000,8680)],[new NeedVO(10000,62400),new NeedVO(10006,540)],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2466,101662,102700,[new BaseRewardVO(10000,8690)],[new NeedVO(10000,62500),new NeedVO(10007,541)],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2467,101808,102800,[new BaseRewardVO(10000,8700),new BaseDropVO(200,new BaseRewardVO(10842,1))],[new NeedVO(10000,62600),new NeedVO(10005,542)],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2468,101954,102900,[new BaseRewardVO(10000,8710)],[new NeedVO(10000,62700),new NeedVO(10006,543)],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(2469,102100,103000,[new BaseRewardVO(10000,8720)],[new NeedVO(10000,62800),new NeedVO(10007,544)],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2470,102246,103100,[new BaseRewardVO(10000,8730)],[new NeedVO(10000,62900),new NeedVO(10005,545)],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(2471,102392,103200,[new BaseRewardVO(10000,8740),new BaseDropVO(200,new BaseRewardVO(10843,1))],[new NeedVO(10000,63000),new NeedVO(10006,546)],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2472,102538,103300,[new BaseRewardVO(10000,8750)],[new NeedVO(10000,63100),new NeedVO(10007,547)],"scene/DH006-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2473,102684,103400,[new BaseRewardVO(10000,8760)],[new NeedVO(10000,63200),new NeedVO(10005,548)],"scene/DH007-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2474,102830,103500,[new BaseRewardVO(10000,8770)],[new NeedVO(10000,63300),new NeedVO(10006,549)],"scene/DH008-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2475,102976,103600,[new BaseRewardVO(10000,8780),new BaseDropVO(200,new BaseRewardVO(10845,1))],[new NeedVO(10000,63400),new NeedVO(10007,550)],"scene/DH009-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2476,103122,103700,[new BaseRewardVO(10000,8790)],[new NeedVO(10000,63500),new NeedVO(10005,551)],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2477,103268,103800,[new BaseRewardVO(10000,8800)],[new NeedVO(10000,63600),new NeedVO(10006,552)],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2478,103414,103900,[new BaseRewardVO(10000,8810)],[new NeedVO(10000,63700),new NeedVO(10007,553)],"scene/yangz101-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2479,103560,104000,[new BaseRewardVO(10000,8820),new BaseDropVO(200,new BaseRewardVO(10846,1))],[new NeedVO(10000,63800),new NeedVO(10005,554)],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2480,103706,104100,[new BaseRewardVO(10000,8830)],[new NeedVO(10000,63900),new NeedVO(10006,555)],"scene/yangz103-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2481,103852,104200,[new BaseRewardVO(10000,8840)],[new NeedVO(10000,64000),new NeedVO(10007,556)],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2482,103998,104300,[new BaseRewardVO(10000,8850)],[new NeedVO(10000,64100),new NeedVO(10005,557)],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2483,104144,104400,[new BaseRewardVO(10000,8860),new BaseDropVO(200,new BaseRewardVO(10847,1))],[new NeedVO(10000,64200),new NeedVO(10006,558)],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2484,104290,104500,[new BaseRewardVO(10000,8870)],[new NeedVO(10000,64300),new NeedVO(10007,559)],"scene/DH026-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2485,104436,104600,[new BaseRewardVO(10000,8880)],[new NeedVO(10000,64400),new NeedVO(10005,560)],"scene/DH027-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2486,104582,104700,[new BaseRewardVO(10000,8890)],[new NeedVO(10000,64500),new NeedVO(10006,561)],"scene/DH028-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2487,104728,104800,[new BaseRewardVO(10000,8900),new BaseDropVO(200,new BaseRewardVO(10891,1))],[new NeedVO(10000,64600),new NeedVO(10007,562)],"scene/DH029-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2488,104874,104900,[new BaseRewardVO(10000,8910)],[new NeedVO(10000,64700),new NeedVO(10005,563)],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2489,105020,105000,[new BaseRewardVO(10000,8920)],[new NeedVO(10000,64800),new NeedVO(10006,564)],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2490,105166,105100,[new BaseRewardVO(10000,8930)],[new NeedVO(10000,64900),new NeedVO(10007,565)],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2491,105312,105200,[new BaseRewardVO(10000,8940),new BaseDropVO(200,new BaseRewardVO(10892,1))],[new NeedVO(10000,65000),new NeedVO(10005,566)],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(2492,105458,105300,[new BaseRewardVO(10000,8950)],[new NeedVO(10000,65100),new NeedVO(10006,567)],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2493,105604,105400,[new BaseRewardVO(10000,8960)],[new NeedVO(10000,65200),new NeedVO(10007,568)],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(2494,105750,105500,[new BaseRewardVO(10000,8970)],[new NeedVO(10000,65300),new NeedVO(10005,569)],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2495,105896,105600,[new BaseRewardVO(10000,8980),new BaseDropVO(200,new BaseRewardVO(10893,1))],[new NeedVO(10000,65400),new NeedVO(10006,570)],"scene/yanz114.json");
         this._battles[this._battles.length] = new MissionBattleVO(2496,106042,105700,[new BaseRewardVO(10000,8990)],[new NeedVO(10000,65500),new NeedVO(10007,571)],"scene/DH032-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2497,106188,105800,[new BaseRewardVO(10000,9000)],[new NeedVO(10000,65600),new NeedVO(10005,572)],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2498,106334,105900,[new BaseRewardVO(10000,9010)],[new NeedVO(10000,65700),new NeedVO(10006,573)],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2499,106480,106000,[new BaseRewardVO(10000,9020),new BaseDropVO(200,new BaseRewardVO(10895,1))],[new NeedVO(10000,65800),new NeedVO(10007,574)],"scene/yangz107-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2500,106626,106100,[new BaseRewardVO(10000,9030)],[new NeedVO(10000,65900),new NeedVO(10005,575)],"scene/yanz1080.json");
         this._battles[this._battles.length] = new MissionBattleVO(2501,106772,106200,[new BaseRewardVO(10000,9040)],[new NeedVO(10000,66000),new NeedVO(10006,576)],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2502,106918,106300,[new BaseRewardVO(10000,9050)],[new NeedVO(10000,66100),new NeedVO(10007,577)],"scene/yanz110.json");
         this._battles[this._battles.length] = new MissionBattleVO(2503,107064,106400,[new BaseRewardVO(10000,9060),new BaseDropVO(200,new BaseRewardVO(10896,1))],[new NeedVO(10000,66200),new NeedVO(10005,578)],"scene/yangz111-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2504,107210,106500,[new BaseRewardVO(10000,9070)],[new NeedVO(10000,66300),new NeedVO(10006,579)],"scene/yanz112.json");
         this._battles[this._battles.length] = new MissionBattleVO(2505,107356,106600,[new BaseRewardVO(10000,9080)],[new NeedVO(10000,66400),new NeedVO(10007,580)],"scene/DH007-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2506,107502,106700,[new BaseRewardVO(10000,9090)],[new NeedVO(10000,66500),new NeedVO(10005,581)],"scene/DH008-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2507,107648,106800,[new BaseRewardVO(10000,9100),new BaseDropVO(200,new BaseRewardVO(10897,1))],[new NeedVO(10000,66600),new NeedVO(10006,582)],"scene/DH009-480.json");
         this._battles[this._battles.length] = new MissionBattleVO(2508,107794,106900,[new BaseRewardVO(10000,9110)],[new NeedVO(10000,66700),new NeedVO(10007,583)],"scene/DH020-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2509,107940,107000,[new BaseRewardVO(10000,9120)],[new NeedVO(10000,66800),new NeedVO(10005,584)],"scene/DH021-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2510,108086,107100,[new BaseRewardVO(10000,9130)],[new NeedVO(10000,66900),new NeedVO(10006,585)],"scene/yangz101-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2511,108232,107200,[new BaseRewardVO(10000,9140),new BaseDropVO(200,new BaseRewardVO(10836,1))],[new NeedVO(10000,67000),new NeedVO(10007,586)],"scene/yanz102.json");
         this._battles[this._battles.length] = new MissionBattleVO(2512,108378,107300,[new BaseRewardVO(10000,9150)],[new NeedVO(10000,67100),new NeedVO(10005,587)],"scene/yangz103-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2513,108524,107400,[new BaseRewardVO(10000,9160)],[new NeedVO(10000,67200),new NeedVO(10006,588)],"scene/yanz104.json");
         this._battles[this._battles.length] = new MissionBattleVO(2514,108670,107500,[new BaseRewardVO(10000,9170)],[new NeedVO(10000,67300),new NeedVO(10007,589)],"scene/yangz113-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2515,108816,107600,[new BaseRewardVO(10000,9180),new BaseDropVO(200,new BaseRewardVO(10837,1))],[new NeedVO(10000,67400),new NeedVO(10005,590)],"scene/yangz109-180.json");
         this._battles[this._battles.length] = new MissionBattleVO(2516,108962,107700,[new BaseRewardVO(10000,9190)],[new NeedVO(10000,67500),new NeedVO(10006,591)],"scene/DH033-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2517,109108,107800,[new BaseRewardVO(10000,9200)],[new NeedVO(10000,67600),new NeedVO(10007,592)],"scene/DH034-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2518,109254,107900,[new BaseRewardVO(10000,9210)],[new NeedVO(10000,67700),new NeedVO(10005,593)],"scene/DH035-580.json");
         this._battles[this._battles.length] = new MissionBattleVO(2519,109400,108000,[new BaseRewardVO(10000,9220),new BaseDropVO(200,new BaseRewardVO(10838,1))],[new NeedVO(10000,67800),new NeedVO(10006,594)],"scene/DH036-580.json");
         this._stars = new Vector.<MissionStarVO>();
         this._stars[this._stars.length] = new MissionStarVO(1001,[new ReqData(101,null),new ReqData(117,{"num":30}),new ReqData(105,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1002,[new ReqData(116,null),new ReqData(105,null),new ReqData(101,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1003,[new ReqData(101,null),new ReqData(106,{"num":2}),new ReqData(103,{"country":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1004,[new ReqData(101,null),new ReqData(105,null),new ReqData(104,{
            "num":1,
            "dataID":100
         })]);
         this._stars[this._stars.length] = new MissionStarVO(1005,[new ReqData(112,{"num":50}),new ReqData(102,{"time":245}),new ReqData(117,{"num":80})]);
         this._stars[this._stars.length] = new MissionStarVO(1006,[new ReqData(111,{"num":50}),new ReqData(102,{"time":175}),new ReqData(103,{"country":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1007,[new ReqData(116,null),new ReqData(102,{"time":345}),new ReqData(122,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1008,[new ReqData(101,null),new ReqData(106,{"num":2}),new ReqData(117,{"num":60})]);
         this._stars[this._stars.length] = new MissionStarVO(1009,[new ReqData(110,{"num":999}),new ReqData(105,null),new ReqData(111,{"num":50})]);
         this._stars[this._stars.length] = new MissionStarVO(1010,[new ReqData(116,null),new ReqData(102,{"time":310}),new ReqData(103,{"country":0})]);
         this._stars[this._stars.length] = new MissionStarVO(1011,[new ReqData(101,null),new ReqData(109,{"num":999}),new ReqData(105,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1012,[new ReqData(101,null),new ReqData(102,{"time":325}),new ReqData(122,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1013,[new ReqData(101,null),new ReqData(105,null),new ReqData(103,{"country":1})]);
         this._stars[this._stars.length] = new MissionStarVO(1014,[new ReqData(110,{"num":999}),new ReqData(106,{"num":2}),new ReqData(113,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1015,[new ReqData(101,null),new ReqData(102,{"time":345}),new ReqData(103,{"country":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1016,[new ReqData(101,null),new ReqData(105,null),new ReqData(117,{"num":70})]);
         this._stars[this._stars.length] = new MissionStarVO(1017,[new ReqData(101,null),new ReqData(109,{"num":999}),new ReqData(102,{"time":405})]);
         this._stars[this._stars.length] = new MissionStarVO(1018,[new ReqData(101,null),new ReqData(110,{"num":999}),new ReqData(103,{"country":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1019,[new ReqData(101,null),new ReqData(106,{"num":2}),new ReqData(103,{"country":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1020,[new ReqData(101,null),new ReqData(123,null),new ReqData(117,{"num":80})]);
         this._stars[this._stars.length] = new MissionStarVO(1021,[new ReqData(102,{"time":340}),new ReqData(119,null),new ReqData(106,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1022,[new ReqData(107,null),new ReqData(122,{"num":3}),new ReqData(118,{"armyID":102})]);
         this._stars[this._stars.length] = new MissionStarVO(1023,[new ReqData(101,null),new ReqData(106,{"num":3}),new ReqData(113,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1024,[new ReqData(102,{"time":350}),new ReqData(123,null),new ReqData(118,{"armyID":109})]);
         this._stars[this._stars.length] = new MissionStarVO(1025,[new ReqData(107,null),new ReqData(123,null),new ReqData(106,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1026,[new ReqData(102,{"time":320}),new ReqData(117,{"num":80}),new ReqData(106,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1027,[new ReqData(109,{"num":599}),new ReqData(118,{"armyID":102}),new ReqData(117,{"num":70})]);
         this._stars[this._stars.length] = new MissionStarVO(1028,[new ReqData(107,null),new ReqData(122,{"num":3}),new ReqData(118,{"armyID":109})]);
         this._stars[this._stars.length] = new MissionStarVO(1029,[new ReqData(102,{"time":310}),new ReqData(123,null),new ReqData(111,{"num":50})]);
         this._stars[this._stars.length] = new MissionStarVO(1030,[new ReqData(108,{"num":2}),new ReqData(119,null),new ReqData(106,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1031,[new ReqData(107,null),new ReqData(103,{"country":2}),new ReqData(117,{"num":70})]);
         this._stars[this._stars.length] = new MissionStarVO(1032,[new ReqData(106,{"num":3}),new ReqData(123,null),new ReqData(118,{"armyID":102})]);
         this._stars[this._stars.length] = new MissionStarVO(1033,[new ReqData(110,{"num":499}),new ReqData(117,{"num":70}),new ReqData(106,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1034,[new ReqData(102,{"time":310}),new ReqData(122,{"num":3}),new ReqData(117,{"num":70})]);
         this._stars[this._stars.length] = new MissionStarVO(1035,[new ReqData(109,{"num":399}),new ReqData(103,{"country":3}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1036,[new ReqData(108,{"num":2}),new ReqData(120,null),new ReqData(123,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1037,[new ReqData(110,{"num":699}),new ReqData(103,{"country":2}),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1038,[new ReqData(101,null),new ReqData(107,null),new ReqData(106,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1039,[new ReqData(118,{"armyID":104}),new ReqData(103,{"country":0}),new ReqData(102,{"time":280})]);
         this._stars[this._stars.length] = new MissionStarVO(1040,[new ReqData(101,null),new ReqData(123,null),new ReqData(117,{"num":70})]);
         this._stars[this._stars.length] = new MissionStarVO(1041,[new ReqData(107,null),new ReqData(103,{"country":1}),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1042,[new ReqData(118,{"armyID":107}),new ReqData(107,null),new ReqData(123,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1043,[new ReqData(107,null),new ReqData(121,{"country":2}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1044,[new ReqData(101,null),new ReqData(107,null),new ReqData(120,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1045,[new ReqData(118,{"armyID":101}),new ReqData(103,{"country":3}),new ReqData(117,{"num":70})]);
         this._stars[this._stars.length] = new MissionStarVO(1046,[new ReqData(101,null),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1047,[new ReqData(102,{"time":340}),new ReqData(121,{"country":1}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1048,[new ReqData(118,{"armyID":104}),new ReqData(107,null),new ReqData(110,{"num":888})]);
         this._stars[this._stars.length] = new MissionStarVO(1049,[new ReqData(107,null),new ReqData(103,{"country":1}),new ReqData(102,{"time":285})]);
         this._stars[this._stars.length] = new MissionStarVO(1050,[new ReqData(101,null),new ReqData(107,null),new ReqData(115,{"num":1500})]);
         this._stars[this._stars.length] = new MissionStarVO(1051,[new ReqData(102,{"time":380}),new ReqData(121,{"country":0}),new ReqData(117,{"num":70})]);
         this._stars[this._stars.length] = new MissionStarVO(1052,[new ReqData(119,null),new ReqData(107,null),new ReqData(117,{"num":80})]);
         this._stars[this._stars.length] = new MissionStarVO(1053,[new ReqData(101,null),new ReqData(103,{"country":3}),new ReqData(106,{"num":4})]);
         this._stars[this._stars.length] = new MissionStarVO(1054,[new ReqData(119,null),new ReqData(107,null),new ReqData(123,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1055,[new ReqData(107,null),new ReqData(123,null),new ReqData(117,{"num":80})]);
         this._stars[this._stars.length] = new MissionStarVO(1056,[new ReqData(102,{"time":420}),new ReqData(107,null),new ReqData(103,{"country":0})]);
         this._stars[this._stars.length] = new MissionStarVO(1057,[new ReqData(118,{"armyID":105}),new ReqData(103,{"country":3}),new ReqData(102,{"time":390})]);
         this._stars[this._stars.length] = new MissionStarVO(1058,[new ReqData(101,null),new ReqData(119,null),new ReqData(105,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1059,[new ReqData(103,{"country":0}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1060,[new ReqData(112,{"num":50}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1061,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1062,[new ReqData(102,{"time":280}),new ReqData(119,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1063,[new ReqData(103,{"country":1}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1064,[new ReqData(107,null),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1065,[new ReqData(117,{"num":80}),new ReqData(107,null),new ReqData(110,{"num":200})]);
         this._stars[this._stars.length] = new MissionStarVO(1066,[new ReqData(102,{"time":280}),new ReqData(119,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1067,[new ReqData(103,{"country":2}),new ReqData(123,null),new ReqData(101,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1068,[new ReqData(101,null),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1069,[new ReqData(117,{"num":80}),new ReqData(107,null),new ReqData(120,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1070,[new ReqData(123,null),new ReqData(119,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1071,[new ReqData(103,{"country":3}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1072,[new ReqData(102,{"time":320}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1073,[new ReqData(117,{"num":80}),new ReqData(107,null),new ReqData(110,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(1074,[new ReqData(123,null),new ReqData(119,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1075,[new ReqData(103,{"country":0}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1076,[new ReqData(112,{"num":50}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1077,[new ReqData(123,null),new ReqData(107,null),new ReqData(111,{"num":50})]);
         this._stars[this._stars.length] = new MissionStarVO(1078,[new ReqData(112,{"num":50}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1079,[new ReqData(102,{"time":280}),new ReqData(119,null),new ReqData(105,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1080,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1081,[new ReqData(101,null),new ReqData(119,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1082,[new ReqData(112,{"num":50}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1083,[new ReqData(103,{"country":2}),new ReqData(123,null),new ReqData(101,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1084,[new ReqData(123,null),new ReqData(107,null),new ReqData(111,{"num":50})]);
         this._stars[this._stars.length] = new MissionStarVO(1085,[new ReqData(102,{"time":320}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1086,[new ReqData(101,null),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1087,[new ReqData(117,{"num":80}),new ReqData(107,null),new ReqData(120,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1088,[new ReqData(123,null),new ReqData(119,null),new ReqData(105,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1089,[new ReqData(107,null),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1090,[new ReqData(103,{"country":0}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1091,[new ReqData(117,{"num":80}),new ReqData(107,null),new ReqData(110,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(1092,[new ReqData(103,{"country":0}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1093,[new ReqData(102,{"time":280}),new ReqData(119,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1094,[new ReqData(123,null),new ReqData(119,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1095,[new ReqData(117,{"num":80}),new ReqData(107,null),new ReqData(110,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(1096,[new ReqData(103,{"country":3}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1097,[new ReqData(103,{"country":1}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1098,[new ReqData(117,{"num":90}),new ReqData(107,null),new ReqData(110,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(1099,[new ReqData(112,{"num":50}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1100,[new ReqData(123,null),new ReqData(119,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1101,[new ReqData(101,null),new ReqData(119,null),new ReqData(105,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1102,[new ReqData(103,{"country":1}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1103,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1104,[new ReqData(103,{"country":0}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1105,[new ReqData(101,null),new ReqData(119,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1106,[new ReqData(117,{"num":90}),new ReqData(107,null),new ReqData(110,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(1107,[new ReqData(123,null),new ReqData(119,null),new ReqData(105,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1108,[new ReqData(107,null),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1109,[new ReqData(112,{"num":50}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1110,[new ReqData(101,null),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1111,[new ReqData(103,{"country":2}),new ReqData(123,null),new ReqData(101,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1112,[new ReqData(123,null),new ReqData(107,null),new ReqData(111,{"num":50})]);
         this._stars[this._stars.length] = new MissionStarVO(1113,[new ReqData(101,null),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1114,[new ReqData(102,{"time":320}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1115,[new ReqData(101,null),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1116,[new ReqData(102,{"time":280}),new ReqData(119,null),new ReqData(105,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1117,[new ReqData(101,null),new ReqData(107,null),new ReqData(120,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1118,[new ReqData(102,{"time":280}),new ReqData(119,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1119,[new ReqData(103,{"country":0}),new ReqData(111,{"num":50}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1120,[new ReqData(117,{"num":95}),new ReqData(107,null),new ReqData(120,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1121,[new ReqData(101,null),new ReqData(119,null),new ReqData(105,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1122,[new ReqData(117,{"num":95}),new ReqData(107,null),new ReqData(110,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(1123,[new ReqData(123,null),new ReqData(119,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1124,[new ReqData(103,{"country":3}),new ReqData(112,{"num":20}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1125,[new ReqData(123,null),new ReqData(107,null),new ReqData(111,{"num":30})]);
         this._stars[this._stars.length] = new MissionStarVO(1126,[new ReqData(107,null),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1127,[new ReqData(101,null),new ReqData(112,{"num":50}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1128,[new ReqData(112,{"num":50}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1129,[new ReqData(101,null),new ReqData(119,null),new ReqData(105,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1130,[new ReqData(103,{"country":1}),new ReqData(101,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1131,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1132,[new ReqData(102,{"time":320}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1133,[new ReqData(102,{"time":280}),new ReqData(119,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1134,[new ReqData(112,{"num":50}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1135,[new ReqData(111,{"num":50}),new ReqData(112,{"num":9}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1136,[new ReqData(103,{"country":3}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1137,[new ReqData(117,{"num":95}),new ReqData(103,{"country":3}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1138,[new ReqData(123,null),new ReqData(119,null),new ReqData(105,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1139,[new ReqData(107,null),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1140,[new ReqData(117,{"num":100}),new ReqData(107,null),new ReqData(110,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(1141,[new ReqData(112,{"num":50}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1142,[new ReqData(103,{"country":2}),new ReqData(123,null),new ReqData(101,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1143,[new ReqData(123,null),new ReqData(107,null),new ReqData(111,{"num":55})]);
         this._stars[this._stars.length] = new MissionStarVO(1144,[new ReqData(117,{"num":100}),new ReqData(107,null),new ReqData(110,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(1145,[new ReqData(123,null),new ReqData(119,null),new ReqData(105,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1146,[new ReqData(103,{"country":0}),new ReqData(112,{"num":50}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1147,[new ReqData(101,null),new ReqData(106,{"num":3}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1148,[new ReqData(111,{"num":50}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1149,[new ReqData(117,{"num":100}),new ReqData(107,null),new ReqData(110,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(1150,[new ReqData(102,{"time":320}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1151,[new ReqData(101,null),new ReqData(119,null),new ReqData(105,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1152,[new ReqData(102,{"time":380}),new ReqData(119,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1153,[new ReqData(101,null),new ReqData(119,null),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1154,[new ReqData(103,{"country":1}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1155,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1156,[new ReqData(112,{"num":50}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1157,[new ReqData(101,null),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1158,[new ReqData(102,{"time":330}),new ReqData(119,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1159,[new ReqData(117,{"num":80}),new ReqData(107,null),new ReqData(120,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1160,[new ReqData(101,null),new ReqData(119,null),new ReqData(121,{"country":0})]);
         this._stars[this._stars.length] = new MissionStarVO(1161,[new ReqData(117,{"num":85}),new ReqData(107,null),new ReqData(110,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(1162,[new ReqData(107,null),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1163,[new ReqData(101,null),new ReqData(112,{"num":50}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1164,[new ReqData(117,{"num":90}),new ReqData(103,{"country":3}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1165,[new ReqData(103,{"country":0}),new ReqData(111,{"num":30}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1166,[new ReqData(111,{"num":50}),new ReqData(110,{"num":599}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1167,[new ReqData(103,{"country":1}),new ReqData(101,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1168,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1169,[new ReqData(103,{"country":3}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1170,[new ReqData(101,null),new ReqData(119,null),new ReqData(102,{"time":335})]);
         this._stars[this._stars.length] = new MissionStarVO(1171,[new ReqData(123,null),new ReqData(119,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1172,[new ReqData(103,{"country":3}),new ReqData(112,{"num":20}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1173,[new ReqData(117,{"num":95}),new ReqData(107,null),new ReqData(111,{"num":30})]);
         this._stars[this._stars.length] = new MissionStarVO(1174,[new ReqData(102,{"time":340}),new ReqData(119,null),new ReqData(110,{"num":400})]);
         this._stars[this._stars.length] = new MissionStarVO(1175,[new ReqData(112,{"num":50}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1176,[new ReqData(101,null),new ReqData(119,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1177,[new ReqData(102,{"time":350}),new ReqData(120,null),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1178,[new ReqData(102,{"time":340}),new ReqData(120,null),new ReqData(109,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(1179,[new ReqData(117,{"num":80}),new ReqData(108,{"num":2}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1180,[new ReqData(101,null),new ReqData(107,null),new ReqData(121,{"country":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1181,[new ReqData(112,{"num":3}),new ReqData(118,{"armyID":110}),new ReqData(121,{"country":1})]);
         this._stars[this._stars.length] = new MissionStarVO(1182,[new ReqData(103,{"country":0}),new ReqData(101,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1183,[new ReqData(123,null),new ReqData(121,{"country":1}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1184,[new ReqData(101,null),new ReqData(119,null),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1185,[new ReqData(102,{"time":330}),new ReqData(120,null),new ReqData(106,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1186,[new ReqData(101,null),new ReqData(119,null),new ReqData(102,{"time":310})]);
         this._stars[this._stars.length] = new MissionStarVO(1187,[new ReqData(121,{"country":2}),new ReqData(115,{"num":3500}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1188,[new ReqData(117,{"num":85}),new ReqData(107,null),new ReqData(110,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(1189,[new ReqData(103,{"country":0}),new ReqData(111,{"num":10}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1190,[new ReqData(117,{"num":90}),new ReqData(103,{"country":3}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1191,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1192,[new ReqData(108,{"num":3}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1193,[new ReqData(103,{"country":2}),new ReqData(123,null),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1194,[new ReqData(118,{"armyID":105}),new ReqData(112,{"num":6}),new ReqData(102,{"time":330})]);
         this._stars[this._stars.length] = new MissionStarVO(1195,[new ReqData(101,null),new ReqData(112,{"num":4}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1196,[new ReqData(102,{"time":350}),new ReqData(120,null),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1197,[new ReqData(117,{"num":75}),new ReqData(118,{"armyID":102}),new ReqData(111,{"num":5})]);
         this._stars[this._stars.length] = new MissionStarVO(1198,[new ReqData(121,{"country":0}),new ReqData(120,null),new ReqData(109,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(1199,[new ReqData(121,{"country":1}),new ReqData(112,{"num":3}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1200,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":107}),new ReqData(112,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(1201,[new ReqData(117,{"num":90}),new ReqData(107,null),new ReqData(110,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(1202,[new ReqData(117,{"num":90}),new ReqData(103,{"country":1}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1203,[new ReqData(121,{"country":3}),new ReqData(101,null),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1204,[new ReqData(102,{"time":330}),new ReqData(120,null),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1205,[new ReqData(123,null),new ReqData(121,{"country":2}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1206,[new ReqData(103,{"country":1}),new ReqData(111,{"num":10}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1207,[new ReqData(103,{"country":1}),new ReqData(101,null),new ReqData(118,{"armyID":108})]);
         this._stars[this._stars.length] = new MissionStarVO(1208,[new ReqData(121,{"country":0}),new ReqData(120,null),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1209,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1210,[new ReqData(103,{"country":1}),new ReqData(118,{"armyID":101}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1211,[new ReqData(112,{"num":3}),new ReqData(118,{"armyID":110}),new ReqData(121,{"country":0})]);
         this._stars[this._stars.length] = new MissionStarVO(1212,[new ReqData(117,{"num":110}),new ReqData(118,{"armyID":102}),new ReqData(111,{"num":5})]);
         this._stars[this._stars.length] = new MissionStarVO(1213,[new ReqData(121,{"country":2}),new ReqData(115,{"num":3200}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1214,[new ReqData(103,{"country":0}),new ReqData(101,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1215,[new ReqData(102,{"time":360}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1216,[new ReqData(117,{"num":90}),new ReqData(108,{"num":3}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1217,[new ReqData(101,null),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1218,[new ReqData(121,{"country":0}),new ReqData(120,null),new ReqData(106,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1219,[new ReqData(102,{"time":260}),new ReqData(118,{"armyID":109}),new ReqData(109,{"num":333})]);
         this._stars[this._stars.length] = new MissionStarVO(1220,[new ReqData(117,{"num":90}),new ReqData(103,{"country":1}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1221,[new ReqData(117,{"num":90}),new ReqData(118,{"armyID":102}),new ReqData(111,{"num":5})]);
         this._stars[this._stars.length] = new MissionStarVO(1222,[new ReqData(103,{"country":1}),new ReqData(117,{"num":115}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1223,[new ReqData(103,{"country":2}),new ReqData(111,{"num":10}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1224,[new ReqData(103,{"country":0}),new ReqData(118,{"armyID":105}),new ReqData(106,{"num":4})]);
         this._stars[this._stars.length] = new MissionStarVO(1225,[new ReqData(103,{"country":1}),new ReqData(107,null),new ReqData(118,{"armyID":108})]);
         this._stars[this._stars.length] = new MissionStarVO(1226,[new ReqData(103,{"country":1}),new ReqData(118,{"armyID":101}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1227,[new ReqData(121,{"country":2}),new ReqData(115,{"num":3200}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1228,[new ReqData(118,{"armyID":107}),new ReqData(101,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1229,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":104}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1230,[new ReqData(112,{"num":2}),new ReqData(118,{"armyID":110}),new ReqData(121,{"country":0})]);
         this._stars[this._stars.length] = new MissionStarVO(1231,[new ReqData(117,{"num":90}),new ReqData(108,{"num":3}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1232,[new ReqData(123,null),new ReqData(121,{"country":2}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1233,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":105}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1234,[new ReqData(102,{"time":330}),new ReqData(118,{"armyID":108}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1235,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1236,[new ReqData(121,{"country":1}),new ReqData(118,{"armyID":102}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1237,[new ReqData(121,{"country":0}),new ReqData(120,null),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1238,[new ReqData(121,{"country":0}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1239,[new ReqData(118,{"armyID":109}),new ReqData(121,{"country":2}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1240,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":105}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1241,[new ReqData(117,{"num":90}),new ReqData(118,{"armyID":106}),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1242,[new ReqData(103,{"country":2}),new ReqData(111,{"num":2}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1243,[new ReqData(121,{"country":1}),new ReqData(118,{"armyID":105}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1244,[new ReqData(121,{"country":0}),new ReqData(120,null),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1245,[new ReqData(117,{"num":110}),new ReqData(103,{"country":3}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1246,[new ReqData(117,{"num":100}),new ReqData(108,{"num":3}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1247,[new ReqData(102,{"time":330}),new ReqData(118,{"armyID":108}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1248,[new ReqData(103,{"country":1}),new ReqData(118,{"armyID":103}),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1249,[new ReqData(103,{"country":1}),new ReqData(107,null),new ReqData(118,{"armyID":108})]);
         this._stars[this._stars.length] = new MissionStarVO(1250,[new ReqData(118,{"armyID":106}),new ReqData(101,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1251,[new ReqData(103,{"country":0}),new ReqData(118,{"armyID":112}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1252,[new ReqData(121,{"country":2}),new ReqData(115,{"num":2200}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(1253,[new ReqData(112,{"num":2}),new ReqData(118,{"armyID":107}),new ReqData(121,{"country":0})]);
         this._stars[this._stars.length] = new MissionStarVO(1254,[new ReqData(121,{"country":1}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(1255,[new ReqData(102,{"time":350}),new ReqData(118,{"armyID":103}),new ReqData(109,{"num":222})]);
         this._stars[this._stars.length] = new MissionStarVO(1256,[new ReqData(103,{"country":0}),new ReqData(118,{"armyID":105}),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(1257,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":104}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2001,[new ReqData(121,{"country":0}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2002,[new ReqData(102,{"time":350}),new ReqData(118,{"armyID":103}),new ReqData(109,{"num":222})]);
         this._stars[this._stars.length] = new MissionStarVO(2003,[new ReqData(118,{"armyID":109}),new ReqData(121,{"country":2}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2004,[new ReqData(121,{"country":1}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2005,[new ReqData(117,{"num":110}),new ReqData(103,{"country":3}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2006,[new ReqData(117,{"num":100}),new ReqData(108,{"num":3}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2007,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":105}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2008,[new ReqData(112,{"num":2}),new ReqData(118,{"armyID":107}),new ReqData(121,{"country":0})]);
         this._stars[this._stars.length] = new MissionStarVO(2009,[new ReqData(121,{"country":2}),new ReqData(115,{"num":2200}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2010,[new ReqData(117,{"num":90}),new ReqData(118,{"armyID":106}),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2011,[new ReqData(103,{"country":0}),new ReqData(118,{"armyID":112}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2012,[new ReqData(103,{"country":2}),new ReqData(111,{"num":2}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2013,[new ReqData(118,{"armyID":106}),new ReqData(101,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2014,[new ReqData(103,{"country":1}),new ReqData(107,null),new ReqData(118,{"armyID":108})]);
         this._stars[this._stars.length] = new MissionStarVO(2015,[new ReqData(121,{"country":1}),new ReqData(118,{"armyID":105}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2016,[new ReqData(103,{"country":1}),new ReqData(118,{"armyID":103}),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2017,[new ReqData(121,{"country":0}),new ReqData(120,null),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2018,[new ReqData(102,{"time":330}),new ReqData(118,{"armyID":108}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2019,[new ReqData(117,{"num":100}),new ReqData(108,{"num":3}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2020,[new ReqData(103,{"country":2}),new ReqData(107,null),new ReqData(118,{"armyID":105})]);
         this._stars[this._stars.length] = new MissionStarVO(2021,[new ReqData(112,{"num":2}),new ReqData(118,{"armyID":110}),new ReqData(121,{"country":0})]);
         this._stars[this._stars.length] = new MissionStarVO(2022,[new ReqData(103,{"country":1}),new ReqData(118,{"armyID":108}),new ReqData(106,{"num":4})]);
         this._stars[this._stars.length] = new MissionStarVO(2023,[new ReqData(121,{"country":0}),new ReqData(115,{"num":3600}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2024,[new ReqData(117,{"num":110}),new ReqData(108,{"num":2}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2025,[new ReqData(121,{"country":1}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2026,[new ReqData(121,{"country":2}),new ReqData(120,null),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2027,[new ReqData(103,{"country":1}),new ReqData(118,{"armyID":101}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2028,[new ReqData(102,{"time":320}),new ReqData(118,{"armyID":109}),new ReqData(109,{"num":99})]);
         this._stars[this._stars.length] = new MissionStarVO(2029,[new ReqData(121,{"country":1}),new ReqData(118,{"armyID":101}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2030,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":106}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2031,[new ReqData(117,{"num":110}),new ReqData(103,{"country":1}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2032,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2033,[new ReqData(117,{"num":110}),new ReqData(118,{"armyID":102}),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2034,[new ReqData(102,{"time":320}),new ReqData(118,{"armyID":108}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2035,[new ReqData(118,{"armyID":107}),new ReqData(115,{"num":3600}),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2036,[new ReqData(103,{"country":1}),new ReqData(117,{"num":115}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2037,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":105}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2038,[new ReqData(101,null),new ReqData(111,{"num":3}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2039,[new ReqData(103,{"country":1}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2040,[new ReqData(117,{"num":100}),new ReqData(107,null),new ReqData(110,{"num":800})]);
         this._stars[this._stars.length] = new MissionStarVO(2041,[new ReqData(111,{"num":3}),new ReqData(118,{"armyID":108}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2042,[new ReqData(103,{"country":2}),new ReqData(123,null),new ReqData(118,{"armyID":105})]);
         this._stars[this._stars.length] = new MissionStarVO(2043,[new ReqData(101,null),new ReqData(119,null),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2044,[new ReqData(123,null),new ReqData(119,null),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2045,[new ReqData(103,{"country":0}),new ReqData(112,{"num":4}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2046,[new ReqData(102,{"time":380}),new ReqData(119,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2047,[new ReqData(101,null),new ReqData(118,{"armyID":103}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2048,[new ReqData(112,{"num":2}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2049,[new ReqData(118,{"armyID":101}),new ReqData(119,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2050,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2051,[new ReqData(117,{"num":100}),new ReqData(107,null),new ReqData(110,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(2052,[new ReqData(112,{"num":3}),new ReqData(120,null),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2053,[new ReqData(102,{"time":320}),new ReqData(120,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2054,[new ReqData(123,null),new ReqData(107,null),new ReqData(111,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2055,[new ReqData(123,null),new ReqData(119,null),new ReqData(105,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2056,[new ReqData(101,null),new ReqData(117,{"num":100}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2057,[new ReqData(118,{"armyID":101}),new ReqData(119,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2058,[new ReqData(106,{"num":3}),new ReqData(111,{"num":3}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2059,[new ReqData(110,{"num":999}),new ReqData(119,null),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2060,[new ReqData(112,{"num":2}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2061,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2062,[new ReqData(121,{"country":1}),new ReqData(117,{"num":90}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2063,[new ReqData(102,{"time":330}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2064,[new ReqData(103,{"country":3}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2065,[new ReqData(111,{"num":3}),new ReqData(118,{"armyID":108}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2066,[new ReqData(103,{"country":2}),new ReqData(119,null),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2067,[new ReqData(112,{"num":3}),new ReqData(120,null),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2068,[new ReqData(103,{"country":3}),new ReqData(107,null),new ReqData(111,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2069,[new ReqData(103,{"country":0}),new ReqData(112,{"num":4}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2070,[new ReqData(118,{"armyID":105}),new ReqData(107,null),new ReqData(103,{"country":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2071,[new ReqData(118,{"armyID":109}),new ReqData(103,{"country":0}),new ReqData(110,{"num":950})]);
         this._stars[this._stars.length] = new MissionStarVO(2072,[new ReqData(103,{"country":2}),new ReqData(111,{"num":3}),new ReqData(118,{"armyID":105})]);
         this._stars[this._stars.length] = new MissionStarVO(2073,[new ReqData(102,{"time":320}),new ReqData(120,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2074,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":103}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2075,[new ReqData(118,{"armyID":101}),new ReqData(119,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2076,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":103}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2077,[new ReqData(106,{"num":3}),new ReqData(111,{"num":3}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2078,[new ReqData(102,{"time":320}),new ReqData(120,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2079,[new ReqData(103,{"country":2}),new ReqData(111,{"num":3}),new ReqData(118,{"armyID":105})]);
         this._stars[this._stars.length] = new MissionStarVO(2080,[new ReqData(121,{"country":1}),new ReqData(117,{"num":90}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2081,[new ReqData(103,{"country":3}),new ReqData(107,null),new ReqData(111,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2082,[new ReqData(110,{"num":999}),new ReqData(119,null),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2083,[new ReqData(118,{"armyID":105}),new ReqData(107,null),new ReqData(103,{"country":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2084,[new ReqData(112,{"num":3}),new ReqData(120,null),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2085,[new ReqData(112,{"num":2}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2086,[new ReqData(102,{"time":330}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2087,[new ReqData(103,{"country":2}),new ReqData(119,null),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2088,[new ReqData(118,{"armyID":109}),new ReqData(103,{"country":0}),new ReqData(110,{"num":950})]);
         this._stars[this._stars.length] = new MissionStarVO(2089,[new ReqData(111,{"num":3}),new ReqData(118,{"armyID":108}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2090,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2091,[new ReqData(112,{"num":2}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2092,[new ReqData(103,{"country":0}),new ReqData(112,{"num":4}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2093,[new ReqData(103,{"country":3}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2094,[new ReqData(102,{"time":320}),new ReqData(120,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2095,[new ReqData(110,{"num":999}),new ReqData(119,null),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2096,[new ReqData(112,{"num":2}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2097,[new ReqData(111,{"num":3}),new ReqData(118,{"armyID":108}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2098,[new ReqData(118,{"armyID":101}),new ReqData(119,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2099,[new ReqData(103,{"country":3}),new ReqData(107,null),new ReqData(111,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2100,[new ReqData(102,{"time":330}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2101,[new ReqData(103,{"country":0}),new ReqData(112,{"num":4}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2102,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":103}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2103,[new ReqData(103,{"country":2}),new ReqData(111,{"num":3}),new ReqData(118,{"armyID":105})]);
         this._stars[this._stars.length] = new MissionStarVO(2104,[new ReqData(112,{"num":3}),new ReqData(120,null),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2105,[new ReqData(118,{"armyID":109}),new ReqData(103,{"country":0}),new ReqData(110,{"num":950})]);
         this._stars[this._stars.length] = new MissionStarVO(2106,[new ReqData(112,{"num":2}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2107,[new ReqData(103,{"country":3}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2108,[new ReqData(121,{"country":1}),new ReqData(117,{"num":90}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2109,[new ReqData(103,{"country":2}),new ReqData(119,null),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2110,[new ReqData(106,{"num":3}),new ReqData(111,{"num":3}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2111,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2112,[new ReqData(121,{"country":0}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2113,[new ReqData(103,{"country":2}),new ReqData(111,{"num":2}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2114,[new ReqData(102,{"time":330}),new ReqData(118,{"armyID":108}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2115,[new ReqData(103,{"country":1}),new ReqData(107,null),new ReqData(118,{"armyID":108})]);
         this._stars[this._stars.length] = new MissionStarVO(2116,[new ReqData(121,{"country":0}),new ReqData(120,null),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2117,[new ReqData(121,{"country":2}),new ReqData(115,{"num":2200}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2118,[new ReqData(117,{"num":110}),new ReqData(103,{"country":3}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2119,[new ReqData(103,{"country":0}),new ReqData(118,{"armyID":112}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2120,[new ReqData(121,{"country":1}),new ReqData(118,{"armyID":105}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2121,[new ReqData(103,{"country":1}),new ReqData(118,{"armyID":103}),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2122,[new ReqData(112,{"num":2}),new ReqData(118,{"armyID":107}),new ReqData(121,{"country":0})]);
         this._stars[this._stars.length] = new MissionStarVO(2123,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":104}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2124,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":105}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2125,[new ReqData(102,{"time":350}),new ReqData(118,{"armyID":103}),new ReqData(109,{"num":222})]);
         this._stars[this._stars.length] = new MissionStarVO(2126,[new ReqData(118,{"armyID":109}),new ReqData(121,{"country":2}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2127,[new ReqData(103,{"country":0}),new ReqData(118,{"armyID":105}),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2128,[new ReqData(117,{"num":100}),new ReqData(108,{"num":3}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2129,[new ReqData(118,{"armyID":106}),new ReqData(101,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2130,[new ReqData(117,{"num":90}),new ReqData(118,{"armyID":106}),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2131,[new ReqData(121,{"country":0}),new ReqData(120,null),new ReqData(109,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(2132,[new ReqData(101,null),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2133,[new ReqData(103,{"country":1}),new ReqData(101,null),new ReqData(118,{"armyID":108})]);
         this._stars[this._stars.length] = new MissionStarVO(2134,[new ReqData(117,{"num":90}),new ReqData(108,{"num":3}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2135,[new ReqData(121,{"country":1}),new ReqData(112,{"num":3}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2136,[new ReqData(121,{"country":3}),new ReqData(101,null),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2137,[new ReqData(117,{"num":110}),new ReqData(118,{"armyID":102}),new ReqData(111,{"num":5})]);
         this._stars[this._stars.length] = new MissionStarVO(2138,[new ReqData(121,{"country":2}),new ReqData(115,{"num":3200}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2139,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2140,[new ReqData(103,{"country":1}),new ReqData(111,{"num":10}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2141,[new ReqData(117,{"num":90}),new ReqData(107,null),new ReqData(110,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(2142,[new ReqData(121,{"country":0}),new ReqData(120,null),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2143,[new ReqData(117,{"num":90}),new ReqData(103,{"country":1}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2143,[new ReqData(102,{"time":360}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2144,[new ReqData(123,null),new ReqData(121,{"country":2}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2145,[new ReqData(102,{"time":330}),new ReqData(120,null),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2146,[new ReqData(103,{"country":1}),new ReqData(118,{"armyID":101}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2147,[new ReqData(103,{"country":0}),new ReqData(101,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2148,[new ReqData(112,{"num":3}),new ReqData(118,{"armyID":110}),new ReqData(121,{"country":0})]);
         this._stars[this._stars.length] = new MissionStarVO(2149,[new ReqData(103,{"country":2}),new ReqData(111,{"num":2}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2150,[new ReqData(117,{"num":100}),new ReqData(108,{"num":3}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2151,[new ReqData(118,{"armyID":109}),new ReqData(121,{"country":2}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2152,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":105}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2153,[new ReqData(121,{"country":1}),new ReqData(118,{"armyID":105}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2154,[new ReqData(102,{"time":350}),new ReqData(118,{"armyID":103}),new ReqData(109,{"num":222})]);
         this._stars[this._stars.length] = new MissionStarVO(2155,[new ReqData(112,{"num":2}),new ReqData(118,{"armyID":107}),new ReqData(121,{"country":0})]);
         this._stars[this._stars.length] = new MissionStarVO(2156,[new ReqData(103,{"country":0}),new ReqData(118,{"armyID":112}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2157,[new ReqData(121,{"country":0}),new ReqData(120,null),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2158,[new ReqData(117,{"num":110}),new ReqData(103,{"country":3}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2159,[new ReqData(121,{"country":0}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2160,[new ReqData(117,{"num":100}),new ReqData(108,{"num":3}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2161,[new ReqData(117,{"num":90}),new ReqData(118,{"armyID":106}),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2162,[new ReqData(118,{"armyID":106}),new ReqData(101,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2163,[new ReqData(103,{"country":1}),new ReqData(118,{"armyID":103}),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2164,[new ReqData(121,{"country":1}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2165,[new ReqData(103,{"country":1}),new ReqData(107,null),new ReqData(118,{"armyID":108})]);
         this._stars[this._stars.length] = new MissionStarVO(2166,[new ReqData(102,{"time":330}),new ReqData(118,{"armyID":108}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2167,[new ReqData(121,{"country":2}),new ReqData(115,{"num":2200}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2168,[new ReqData(117,{"num":60}),new ReqData(107,null),new ReqData(110,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(2169,[new ReqData(111,{"num":2}),new ReqData(103,{"country":0}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2170,[new ReqData(103,{"country":2}),new ReqData(106,{"num":3}),new ReqData(101,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2171,[new ReqData(103,{"country":1}),new ReqData(107,null),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2172,[new ReqData(101,null),new ReqData(118,{"armyID":105}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2173,[new ReqData(121,{"country":3}),new ReqData(119,null),new ReqData(118,{"armyID":102})]);
         this._stars[this._stars.length] = new MissionStarVO(2174,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2175,[new ReqData(101,null),new ReqData(119,null),new ReqData(118,{"armyID":103})]);
         this._stars[this._stars.length] = new MissionStarVO(2176,[new ReqData(103,{"country":1}),new ReqData(119,null),new ReqData(105,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2177,[new ReqData(101,null),new ReqData(103,{"country":3}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2178,[new ReqData(107,null),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2179,[new ReqData(101,null),new ReqData(106,{"num":3}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2180,[new ReqData(102,{"time":320}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2181,[new ReqData(112,{"num":2}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2182,[new ReqData(121,{"country":1}),new ReqData(118,{"armyID":105}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2183,[new ReqData(117,{"num":100}),new ReqData(107,null),new ReqData(110,{"num":555})]);
         this._stars[this._stars.length] = new MissionStarVO(2184,[new ReqData(102,{"time":350}),new ReqData(118,{"armyID":103}),new ReqData(109,{"num":222})]);
         this._stars[this._stars.length] = new MissionStarVO(2185,[new ReqData(103,{"country":0}),new ReqData(112,{"num":3}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2186,[new ReqData(121,{"country":0}),new ReqData(111,{"num":3}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2187,[new ReqData(121,{"country":1}),new ReqData(119,null),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2188,[new ReqData(121,{"country":2}),new ReqData(112,{"num":4}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2189,[new ReqData(121,{"country":3}),new ReqData(119,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2190,[new ReqData(121,{"country":2}),new ReqData(120,null),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2191,[new ReqData(121,{"country":1}),new ReqData(116,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2192,[new ReqData(121,{"country":0}),new ReqData(117,{"num":100}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2193,[new ReqData(121,{"country":1}),new ReqData(118,{"armyID":103}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2194,[new ReqData(121,{"country":2}),new ReqData(109,{"num":888}),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2195,[new ReqData(121,{"country":3}),new ReqData(119,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2196,[new ReqData(121,{"country":2}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2197,[new ReqData(121,{"country":1}),new ReqData(108,{"num":2}),new ReqData(110,{"num":1300})]);
         this._stars[this._stars.length] = new MissionStarVO(2198,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":108}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2199,[new ReqData(121,{"country":3}),new ReqData(123,null),new ReqData(118,{"armyID":105})]);
         this._stars[this._stars.length] = new MissionStarVO(2200,[new ReqData(121,{"country":1}),new ReqData(119,null),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2201,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(111,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2202,[new ReqData(121,{"country":1}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2203,[new ReqData(121,{"country":2}),new ReqData(107,null),new ReqData(110,{"num":800})]);
         this._stars[this._stars.length] = new MissionStarVO(2204,[new ReqData(121,{"country":3}),new ReqData(119,null),new ReqData(105,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2205,[new ReqData(121,{"country":2}),new ReqData(115,{"num":2200}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2206,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2207,[new ReqData(121,{"country":3}),new ReqData(101,null),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2208,[new ReqData(117,{"num":110}),new ReqData(118,{"armyID":102}),new ReqData(111,{"num":5})]);
         this._stars[this._stars.length] = new MissionStarVO(2209,[new ReqData(117,{"num":90}),new ReqData(103,{"country":1}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2210,[new ReqData(102,{"time":360}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2211,[new ReqData(102,{"time":330}),new ReqData(120,null),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2212,[new ReqData(103,{"country":1}),new ReqData(111,{"num":10}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2213,[new ReqData(117,{"num":90}),new ReqData(107,null),new ReqData(110,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(2214,[new ReqData(121,{"country":0}),new ReqData(120,null),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2215,[new ReqData(123,null),new ReqData(121,{"country":2}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2216,[new ReqData(103,{"country":1}),new ReqData(118,{"armyID":101}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2217,[new ReqData(121,{"country":0}),new ReqData(120,null),new ReqData(109,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(2218,[new ReqData(117,{"num":90}),new ReqData(120,null),new ReqData(106,{"num":5})]);
         this._stars[this._stars.length] = new MissionStarVO(2219,[new ReqData(103,{"country":1}),new ReqData(101,null),new ReqData(118,{"armyID":108})]);
         this._stars[this._stars.length] = new MissionStarVO(2220,[new ReqData(121,{"country":1}),new ReqData(112,{"num":3}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2221,[new ReqData(103,{"country":0}),new ReqData(101,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2222,[new ReqData(112,{"num":3}),new ReqData(118,{"armyID":110}),new ReqData(121,{"country":0})]);
         this._stars[this._stars.length] = new MissionStarVO(2223,[new ReqData(102,{"time":320}),new ReqData(120,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2224,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2225,[new ReqData(110,{"num":999}),new ReqData(119,null),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2226,[new ReqData(112,{"num":2}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2227,[new ReqData(103,{"country":2}),new ReqData(119,null),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2228,[new ReqData(106,{"num":3}),new ReqData(111,{"num":3}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2229,[new ReqData(111,{"num":3}),new ReqData(118,{"armyID":108}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2230,[new ReqData(118,{"armyID":109}),new ReqData(103,{"country":0}),new ReqData(110,{"num":950})]);
         this._stars[this._stars.length] = new MissionStarVO(2231,[new ReqData(112,{"num":2}),new ReqData(120,null),new ReqData(102,{"time":320})]);
         this._stars[this._stars.length] = new MissionStarVO(2232,[new ReqData(103,{"country":3}),new ReqData(120,null),new ReqData(102,{"time":420})]);
         this._stars[this._stars.length] = new MissionStarVO(2233,[new ReqData(121,{"country":1}),new ReqData(117,{"num":90}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2234,[new ReqData(103,{"country":0}),new ReqData(112,{"num":4}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2235,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":103}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2236,[new ReqData(103,{"country":2}),new ReqData(111,{"num":3}),new ReqData(118,{"armyID":105})]);
         this._stars[this._stars.length] = new MissionStarVO(2237,[new ReqData(112,{"num":3}),new ReqData(120,null),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2238,[new ReqData(117,{"num":40}),new ReqData(107,null),new ReqData(120,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2239,[new ReqData(101,null),new ReqData(119,null),new ReqData(105,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2240,[new ReqData(117,{"num":40}),new ReqData(107,null),new ReqData(110,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(2241,[new ReqData(123,null),new ReqData(119,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2242,[new ReqData(103,{"country":3}),new ReqData(112,{"num":20}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2243,[new ReqData(123,null),new ReqData(107,null),new ReqData(111,{"num":30})]);
         this._stars[this._stars.length] = new MissionStarVO(2244,[new ReqData(107,null),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2245,[new ReqData(101,null),new ReqData(112,{"num":15}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2246,[new ReqData(112,{"num":15}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2247,[new ReqData(101,null),new ReqData(119,null),new ReqData(105,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2248,[new ReqData(103,{"country":1}),new ReqData(101,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2249,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2250,[new ReqData(121,{"country":2}),new ReqData(120,null),new ReqData(106,{"num":4})]);
         this._stars[this._stars.length] = new MissionStarVO(2251,[new ReqData(121,{"country":3}),new ReqData(119,null),new ReqData(106,{"num":4})]);
         this._stars[this._stars.length] = new MissionStarVO(2252,[new ReqData(112,{"num":2}),new ReqData(120,null),new ReqData(106,{"num":4})]);
         this._stars[this._stars.length] = new MissionStarVO(2253,[new ReqData(123,null),new ReqData(119,null),new ReqData(105,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2254,[new ReqData(107,null),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2255,[new ReqData(103,{"country":0}),new ReqData(112,{"num":50}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2256,[new ReqData(101,null),new ReqData(106,{"num":3}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2257,[new ReqData(103,{"country":2}),new ReqData(123,null),new ReqData(101,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2258,[new ReqData(123,null),new ReqData(107,null),new ReqData(111,{"num":55})]);
         this._stars[this._stars.length] = new MissionStarVO(2259,[new ReqData(112,{"num":50}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2260,[new ReqData(117,{"num":100}),new ReqData(107,null),new ReqData(110,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(2261,[new ReqData(123,null),new ReqData(119,null),new ReqData(105,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2262,[new ReqData(112,{"num":50}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2263,[new ReqData(101,null),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2264,[new ReqData(117,{"num":100}),new ReqData(107,null),new ReqData(110,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(2265,[new ReqData(102,{"time":500}),new ReqData(119,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2266,[new ReqData(101,null),new ReqData(119,null),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2267,[new ReqData(117,{"num":100}),new ReqData(107,null),new ReqData(110,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(2268,[new ReqData(121,{"country":0}),new ReqData(119,null),new ReqData(112,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2269,[new ReqData(102,{"time":350}),new ReqData(118,{"armyID":103}),new ReqData(109,{"num":555})]);
         this._stars[this._stars.length] = new MissionStarVO(2270,[new ReqData(117,{"num":60}),new ReqData(108,{"num":3}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2271,[new ReqData(118,{"armyID":109}),new ReqData(121,{"country":2}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2272,[new ReqData(121,{"country":1}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2273,[new ReqData(117,{"num":50}),new ReqData(103,{"country":3}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2274,[new ReqData(117,{"num":40}),new ReqData(108,{"num":3}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2275,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":105}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2276,[new ReqData(121,{"country":1}),new ReqData(118,{"armyID":105}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2277,[new ReqData(103,{"country":1}),new ReqData(118,{"armyID":106}),new ReqData(112,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2278,[new ReqData(121,{"country":0}),new ReqData(120,null),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2279,[new ReqData(102,{"time":330}),new ReqData(118,{"armyID":108}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2280,[new ReqData(112,{"num":2}),new ReqData(118,{"armyID":107}),new ReqData(121,{"country":0})]);
         this._stars[this._stars.length] = new MissionStarVO(2281,[new ReqData(121,{"country":2}),new ReqData(115,{"num":1200}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2282,[new ReqData(117,{"num":40}),new ReqData(118,{"armyID":106}),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2283,[new ReqData(102,{"time":330}),new ReqData(119,null),new ReqData(106,{"num":5})]);
         this._stars[this._stars.length] = new MissionStarVO(2284,[new ReqData(117,{"num":80}),new ReqData(107,null),new ReqData(120,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2285,[new ReqData(102,{"time":340}),new ReqData(119,null),new ReqData(110,{"num":400})]);
         this._stars[this._stars.length] = new MissionStarVO(2286,[new ReqData(112,{"num":50}),new ReqData(120,null),new ReqData(106,{"num":5})]);
         this._stars[this._stars.length] = new MissionStarVO(2287,[new ReqData(101,null),new ReqData(119,null),new ReqData(102,{"time":335})]);
         this._stars[this._stars.length] = new MissionStarVO(2288,[new ReqData(123,null),new ReqData(119,null),new ReqData(106,{"num":5})]);
         this._stars[this._stars.length] = new MissionStarVO(2289,[new ReqData(103,{"country":3}),new ReqData(112,{"num":20}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2290,[new ReqData(117,{"num":95}),new ReqData(107,null),new ReqData(111,{"num":30})]);
         this._stars[this._stars.length] = new MissionStarVO(2291,[new ReqData(101,null),new ReqData(119,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2292,[new ReqData(102,{"time":350}),new ReqData(120,null),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2293,[new ReqData(101,null),new ReqData(119,null),new ReqData(121,{"country":0})]);
         this._stars[this._stars.length] = new MissionStarVO(2294,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2295,[new ReqData(103,{"country":3}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2296,[new ReqData(117,{"num":85}),new ReqData(107,null),new ReqData(110,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(2297,[new ReqData(107,null),new ReqData(120,null),new ReqData(106,{"num":5})]);
         this._stars[this._stars.length] = new MissionStarVO(2298,[new ReqData(103,{"country":2}),new ReqData(111,{"num":3}),new ReqData(118,{"armyID":105})]);
         this._stars[this._stars.length] = new MissionStarVO(2299,[new ReqData(121,{"country":1}),new ReqData(117,{"num":90}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2300,[new ReqData(103,{"country":3}),new ReqData(107,null),new ReqData(111,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2301,[new ReqData(102,{"time":330}),new ReqData(120,null),new ReqData(106,{"num":5})]);
         this._stars[this._stars.length] = new MissionStarVO(2302,[new ReqData(103,{"country":2}),new ReqData(119,null),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2303,[new ReqData(118,{"armyID":109}),new ReqData(103,{"country":0}),new ReqData(110,{"num":950})]);
         this._stars[this._stars.length] = new MissionStarVO(2304,[new ReqData(111,{"num":3}),new ReqData(118,{"armyID":108}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2305,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":103}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2306,[new ReqData(106,{"num":3}),new ReqData(111,{"num":1}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2307,[new ReqData(103,{"country":3}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2308,[new ReqData(102,{"time":320}),new ReqData(120,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2309,[new ReqData(110,{"num":999}),new ReqData(119,null),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2310,[new ReqData(112,{"num":2}),new ReqData(120,null),new ReqData(106,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2311,[new ReqData(118,{"armyID":105}),new ReqData(107,null),new ReqData(103,{"country":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2312,[new ReqData(112,{"num":3}),new ReqData(120,null),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2313,[new ReqData(101,null),new ReqData(111,{"num":3}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2314,[new ReqData(103,{"country":1}),new ReqData(120,null),new ReqData(106,{"num":7})]);
         this._stars[this._stars.length] = new MissionStarVO(2315,[new ReqData(117,{"num":100}),new ReqData(107,null),new ReqData(110,{"num":800})]);
         this._stars[this._stars.length] = new MissionStarVO(2316,[new ReqData(101,null),new ReqData(119,null),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2317,[new ReqData(117,{"num":100}),new ReqData(107,null),new ReqData(110,{"num":1000})]);
         this._stars[this._stars.length] = new MissionStarVO(2318,[new ReqData(112,{"num":3}),new ReqData(120,null),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2319,[new ReqData(118,{"armyID":101}),new ReqData(119,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2320,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2321,[new ReqData(112,{"num":2}),new ReqData(120,null),new ReqData(106,{"num":7})]);
         this._stars[this._stars.length] = new MissionStarVO(2322,[new ReqData(111,{"num":3}),new ReqData(118,{"armyID":108}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2323,[new ReqData(103,{"country":2}),new ReqData(123,null),new ReqData(118,{"armyID":105})]);
         this._stars[this._stars.length] = new MissionStarVO(2324,[new ReqData(123,null),new ReqData(119,null),new ReqData(118,{"armyID":109})]);
         this._stars[this._stars.length] = new MissionStarVO(2325,[new ReqData(101,null),new ReqData(117,{"num":100}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2326,[new ReqData(123,null),new ReqData(119,null),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2327,[new ReqData(103,{"country":0}),new ReqData(112,{"num":4}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2328,[new ReqData(103,{"country":0}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2329,[new ReqData(101,null),new ReqData(119,null),new ReqData(118,{"armyID":101})]);
         this._stars[this._stars.length] = new MissionStarVO(2330,[new ReqData(123,null),new ReqData(107,null),new ReqData(111,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2331,[new ReqData(107,null),new ReqData(120,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2332,[new ReqData(103,{"country":2}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2333,[new ReqData(112,{"num":3}),new ReqData(120,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2334,[new ReqData(103,{"country":1}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2335,[new ReqData(117,{"num":80}),new ReqData(107,null),new ReqData(110,{"num":300})]);
         this._stars[this._stars.length] = new MissionStarVO(2336,[new ReqData(103,{"country":0}),new ReqData(119,null),new ReqData(118,{"armyID":105})]);
         this._stars[this._stars.length] = new MissionStarVO(2337,[new ReqData(103,{"country":3}),new ReqData(123,null),new ReqData(108,{"num":4})]);
         this._stars[this._stars.length] = new MissionStarVO(2338,[new ReqData(112,{"num":5}),new ReqData(120,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2339,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2340,[new ReqData(117,{"num":80}),new ReqData(107,null),new ReqData(110,{"num":800})]);
         this._stars[this._stars.length] = new MissionStarVO(2341,[new ReqData(101,null),new ReqData(120,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2342,[new ReqData(103,{"country":3}),new ReqData(119,null),new ReqData(106,{"num":5})]);
         this._stars[this._stars.length] = new MissionStarVO(2343,[new ReqData(121,{"country":0}),new ReqData(120,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2344,[new ReqData(121,{"country":1}),new ReqData(107,null),new ReqData(110,{"num":888})]);
         this._stars[this._stars.length] = new MissionStarVO(2345,[new ReqData(121,{"country":2}),new ReqData(119,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2346,[new ReqData(121,{"country":3}),new ReqData(119,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2347,[new ReqData(121,{"country":0}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2348,[new ReqData(121,{"country":1}),new ReqData(120,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2349,[new ReqData(121,{"country":2}),new ReqData(119,null),new ReqData(109,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2350,[new ReqData(121,{"country":3}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2351,[new ReqData(121,{"country":0}),new ReqData(120,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2352,[new ReqData(121,{"country":1}),new ReqData(107,null),new ReqData(111,{"num":50})]);
         this._stars[this._stars.length] = new MissionStarVO(2353,[new ReqData(121,{"country":2}),new ReqData(120,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2354,[new ReqData(121,{"country":3}),new ReqData(107,null),new ReqData(110,{"num":999})]);
         this._stars[this._stars.length] = new MissionStarVO(2355,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2356,[new ReqData(121,{"country":1}),new ReqData(119,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2357,[new ReqData(121,{"country":2}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2358,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":101}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2359,[new ReqData(121,{"country":1}),new ReqData(118,{"armyID":102}),new ReqData(111,{"num":5})]);
         this._stars[this._stars.length] = new MissionStarVO(2360,[new ReqData(121,{"country":2}),new ReqData(117,{"num":115}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2361,[new ReqData(121,{"country":3}),new ReqData(111,{"num":10}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2362,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":105}),new ReqData(106,{"num":9})]);
         this._stars[this._stars.length] = new MissionStarVO(2363,[new ReqData(121,{"country":1}),new ReqData(107,null),new ReqData(118,{"armyID":108})]);
         this._stars[this._stars.length] = new MissionStarVO(2364,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":101}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2365,[new ReqData(121,{"country":3}),new ReqData(115,{"num":3200}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2366,[new ReqData(121,{"country":0}),new ReqData(101,null),new ReqData(106,{"num":8})]);
         this._stars[this._stars.length] = new MissionStarVO(2367,[new ReqData(121,{"country":1}),new ReqData(118,{"armyID":104}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2368,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":110}),new ReqData(115,{"num":2000})]);
         this._stars[this._stars.length] = new MissionStarVO(2369,[new ReqData(121,{"country":3}),new ReqData(108,{"num":3}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2370,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":103}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2371,[new ReqData(121,{"country":1}),new ReqData(118,{"armyID":105}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2372,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":108}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2373,[new ReqData(121,{"country":3}),new ReqData(119,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2374,[new ReqData(121,{"country":2}),new ReqData(119,null),new ReqData(111,{"num":333})]);
         this._stars[this._stars.length] = new MissionStarVO(2375,[new ReqData(121,{"country":1}),new ReqData(120,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2376,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2377,[new ReqData(121,{"country":3}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2378,[new ReqData(121,{"country":2}),new ReqData(119,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2379,[new ReqData(121,{"country":1}),new ReqData(107,null),new ReqData(110,{"num":2000})]);
         this._stars[this._stars.length] = new MissionStarVO(2380,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":108}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2381,[new ReqData(121,{"country":3}),new ReqData(120,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2382,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":103}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2383,[new ReqData(121,{"country":1}),new ReqData(123,null),new ReqData(108,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2384,[new ReqData(121,{"country":0}),new ReqData(123,null),new ReqData(108,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2385,[new ReqData(121,{"country":3}),new ReqData(107,null),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2386,[new ReqData(121,{"country":1}),new ReqData(123,null),new ReqData(108,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2387,[new ReqData(121,{"country":0}),new ReqData(120,null),new ReqData(106,{"num":5})]);
         this._stars[this._stars.length] = new MissionStarVO(2388,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":108}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2389,[new ReqData(121,{"country":1}),new ReqData(118,{"armyID":107}),new ReqData(111,{"num":5})]);
         this._stars[this._stars.length] = new MissionStarVO(2390,[new ReqData(121,{"country":2}),new ReqData(117,{"num":115}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2391,[new ReqData(121,{"country":3}),new ReqData(111,{"num":3}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2392,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":106}),new ReqData(106,{"num":9})]);
         this._stars[this._stars.length] = new MissionStarVO(2393,[new ReqData(121,{"country":1}),new ReqData(107,null),new ReqData(118,{"armyID":108})]);
         this._stars[this._stars.length] = new MissionStarVO(2394,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":105}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2395,[new ReqData(121,{"country":3}),new ReqData(115,{"num":3200}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2396,[new ReqData(121,{"country":0}),new ReqData(101,null),new ReqData(106,{"num":8})]);
         this._stars[this._stars.length] = new MissionStarVO(2397,[new ReqData(121,{"country":1}),new ReqData(118,{"armyID":104}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2398,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":103}),new ReqData(115,{"num":2000})]);
         this._stars[this._stars.length] = new MissionStarVO(2399,[new ReqData(121,{"country":3}),new ReqData(108,{"num":3}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2400,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":102}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2401,[new ReqData(121,{"country":1}),new ReqData(118,{"armyID":101}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2402,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":110}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2403,[new ReqData(121,{"country":3}),new ReqData(119,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2404,[new ReqData(121,{"country":2}),new ReqData(119,null),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2405,[new ReqData(121,{"country":1}),new ReqData(120,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2406,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2407,[new ReqData(121,{"country":3}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2408,[new ReqData(121,{"country":2}),new ReqData(119,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2409,[new ReqData(121,{"country":1}),new ReqData(107,null),new ReqData(110,{"num":2000})]);
         this._stars[this._stars.length] = new MissionStarVO(2410,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":108}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2411,[new ReqData(121,{"country":3}),new ReqData(120,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2412,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":103}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2413,[new ReqData(121,{"country":1}),new ReqData(123,null),new ReqData(108,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2414,[new ReqData(121,{"country":0}),new ReqData(123,null),new ReqData(108,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2415,[new ReqData(121,{"country":3}),new ReqData(107,null),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2416,[new ReqData(121,{"country":1}),new ReqData(123,null),new ReqData(108,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2417,[new ReqData(121,{"country":0}),new ReqData(120,null),new ReqData(106,{"num":5})]);
         this._stars[this._stars.length] = new MissionStarVO(2418,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":101}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2419,[new ReqData(121,{"country":1}),new ReqData(118,{"armyID":102}),new ReqData(111,{"num":5})]);
         this._stars[this._stars.length] = new MissionStarVO(2420,[new ReqData(121,{"country":2}),new ReqData(117,{"num":115}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2421,[new ReqData(121,{"country":3}),new ReqData(111,{"num":10}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2422,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":105}),new ReqData(106,{"num":9})]);
         this._stars[this._stars.length] = new MissionStarVO(2423,[new ReqData(121,{"country":1}),new ReqData(107,null),new ReqData(118,{"armyID":108})]);
         this._stars[this._stars.length] = new MissionStarVO(2424,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":101}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2425,[new ReqData(121,{"country":3}),new ReqData(115,{"num":3200}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2426,[new ReqData(121,{"country":0}),new ReqData(101,null),new ReqData(106,{"num":8})]);
         this._stars[this._stars.length] = new MissionStarVO(2427,[new ReqData(121,{"country":1}),new ReqData(118,{"armyID":104}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2428,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":110}),new ReqData(115,{"num":2000})]);
         this._stars[this._stars.length] = new MissionStarVO(2429,[new ReqData(121,{"country":3}),new ReqData(108,{"num":3}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2430,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":103}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2431,[new ReqData(121,{"country":1}),new ReqData(118,{"armyID":105}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2432,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":108}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2433,[new ReqData(121,{"country":3}),new ReqData(119,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2434,[new ReqData(121,{"country":2}),new ReqData(119,null),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2435,[new ReqData(121,{"country":1}),new ReqData(120,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2436,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2437,[new ReqData(121,{"country":3}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2438,[new ReqData(121,{"country":2}),new ReqData(119,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2439,[new ReqData(121,{"country":1}),new ReqData(107,null),new ReqData(110,{"num":2000})]);
         this._stars[this._stars.length] = new MissionStarVO(2440,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":108}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2441,[new ReqData(121,{"country":3}),new ReqData(120,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2442,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":103}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2443,[new ReqData(121,{"country":1}),new ReqData(123,null),new ReqData(108,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2444,[new ReqData(121,{"country":0}),new ReqData(123,null),new ReqData(108,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2445,[new ReqData(121,{"country":3}),new ReqData(107,null),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2446,[new ReqData(121,{"country":1}),new ReqData(123,null),new ReqData(108,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2447,[new ReqData(121,{"country":0}),new ReqData(120,null),new ReqData(106,{"num":5})]);
         this._stars[this._stars.length] = new MissionStarVO(2448,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":101}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2449,[new ReqData(121,{"country":1}),new ReqData(117,{"num":115}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2450,[new ReqData(121,{"country":2}),new ReqData(111,{"num":10}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2451,[new ReqData(121,{"country":3}),new ReqData(118,{"armyID":105}),new ReqData(106,{"num":9})]);
         this._stars[this._stars.length] = new MissionStarVO(2452,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(118,{"armyID":108})]);
         this._stars[this._stars.length] = new MissionStarVO(2453,[new ReqData(121,{"country":1}),new ReqData(115,{"num":3200}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2454,[new ReqData(121,{"country":2}),new ReqData(101,null),new ReqData(106,{"num":8})]);
         this._stars[this._stars.length] = new MissionStarVO(2455,[new ReqData(121,{"country":3}),new ReqData(118,{"armyID":104}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2456,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":110}),new ReqData(115,{"num":2000})]);
         this._stars[this._stars.length] = new MissionStarVO(2457,[new ReqData(121,{"country":1}),new ReqData(108,{"num":3}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2458,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":103}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2459,[new ReqData(121,{"country":3}),new ReqData(118,{"armyID":108}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2460,[new ReqData(121,{"country":3}),new ReqData(119,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2461,[new ReqData(121,{"country":2}),new ReqData(119,null),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2462,[new ReqData(121,{"country":1}),new ReqData(120,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2463,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2464,[new ReqData(121,{"country":3}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2465,[new ReqData(121,{"country":2}),new ReqData(119,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2466,[new ReqData(121,{"country":1}),new ReqData(107,null),new ReqData(110,{"num":2000})]);
         this._stars[this._stars.length] = new MissionStarVO(2467,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":108}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2468,[new ReqData(121,{"country":3}),new ReqData(120,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2469,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":103}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2470,[new ReqData(121,{"country":1}),new ReqData(123,null),new ReqData(108,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2471,[new ReqData(121,{"country":0}),new ReqData(123,null),new ReqData(108,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2472,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":101}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2473,[new ReqData(121,{"country":1}),new ReqData(117,{"num":115}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2474,[new ReqData(121,{"country":2}),new ReqData(111,{"num":10}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2475,[new ReqData(121,{"country":3}),new ReqData(118,{"armyID":105}),new ReqData(106,{"num":9})]);
         this._stars[this._stars.length] = new MissionStarVO(2476,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(118,{"armyID":108})]);
         this._stars[this._stars.length] = new MissionStarVO(2477,[new ReqData(121,{"country":1}),new ReqData(115,{"num":3200}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2478,[new ReqData(121,{"country":2}),new ReqData(101,null),new ReqData(106,{"num":8})]);
         this._stars[this._stars.length] = new MissionStarVO(2479,[new ReqData(121,{"country":3}),new ReqData(118,{"armyID":104}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2480,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":110}),new ReqData(115,{"num":2000})]);
         this._stars[this._stars.length] = new MissionStarVO(2481,[new ReqData(121,{"country":1}),new ReqData(108,{"num":3}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2482,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":103}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2483,[new ReqData(121,{"country":3}),new ReqData(120,null),new ReqData(106,{"num":5})]);
         this._stars[this._stars.length] = new MissionStarVO(2484,[new ReqData(121,{"country":3}),new ReqData(119,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2485,[new ReqData(121,{"country":2}),new ReqData(119,null),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2486,[new ReqData(121,{"country":1}),new ReqData(120,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2487,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2488,[new ReqData(121,{"country":3}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2489,[new ReqData(121,{"country":2}),new ReqData(119,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2490,[new ReqData(121,{"country":1}),new ReqData(107,null),new ReqData(110,{"num":2000})]);
         this._stars[this._stars.length] = new MissionStarVO(2491,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":108}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2492,[new ReqData(121,{"country":3}),new ReqData(120,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2493,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":103}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2494,[new ReqData(121,{"country":1}),new ReqData(123,null),new ReqData(108,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2495,[new ReqData(121,{"country":0}),new ReqData(123,null),new ReqData(108,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2496,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":101}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2497,[new ReqData(121,{"country":1}),new ReqData(117,{"num":115}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2498,[new ReqData(121,{"country":2}),new ReqData(111,{"num":10}),new ReqData(108,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2499,[new ReqData(121,{"country":3}),new ReqData(118,{"armyID":105}),new ReqData(106,{"num":9})]);
         this._stars[this._stars.length] = new MissionStarVO(2500,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(118,{"armyID":108})]);
         this._stars[this._stars.length] = new MissionStarVO(2501,[new ReqData(121,{"country":1}),new ReqData(115,{"num":3200}),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2502,[new ReqData(121,{"country":2}),new ReqData(101,null),new ReqData(106,{"num":8})]);
         this._stars[this._stars.length] = new MissionStarVO(2503,[new ReqData(121,{"country":3}),new ReqData(118,{"armyID":104}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2504,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":110}),new ReqData(115,{"num":2000})]);
         this._stars[this._stars.length] = new MissionStarVO(2505,[new ReqData(121,{"country":1}),new ReqData(108,{"num":3}),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2506,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":103}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2507,[new ReqData(121,{"country":3}),new ReqData(120,null),new ReqData(106,{"num":5})]);
         this._stars[this._stars.length] = new MissionStarVO(2508,[new ReqData(121,{"country":3}),new ReqData(119,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2509,[new ReqData(121,{"country":2}),new ReqData(119,null),new ReqData(111,{"num":3})]);
         this._stars[this._stars.length] = new MissionStarVO(2510,[new ReqData(121,{"country":1}),new ReqData(120,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2511,[new ReqData(121,{"country":0}),new ReqData(107,null),new ReqData(119,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2512,[new ReqData(121,{"country":3}),new ReqData(123,null),new ReqData(108,{"num":2})]);
         this._stars[this._stars.length] = new MissionStarVO(2513,[new ReqData(121,{"country":2}),new ReqData(119,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2514,[new ReqData(121,{"country":1}),new ReqData(107,null),new ReqData(110,{"num":2000})]);
         this._stars[this._stars.length] = new MissionStarVO(2515,[new ReqData(121,{"country":0}),new ReqData(118,{"armyID":108}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2516,[new ReqData(121,{"country":3}),new ReqData(120,null),new ReqData(106,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2517,[new ReqData(121,{"country":2}),new ReqData(118,{"armyID":103}),new ReqData(107,null)]);
         this._stars[this._stars.length] = new MissionStarVO(2518,[new ReqData(121,{"country":1}),new ReqData(123,null),new ReqData(108,{"num":6})]);
         this._stars[this._stars.length] = new MissionStarVO(2519,[new ReqData(121,{"country":0}),new ReqData(123,null),new ReqData(108,{"num":6})]);
      }
      
      private function initWave() : void
      {
         this._dic = new Vector.<WaveDataVO>();
         this._dic[this._dic.length] = new ExtraWave1001().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1002().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1003().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1004().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1005().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1006().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1007().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1008().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1009().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1010().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1011().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1012().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1013().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1014().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1015().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1016().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1017().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1018().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1019().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1020().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1021().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1022().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1023().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1024().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1025().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1026().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1027().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1028().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1029().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1030().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1031().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1032().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1033().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1034().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1035().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1036().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1037().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1038().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1039().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1040().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1041().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1042().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1043().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1044().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1045().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1046().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1047().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1048().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1049().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1050().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1051().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1052().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1053().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1054().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1055().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1056().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1057().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1058().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1059().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1060().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1061().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1062().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1063().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1064().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1065().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1066().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1067().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1068().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1069().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1070().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1071().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1072().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1073().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1074().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1075().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1076().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1077().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1078().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1079().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1080().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1081().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1082().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1083().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1084().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1085().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1086().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1087().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1088().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1089().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1090().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1091().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1092().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1093().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1094().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1095().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1096().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1097().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1098().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1099().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1100().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1101().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1102().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1103().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1104().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1105().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1106().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1107().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1108().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1109().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1110().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1111().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1112().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1113().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1114().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1115().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1116().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1117().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1118().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1119().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1120().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1121().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1122().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1123().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1124().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1125().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1126().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1127().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1128().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1129().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1130().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1131().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1132().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1133().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1134().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1135().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1136().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1137().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1138().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1139().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1140().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1141().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1142().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1143().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1144().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1145().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1146().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1147().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1148().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1149().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1150().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1151().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1152().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1153().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1154().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1155().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1156().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1157().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1158().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1159().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1160().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1161().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1162().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1163().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1164().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1165().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1166().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1167().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1168().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1169().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1170().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1171().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1172().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1173().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1174().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1175().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1176().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1177().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1178().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1179().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1180().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1181().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1182().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1183().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1184().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1185().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1186().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1187().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1188().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1189().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1190().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1191().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1192().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1193().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1194().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1195().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1196().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1197().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1198().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1199().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1200().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1201().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1202().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1203().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1204().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1205().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1206().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1207().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1208().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1209().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1210().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1211().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1212().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1213().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1214().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1215().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1216().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1217().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1218().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1219().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1220().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1221().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1222().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1223().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1224().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1225().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1226().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1227().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1228().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1229().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1230().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1231().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1232().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1233().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1234().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1235().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1236().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1237().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1238().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1239().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1240().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1241().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1242().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1243().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1244().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1245().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1246().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1247().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1248().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1249().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1250().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1251().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1252().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1253().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1254().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1255().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1256().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave1257().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2001().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2002().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2003().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2004().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2005().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2006().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2007().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2008().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2009().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2010().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2011().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2012().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2013().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2014().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2015().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2016().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2017().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2018().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2019().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2020().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2021().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2022().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2023().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2024().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2025().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2026().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2027().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2028().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2029().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2030().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2031().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2032().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2033().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2034().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2035().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2036().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2037().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2038().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2039().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2040().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2041().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2042().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2043().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2044().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2045().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2046().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2047().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2048().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2049().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2050().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2051().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2052().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2053().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2054().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2055().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2056().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2057().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2058().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2059().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2060().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2061().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2062().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2063().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2064().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2065().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2066().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2067().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2068().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2069().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2070().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2071().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2072().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2073().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2074().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2075().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2076().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2077().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2078().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2079().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2080().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2081().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2082().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2083().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2084().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2085().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2086().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2087().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2088().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2089().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2090().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2091().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2092().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2093().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2094().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2095().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2096().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2097().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2098().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2099().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2100().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2101().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2102().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2103().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2104().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2105().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2106().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2107().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2108().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2109().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2110().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2111().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2112().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2113().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2114().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2115().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2116().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2117().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2118().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2119().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2120().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2121().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2122().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2123().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2124().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2125().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2126().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2127().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2128().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2129().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2130().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2131().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2132().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2133().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2134().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2135().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2136().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2137().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2138().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2139().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2140().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2141().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2142().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2143().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2144().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2145().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2146().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2147().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2148().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2149().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2150().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2151().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2152().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2153().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2154().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2155().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2156().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2157().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2158().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2159().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2160().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2161().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2162().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2163().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2164().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2165().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2166().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2167().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2168().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2169().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2170().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2171().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2172().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2173().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2174().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2175().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2176().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2177().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2178().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2179().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2180().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2181().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2182().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2183().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2184().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2185().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2186().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2187().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2188().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2189().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2190().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2191().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2192().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2193().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2194().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2195().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2196().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2197().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2198().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2199().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2200().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2201().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2202().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2203().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2204().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2205().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2206().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2207().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2208().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2209().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2210().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2211().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2212().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2213().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2214().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2215().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2216().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2217().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2218().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2219().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2220().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2221().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2222().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2223().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2224().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2225().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2226().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2227().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2228().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2229().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2230().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2231().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2232().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2233().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2234().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2235().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2236().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2237().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2238().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2239().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2240().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2241().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2242().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2243().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2244().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2245().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2246().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2247().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2248().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2249().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2250().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2251().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2252().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2253().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2254().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2255().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2256().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2257().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2258().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2259().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2260().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2261().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2262().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2263().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2264().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2265().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2266().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2267().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2268().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2269().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2270().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2271().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2272().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2273().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2274().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2275().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2276().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2277().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2278().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2279().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2280().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2281().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2282().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2283().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2284().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2285().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2286().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2287().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2288().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2289().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2290().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2291().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2292().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2293().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2294().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2295().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2296().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2297().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2298().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2299().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2300().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2301().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2302().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2303().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2304().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2305().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2306().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2307().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2308().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2309().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2310().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2311().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2312().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2313().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2314().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2315().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2316().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2317().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2318().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2319().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2320().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2321().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2322().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2323().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2324().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2325().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2326().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2327().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2328().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2329().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2330().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2331().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2332().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2333().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2334().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2335().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2336().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2337().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2338().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2339().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2340().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2341().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2342().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2343().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2344().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2345().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2346().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2347().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2348().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2349().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2350().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2351().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2352().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2353().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2354().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2355().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2356().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2357().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2358().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2359().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2360().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2361().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2362().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2363().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2364().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2365().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2366().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2367().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2368().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2369().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2370().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2371().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2372().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2373().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2374().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2375().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2376().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2377().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2378().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2379().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2380().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2381().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2382().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2383().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2384().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2385().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2386().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2387().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2388().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2389().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2390().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2391().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2392().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2393().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2394().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2395().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2396().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2397().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2398().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2399().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2400().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2401().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2402().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2403().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2404().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2405().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2406().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2407().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2408().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2409().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2410().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2411().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2412().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2413().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2414().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2415().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2416().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2417().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2418().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2419().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2420().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2421().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2422().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2423().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2424().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2425().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2426().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2427().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2428().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2429().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2430().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2431().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2432().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2433().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2434().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2435().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2436().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2437().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2438().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2439().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2440().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2441().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2442().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2443().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2444().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2445().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2446().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2447().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2448().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2449().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2450().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2451().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2452().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2453().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2454().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2455().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2456().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2457().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2458().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2459().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2460().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2461().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2462().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2463().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2464().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2465().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2466().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2467().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2468().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2469().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2470().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2471().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2472().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2473().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2474().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2475().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2476().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2477().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2478().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2479().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2480().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2481().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2482().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2483().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2484().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2485().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2486().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2487().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2488().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2489().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2490().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2491().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2492().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2493().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2494().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2495().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2496().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2497().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2498().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2499().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2500().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2501().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2502().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2503().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2504().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2505().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2506().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2507().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2508().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2509().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2510().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2511().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2512().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2513().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2514().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2515().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2516().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2517().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2518().waveDataVO;
         this._dic[this._dic.length] = new ExtraWave2519().waveDataVO;
      }
      
      public function findBattle(param1:int) : MissionBattleVO
      {
         var _loc2_:MissionBattleVO = null;
         for each(_loc2_ in this._battles)
         {
            if(_loc2_.mid == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findWave(param1:int) : WaveDataVO
      {
         var _loc2_:WaveDataVO = null;
         for each(_loc2_ in this._dic)
         {
            if(_loc2_.dataID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findReq(param1:int) : MissionStarVO
      {
         var _loc2_:MissionStarVO = null;
         for each(_loc2_ in this._stars)
         {
            if(_loc2_.mid == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

