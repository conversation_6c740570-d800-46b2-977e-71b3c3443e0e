package com.mogames.display
{
   import com.mogames.data.ImageInfo;
   import flash.display.Bitmap;
   import flash.events.MouseEvent;
   import mogames.gameUI.item.BaseTipItem;
   
   public class BitmapBtn extends BaseTipItem
   {
      
      protected var _bitmap:Bitmap;
      
      protected var _sequences:Vector.<ImageInfo>;
      
      public function BitmapBtn(param1:Vector.<ImageInfo>)
      {
         super();
         this._sequences = param1;
         this._bitmap = new Bitmap();
         addChild(this._bitmap);
         addEventListener(MouseEvent.MOUSE_UP,this.onMouse);
         addEventListener(MouseEvent.MOUSE_OVER,this.onMouse);
         addEventListener(MouseEvent.MOUSE_OUT,this.onMouse);
         addEventListener(MouseEvent.MOUSE_DOWN,this.onMouse);
         this.changeStates(0);
         buttonMode = true;
         mouseChildren = false;
      }
      
      public function set enabled(param1:Boolean) : void
      {
         mouseEnabled = param1;
         if(param1)
         {
            this.changeStates(0);
         }
         else
         {
            this.changeStates(1);
         }
      }
      
      private function changeStates(param1:int) : void
      {
         this._bitmap.bitmapData = this._sequences[param1].bitmapData;
         this._bitmap.x = this._sequences[param1].pivotX;
         this._bitmap.y = this._sequences[param1].pivotY;
      }
      
      private function onMouse(param1:MouseEvent) : void
      {
         switch(param1.type)
         {
            case MouseEvent.MOUSE_UP:
            case MouseEvent.MOUSE_OUT:
               this.changeStates(0);
               break;
            case MouseEvent.MOUSE_OVER:
               this.changeStates(1);
               break;
            case MouseEvent.MOUSE_DOWN:
               this.changeStates(2);
         }
      }
      
      override public function destroy() : void
      {
         this._bitmap.bitmapData = null;
         this._bitmap = null;
         this._sequences = null;
         super.destroy();
      }
   }
}

