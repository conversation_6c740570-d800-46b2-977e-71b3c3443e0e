package com.mogames.data
{
   import com.mogames.utils.MathUtil;
   
   public class ValueVO
   {
      
      private var _value:Oint = new Oint();
      
      private var _type:Oint = new Oint();
      
      public function ValueVO(param1:int, param2:int)
      {
         super();
         MathUtil.saveINT(this._value,param1);
         MathUtil.saveINT(this._type,param2);
      }
      
      public function get value() : int
      {
         return MathUtil.loadINT(this._value);
      }
      
      public function get isPer() : Boolean
      {
         return MathUtil.loadINT(this._type) == 1;
      }
      
      public function get valueStr() : String
      {
         if(this.isPer)
         {
            return this.value + "%";
         }
         return this.value + "";
      }
   }
}

