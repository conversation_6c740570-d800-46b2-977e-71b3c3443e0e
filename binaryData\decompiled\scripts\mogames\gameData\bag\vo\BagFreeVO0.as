package mogames.gameData.bag.vo
{
   import mogames.gameData.bag.base.BagLockVO;
   import mogames.gameData.mission.MissionProxy;
   
   public class BagFreeVO0 extends BagLockVO
   {
      
      public function BagFreeVO0(param1:int, param2:String = "")
      {
         super(param1,param2);
      }
      
      override public function get isOpen() : Boolean
      {
         return MissionProxy.instance().allFinish(201) && MissionProxy.instance().curStar(201) >= MissionProxy.instance().totalStar(201);
      }
   }
}

