package mogames.gameData.base.func
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.flag.FlagProxy;
   
   public class HeiShiVO
   {
      
      private var _flagID:Oint = new Oint();
      
      private var _golds:Array;
      
      private var _moneys:Array;
      
      public function HeiShiVO(param1:int, param2:Array, param3:Array)
      {
         super();
         MathUtil.saveINT(this._flagID,param1);
         this._golds = param2;
         this._moneys = param3;
      }
      
      public function get randomGood() : Object
      {
         if(MathUtil.checkOdds(300))
         {
            return {
               "type":1,
               "index":int(Math.random() * this._golds.length)
            };
         }
         return {
            "type":0,
            "index":int(Math.random() * this._moneys.length)
         };
      }
      
      public function get golds() : Array
      {
         return this._golds;
      }
      
      public function get moneys() : Array
      {
         return this._moneys;
      }
      
      public function setBuy() : void
      {
         FlagProxy.instance().openFlag.setValue(this.flagID);
      }
      
      public function get hasBuy() : Boolean
      {
         return FlagProxy.instance().openFlag.isComplete(this.flagID);
      }
      
      private function get flagID() : int
      {
         return MathUtil.loadINT(this._flagID);
      }
   }
}

