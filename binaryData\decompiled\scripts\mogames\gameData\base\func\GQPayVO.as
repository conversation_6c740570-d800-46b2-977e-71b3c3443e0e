package mogames.gameData.base.func
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import file.GQPayConfig;
   import mogames.gameData.pay.GQPayProxy;
   
   public class GQPayVO
   {
      
      private var _flagID:Oint = new Oint();
      
      private var _need:Oint = new Oint();
      
      private var _rewards:Array;
      
      public function GQPayVO(param1:int, param2:int, param3:Array)
      {
         super();
         MathUtil.saveINT(this._flagID,param1);
         MathUtil.saveINT(this._need,param2);
         this._rewards = param3;
      }
      
      public function setGet() : void
      {
         GQPayProxy.instance().openFlag.setValue(this.flagID,1);
      }
      
      public function get canGet() : Boolean
      {
         return GQPayConfig.instance().total >= this.need;
      }
      
      public function get hasGet() : Boolean
      {
         return GQPayProxy.instance().openFlag.isComplete(this.flagID);
      }
      
      public function get need() : int
      {
         return MathUtil.loadINT(this._need);
      }
      
      public function get rewards() : Array
      {
         return this._rewards;
      }
      
      private function get flagID() : int
      {
         return MathUtil.loadINT(this._flagID);
      }
   }
}

