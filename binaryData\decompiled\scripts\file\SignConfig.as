package file
{
   import mogames.gameData.base.RewardProxy;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.boon.SignRewardVO;
   
   public class SignConfig
   {
      
      private static var _instance:SignConfig;
      
      private var _rewards:Vector.<SignRewardVO>;
      
      private var _single:Array;
      
      public function SignConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : SignConfig
      {
         if(!_instance)
         {
            _instance = new SignConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._rewards = new Vector.<SignRewardVO>();
         this._rewards[this._rewards.length] = new SignRewardVO(0,189,3,[new BaseRewardVO(11104,8),new BaseRewardVO(10981,1),new BaseRewardVO(10284,10),new BaseRewardVO(10008,200),new BaseRewardVO(10302,3),new BaseRewardVO(18941,1)]);
         this._rewards[this._rewards.length] = new SignRewardVO(1,190,7,[new BaseRewardVO(10316,10),new BaseRewardVO(11401,10),new BaseRewardVO(11601,10),new BaseRewardVO(10285,10),new BaseRewardVO(10563,10),new BaseRewardVO(18942,1)]);
         this._rewards[this._rewards.length] = new SignRewardVO(2,191,12,[new BaseRewardVO(10982,1),new BaseRewardVO(10852,10),new BaseRewardVO(10286,10),new BaseRewardVO(10287,10),new BaseRewardVO(10303,10),new BaseRewardVO(18943,1)]);
         this._rewards[this._rewards.length] = new SignRewardVO(3,192,20,[new BaseRewardVO(10659,3),new BaseRewardVO(10853,10),new BaseRewardVO(10288,10),new BaseRewardVO(10985,1),new BaseRewardVO(10572,5),new BaseRewardVO(18944,1)]);
         this._rewards[this._rewards.length] = new SignRewardVO(4,193,0,[new BaseRewardVO(10986,1),new BaseRewardVO(10575,5),new BaseRewardVO(10987,1),new BaseRewardVO(10551,10),new BaseRewardVO(10573,5),new BaseRewardVO(18945,1)]);
         this._single = [new BaseRewardVO(10000,10000)];
      }
      
      public function monthRefresh() : void
      {
         var _loc1_:SignRewardVO = null;
         for each(_loc1_ in this._rewards)
         {
            _loc1_.monthRefresh();
         }
      }
      
      public function findRewardVO(param1:int) : SignRewardVO
      {
         var _loc2_:SignRewardVO = null;
         for each(_loc2_ in this._rewards)
         {
            if(_loc2_.index == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function get singleReward() : Array
      {
         return RewardProxy.instance().newGiftReward(this._single);
      }
      
      public function get canGet() : Boolean
      {
         var _loc1_:SignRewardVO = null;
         for each(_loc1_ in this._rewards)
         {
            if(_loc1_.canGet)
            {
               return true;
            }
         }
         return false;
      }
   }
}

