package file
{
   import mogames.gameData.talent.TalentFactory;
   import mogames.gameData.talent.base.TalentConstVO;
   import mogames.gameData.talent.base.TalentLearnVO;
   import mogames.gameData.talent.base.TalentNeedVO;
   
   public class TalentConfig
   {
      
      private static var _instance:TalentConfig;
      
      private var _args:Vector.<TalentConstVO>;
      
      private var _learns:Vector.<TalentLearnVO>;
      
      public function TalentConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : TalentConfig
      {
         if(!_instance)
         {
            _instance = new TalentConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._args = new Vector.<TalentConstVO>();
         this._args[this._args.length] = new TalentConstVO(1101,0,"朴刀兵训练","ICON_XUN_LIAN1","减少朴刀兵@param0%的训练费用。");
         this._args[this._args.length] = new TalentConstVO(1102,0,"链锤兵训练","ICON_XUN_LIAN2","减少链锤兵@param0%的训练费用。");
         this._args[this._args.length] = new TalentConstVO(1103,0,"强壮训练","ICON_TI_PO","朴刀兵和链锤兵血量增加@param0%。");
         this._args[this._args.length] = new TalentConstVO(1104,0,"攻击训练","ICON_GONG_JI","朴刀兵和链锤兵攻击力增加@param0%。");
         this._args[this._args.length] = new TalentConstVO(1105,0,"牺牲","ICON_XI_SHENG","朴刀兵阵亡后，降低目标@param0%攻击，持续10秒，减攻效果不叠加。");
         this._args[this._args.length] = new TalentConstVO(1106,0,"重锤击","ICON_ZHONG_CUI","链锤兵攻击时有@param0%的几率对目标造成@param1倍伤害。如果目标阵亡，则提高自身@param2%的攻击，持续@param3秒，加攻效果不叠加。");
         this._args[this._args.length] = new TalentConstVO(1201,0,"女兵训练","ICON_XUN_LIAN3","减少女兵@param0%的训练费用。");
         this._args[this._args.length] = new TalentConstVO(1202,0,"武斗兵训练","ICON_XUN_LIAN4","减少武斗兵@param0%的训练费用。");
         this._args[this._args.length] = new TalentConstVO(1203,0,"强壮训练","ICON_TI_PO","女兵和武斗兵血量增加@param0%。");
         this._args[this._args.length] = new TalentConstVO(1204,0,"攻击训练","ICON_GONG_JI","女兵和武斗兵攻击力增加@param0%。");
         this._args[this._args.length] = new TalentConstVO(1205,0,"魅惑","ICON_MEI_HUO","女兵攻击时有@param0%的几率降低目标@param1%的防御，持续@param2秒，减防效果不叠加。");
         this._args[this._args.length] = new TalentConstVO(1206,0,"再生","ICON_ZAI_SHENG","武斗兵快阵亡时有@param0%的几率回复@param1%的血量。");
         this._args[this._args.length] = new TalentConstVO(1301,0,"蛮兵训练","ICON_XUN_LIAN5","减少蛮兵@param0%的训练费用。");
         this._args[this._args.length] = new TalentConstVO(1302,0,"骑兵训练","ICON_XUN_LIAN6","减少骑兵@param0%的训练费用。");
         this._args[this._args.length] = new TalentConstVO(1303,0,"强壮训练","ICON_TI_PO","蛮兵和骑兵血量增加@param0%。");
         this._args[this._args.length] = new TalentConstVO(1304,0,"攻击训练","ICON_GONG_JI","蛮兵和骑兵攻击力增加@param0%。");
         this._args[this._args.length] = new TalentConstVO(1305,0,"嗜血","ICON_SHI_XUE","蛮兵在攻击时有@param0%的几率回复伤害值@param1%的血量。");
         this._args[this._args.length] = new TalentConstVO(1306,0,"冲锋","ICON_CHONG_FENG","骑兵对武将单位的伤害提高@param0%。");
         this._args[this._args.length] = new TalentConstVO(2101,1,"枪兵训练","ICON_XUN_LIAN7","减少枪兵@param0%的训练费用。");
         this._args[this._args.length] = new TalentConstVO(2102,1,"飞刀兵训练","ICON_XUN_LIAN8","减少飞刀兵@param0%的训练费用。");
         this._args[this._args.length] = new TalentConstVO(2103,1,"强壮训练","ICON_TI_PO","枪兵和飞刀兵血量增加@param0%。");
         this._args[this._args.length] = new TalentConstVO(2104,1,"攻击训练","ICON_GONG_JI","枪兵和飞刀兵攻击力增加@param0%。");
         this._args[this._args.length] = new TalentConstVO(2105,1,"猛刺","ICON_HEN_SAO","枪兵攻击时有@param0%的几率对前方敌人造成@param1倍伤害并弹开一小段距离。");
         this._args[this._args.length] = new TalentConstVO(2106,1,"施毒","ICON_SHI_DU","飞刀兵攻击时有@param0%的几率使目标士兵中毒，流失当前血量的@param1%，持续@param2秒,中毒效果不叠加。");
         this._args[this._args.length] = new TalentConstVO(2201,1,"铁缒兵训练","ICON_XUN_LIAN9","减少铁缒兵@param0%的训练费用。");
         this._args[this._args.length] = new TalentConstVO(2202,1,"弓箭手训练","ICON_XUN_LIAN10","减少弓箭手@param0%的训练费用。");
         this._args[this._args.length] = new TalentConstVO(2203,1,"强壮训练","ICON_TI_PO","铁缒兵和弓箭手血量增加@param0%。");
         this._args[this._args.length] = new TalentConstVO(2204,1,"攻击训练","ICON_GONG_JI","铁缒兵和弓箭手兵攻击力增加@param0%。");
         this._args[this._args.length] = new TalentConstVO(2205,1,"地震","ICON_DI_ZHEN","铁缒兵攻击时有@param0%的几率对四周最多3名敌人造成自身@param1%伤害并降低50%的移动速度，持续3秒，减速效果不叠加。");
         this._args[this._args.length] = new TalentConstVO(2206,1,"火箭","ICON_HUO_JIAN","弓箭手攻击时有@param0%的几率射出一根火箭对目标造成@param1倍伤害。");
         this._args[this._args.length] = new TalentConstVO(2301,1,"刺客训练","ICON_XUN_LIAN11","减少刺客@param0%的训练费用。");
         this._args[this._args.length] = new TalentConstVO(2302,1,"弩兵训练","ICON_XUN_LIAN12","减少弩兵@param0%的训练费用。");
         this._args[this._args.length] = new TalentConstVO(2303,1,"强壮训练","ICON_TI_PO","刺客和弩兵血量增加@param0%。");
         this._args[this._args.length] = new TalentConstVO(2304,1,"攻击训练","ICON_GONG_JI","刺客和弩兵攻击力增加@param0%。");
         this._args[this._args.length] = new TalentConstVO(2305,1,"隐身","ICON_YIN_SHEN","刺客攻击时有@param0%的几率触发隐身状态免疫所有伤害,持续@param1秒。");
         this._args[this._args.length] = new TalentConstVO(2306,1,"透甲箭","ICON_TOU_JIA","弩兵攻击时有@param0%的几率无视对方防御并造成@param1倍伤害。");
         this._args[this._args.length] = new TalentConstVO(3101,2,"加速采集","ICON_CAI_JI","提高@param0%的果树食物采集速度。");
         this._args[this._args.length] = new TalentConstVO(3102,2,"加速伐木","ICON_FA_MU","提高@param0%的木头采集速度。");
         this._args[this._args.length] = new TalentConstVO(3103,2,"加速狩猎","ICON_SHOU_LIE","提高@param0%的肉类食物采集速度。");
         this._args[this._args.length] = new TalentConstVO(3104,2,"撬锁","ICON_KAI_SUI","开锁所需时间减少@param0%。");
         this._learns = new Vector.<TalentLearnVO>();
         this._learns[this._learns.length] = new TalentLearnVO(1101,[[new TalentNeedVO(0,1)],[new TalentNeedVO(0,3)],[new TalentNeedVO(0,5)]]);
         this._learns[this._learns.length] = new TalentLearnVO(1102,[[new TalentNeedVO(0,1)],[new TalentNeedVO(0,3)],[new TalentNeedVO(0,5)]]);
         this._learns[this._learns.length] = new TalentLearnVO(1103,[[new TalentNeedVO(0,4),new TalentNeedVO(1101,1),new TalentNeedVO(1102,1)],[new TalentNeedVO(0,8)],[new TalentNeedVO(0,12)]]);
         this._learns[this._learns.length] = new TalentLearnVO(1104,[[new TalentNeedVO(0,6),new TalentNeedVO(1103,1)],[new TalentNeedVO(0,13)],[new TalentNeedVO(0,20)]]);
         this._learns[this._learns.length] = new TalentLearnVO(1105,[[new TalentNeedVO(0,8),new TalentNeedVO(1103,1)],[new TalentNeedVO(0,15)],[new TalentNeedVO(0,22)]]);
         this._learns[this._learns.length] = new TalentLearnVO(1106,[[new TalentNeedVO(0,8),new TalentNeedVO(1103,1)],[new TalentNeedVO(0,15)],[new TalentNeedVO(0,22)]]);
         this._learns[this._learns.length] = new TalentLearnVO(1201,[[new TalentNeedVO(0,1)],[new TalentNeedVO(0,3)],[new TalentNeedVO(0,5)]]);
         this._learns[this._learns.length] = new TalentLearnVO(1202,[[new TalentNeedVO(0,1)],[new TalentNeedVO(0,3)],[new TalentNeedVO(0,5)]]);
         this._learns[this._learns.length] = new TalentLearnVO(1203,[[new TalentNeedVO(0,4),new TalentNeedVO(1201,1),new TalentNeedVO(1202,1)],[new TalentNeedVO(0,8)],[new TalentNeedVO(0,12)]]);
         this._learns[this._learns.length] = new TalentLearnVO(1204,[[new TalentNeedVO(0,6),new TalentNeedVO(1203,1)],[new TalentNeedVO(0,13)],[new TalentNeedVO(0,20)]]);
         this._learns[this._learns.length] = new TalentLearnVO(1205,[[new TalentNeedVO(0,8),new TalentNeedVO(1203,1)],[new TalentNeedVO(0,15)],[new TalentNeedVO(0,22)]]);
         this._learns[this._learns.length] = new TalentLearnVO(1206,[[new TalentNeedVO(0,8),new TalentNeedVO(1203,1)],[new TalentNeedVO(0,15)],[new TalentNeedVO(0,22)]]);
         this._learns[this._learns.length] = new TalentLearnVO(1301,[[new TalentNeedVO(0,1)],[new TalentNeedVO(0,3)],[new TalentNeedVO(0,5)]]);
         this._learns[this._learns.length] = new TalentLearnVO(1302,[[new TalentNeedVO(0,1)],[new TalentNeedVO(0,3)],[new TalentNeedVO(0,5)]]);
         this._learns[this._learns.length] = new TalentLearnVO(1303,[[new TalentNeedVO(0,4),new TalentNeedVO(1301,1),new TalentNeedVO(1302,1)],[new TalentNeedVO(0,8)],[new TalentNeedVO(0,12)]]);
         this._learns[this._learns.length] = new TalentLearnVO(1304,[[new TalentNeedVO(0,6),new TalentNeedVO(1303,1)],[new TalentNeedVO(0,13)],[new TalentNeedVO(0,20)]]);
         this._learns[this._learns.length] = new TalentLearnVO(1305,[[new TalentNeedVO(0,8),new TalentNeedVO(1303,1)],[new TalentNeedVO(0,15)],[new TalentNeedVO(0,22)]]);
         this._learns[this._learns.length] = new TalentLearnVO(1306,[[new TalentNeedVO(0,8),new TalentNeedVO(1303,1)],[new TalentNeedVO(0,15)],[new TalentNeedVO(0,22)]]);
         this._learns[this._learns.length] = new TalentLearnVO(2101,[[new TalentNeedVO(0,1)],[new TalentNeedVO(0,3)],[new TalentNeedVO(0,5)]]);
         this._learns[this._learns.length] = new TalentLearnVO(2102,[[new TalentNeedVO(0,1)],[new TalentNeedVO(0,3)],[new TalentNeedVO(0,5)]]);
         this._learns[this._learns.length] = new TalentLearnVO(2103,[[new TalentNeedVO(0,4),new TalentNeedVO(2101,1),new TalentNeedVO(2102,1)],[new TalentNeedVO(0,8)],[new TalentNeedVO(0,12)]]);
         this._learns[this._learns.length] = new TalentLearnVO(2104,[[new TalentNeedVO(0,6),new TalentNeedVO(2103,1)],[new TalentNeedVO(0,13)],[new TalentNeedVO(0,20)]]);
         this._learns[this._learns.length] = new TalentLearnVO(2105,[[new TalentNeedVO(0,8),new TalentNeedVO(2103,1)],[new TalentNeedVO(0,15)],[new TalentNeedVO(0,22)]]);
         this._learns[this._learns.length] = new TalentLearnVO(2106,[[new TalentNeedVO(0,8),new TalentNeedVO(2103,1)],[new TalentNeedVO(0,15)],[new TalentNeedVO(0,22)]]);
         this._learns[this._learns.length] = new TalentLearnVO(2201,[[new TalentNeedVO(0,1)],[new TalentNeedVO(0,3)],[new TalentNeedVO(0,5)]]);
         this._learns[this._learns.length] = new TalentLearnVO(2202,[[new TalentNeedVO(0,1)],[new TalentNeedVO(0,3)],[new TalentNeedVO(0,5)]]);
         this._learns[this._learns.length] = new TalentLearnVO(2203,[[new TalentNeedVO(0,4),new TalentNeedVO(2201,1),new TalentNeedVO(2202,1)],[new TalentNeedVO(0,8)],[new TalentNeedVO(0,12)]]);
         this._learns[this._learns.length] = new TalentLearnVO(2204,[[new TalentNeedVO(0,6),new TalentNeedVO(2203,1)],[new TalentNeedVO(0,13)],[new TalentNeedVO(0,20)]]);
         this._learns[this._learns.length] = new TalentLearnVO(2205,[[new TalentNeedVO(0,8),new TalentNeedVO(2203,1)],[new TalentNeedVO(0,15)],[new TalentNeedVO(0,22)]]);
         this._learns[this._learns.length] = new TalentLearnVO(2206,[[new TalentNeedVO(0,8),new TalentNeedVO(2203,1)],[new TalentNeedVO(0,15)],[new TalentNeedVO(0,22)]]);
         this._learns[this._learns.length] = new TalentLearnVO(2301,[[new TalentNeedVO(0,1)],[new TalentNeedVO(0,3)],[new TalentNeedVO(0,5)]]);
         this._learns[this._learns.length] = new TalentLearnVO(2302,[[new TalentNeedVO(0,1)],[new TalentNeedVO(0,3)],[new TalentNeedVO(0,5)]]);
         this._learns[this._learns.length] = new TalentLearnVO(2303,[[new TalentNeedVO(0,4),new TalentNeedVO(2301,1),new TalentNeedVO(2302,1)],[new TalentNeedVO(0,8)],[new TalentNeedVO(0,12)]]);
         this._learns[this._learns.length] = new TalentLearnVO(2304,[[new TalentNeedVO(0,6),new TalentNeedVO(2303,1)],[new TalentNeedVO(0,13)],[new TalentNeedVO(0,20)]]);
         this._learns[this._learns.length] = new TalentLearnVO(2305,[[new TalentNeedVO(0,8),new TalentNeedVO(2303,1)],[new TalentNeedVO(0,15)],[new TalentNeedVO(0,22)]]);
         this._learns[this._learns.length] = new TalentLearnVO(2306,[[new TalentNeedVO(0,8),new TalentNeedVO(2303,1)],[new TalentNeedVO(0,15)],[new TalentNeedVO(0,22)]]);
         this._learns[this._learns.length] = new TalentLearnVO(3101,[[new TalentNeedVO(0,3)],[new TalentNeedVO(0,10)],[new TalentNeedVO(0,17)]]);
         this._learns[this._learns.length] = new TalentLearnVO(3102,[[new TalentNeedVO(0,4)],[new TalentNeedVO(0,11)],[new TalentNeedVO(0,18)]]);
         this._learns[this._learns.length] = new TalentLearnVO(3103,[[new TalentNeedVO(0,5)],[new TalentNeedVO(0,12)],[new TalentNeedVO(0,19)]]);
         this._learns[this._learns.length] = new TalentLearnVO(3104,[[new TalentNeedVO(0,6)],[new TalentNeedVO(0,13)],[new TalentNeedVO(0,20)]]);
      }
      
      public function newTalentList() : Array
      {
         var _loc2_:TalentConstVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._args)
         {
            _loc1_[_loc1_.length] = TalentFactory.newTalent(_loc2_.id);
         }
         return _loc1_;
      }
      
      public function findConst(param1:int) : TalentConstVO
      {
         var _loc2_:TalentConstVO = null;
         for each(_loc2_ in this._args)
         {
            if(_loc2_.id == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findLearn(param1:int) : TalentLearnVO
      {
         var _loc2_:TalentLearnVO = null;
         for each(_loc2_ in this._learns)
         {
            if(_loc2_.id == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

