package file
{
   import mogames.ConstData;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.fuben.vo.FubenVO;
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.RoleArgVO;
   
   public class ShanMaiConfig
   {
      
      private static var _instance:ShanMaiConfig;
      
      public var bossArg:RoleArgVO;
      
      public var showTime:int;
      
      public var needTime:int;
      
      public var iceTime:int;
      
      public var baseHurt:int;
      
      public var area:int;
      
      public var speed:int;
      
      public function ShanMaiConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : ShanMaiConfig
      {
         if(!_instance)
         {
            _instance = new ShanMaiConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.showTime = 16;
         this.needTime = 2;
         this.iceTime = 3;
         this.baseHurt = 1300;
         this.area = 300;
         this.speed = 50;
         this.bossArg = new RoleArgVO(166,10000,5000,1590,30,10,250,130,null);
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2700);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(247,16000,1500,0,50,25,150,100,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(248,16000,1500,0,50,25,150,100,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(249,16000,1500,0,50,25,150,100,null)));
         _loc2_.addEnemy(new WaveEnemyVO(1,new RoleArgVO(243,80000,1500,0,50,25,150,100,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(250,16000,1500,0,50,25,150,100,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(247,16000,1500,0,50,25,150,100,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(248,5000,400,0,30,25,150,100,null)));
         _loc2_.addEnemy(new WaveEnemyVO(1,new RoleArgVO(244,80000,1500,0,50,25,150,100,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(249,16000,1500,0,50,25,150,100,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(250,3000,400,0,30,25,150,100,null)));
         _loc2_.addEnemy(new WaveEnemyVO(1,new RoleArgVO(245,80000,1500,0,50,25,150,100,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(247,16000,1500,0,50,25,150,100,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(30,new RoleArgVO(248,3000,400,0,30,25,150,100,null)));
         _loc2_.addEnemy(new WaveEnemyVO(1,new RoleArgVO(246,80000,1500,0,50,25,150,100,null)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
      
      public function newReward(param1:Boolean) : Array
      {
         if(!param1)
         {
            return this.loseReward;
         }
         return this.winReward;
      }
      
      private function get winReward() : Array
      {
         var _loc6_:int = 0;
         var _loc1_:int = ConstData.INT3.v;
         var _loc2_:int = Math.random() * 100 + 1;
         if(_loc2_ <= 80)
         {
            _loc1_ = ConstData.INT2.v;
         }
         var _loc3_:Array = [];
         var _loc4_:FubenVO = FubenConfig.instance().findFuben(606);
         var _loc5_:Array = _loc4_.drops.slice(1);
         var _loc7_:int = 0;
         while(_loc7_ < _loc1_)
         {
            _loc6_ = Math.random() * _loc5_.length;
            _loc3_[_loc3_.length] = _loc5_[_loc6_];
            _loc5_.splice(_loc6_,1);
            _loc7_++;
         }
         return _loc3_;
      }
      
      private function get loseReward() : Array
      {
         return [new BaseRewardVO(10000,30000)];
      }
   }
}

