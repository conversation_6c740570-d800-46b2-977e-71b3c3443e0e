package com.mogames.data
{
   import com.mogames.utils.MathUtil;
   
   public class TimeVO
   {
      
      private var _year:Oint = new Oint();
      
      private var _month:Oint = new Oint();
      
      private var _day:Oint = new Oint();
      
      private var _hour:Oint = new Oint();
      
      private var _min:Oint = new Oint();
      
      private var _sec:Oint = new Oint();
      
      private var _week:Oint = new Oint();
      
      private var _date:Date;
      
      public function TimeVO(param1:String = "2013-05-30 00:00:00")
      {
         super();
         this.timeStamp = param1;
      }
      
      public function set timeStamp(param1:String) : void
      {
         if(param1 == "")
         {
            return;
         }
         var _loc2_:Array = param1.split(" ")[0].split("-");
         MathUtil.saveINT(this._year,_loc2_[0]);
         MathUtil.saveINT(this._month,_loc2_[1]);
         MathUtil.saveINT(this._day,_loc2_[2]);
         _loc2_ = param1.split(" ")[1].split(":");
         MathUtil.saveINT(this._hour,_loc2_[0]);
         MathUtil.saveINT(this._min,_loc2_[1]);
         MathUtil.saveINT(this._sec,_loc2_[2]);
         this._date = new Date(this.year,this.month - 1,this.day,this.hour,this.min,this.sec);
         MathUtil.saveINT(this._week,this._date.getDay());
      }
      
      public function checkInTime(param1:int, param2:int, param3:int, param4:int) : Boolean
      {
         if(this.hour == param1)
         {
            return this.min >= param2;
         }
         if(this.hour == param3)
         {
            return this.min <= param4;
         }
         if(this.hour > param1 && this.hour < param3)
         {
            return true;
         }
         return false;
      }
      
      public function get timeStamp() : String
      {
         return [this.year,this.month,this.day].join("-") + " " + [this.hour,this.min,this.sec].join(":");
      }
      
      public function get hourAndMin() : String
      {
         return this.hour + ":" + this.min;
      }
      
      public function get monthAndDay() : String
      {
         return this.month + "月" + this.day + "日";
      }
      
      public function get year() : int
      {
         return MathUtil.loadINT(this._year);
      }
      
      public function get month() : int
      {
         return MathUtil.loadINT(this._month);
      }
      
      public function get day() : int
      {
         return MathUtil.loadINT(this._day);
      }
      
      public function get hour() : int
      {
         return MathUtil.loadINT(this._hour);
      }
      
      public function get min() : int
      {
         return MathUtil.loadINT(this._min);
      }
      
      public function get sec() : int
      {
         return MathUtil.loadINT(this._sec);
      }
      
      public function get week() : int
      {
         return MathUtil.loadINT(this._week);
      }
      
      public function get date() : Date
      {
         return this._date;
      }
   }
}

