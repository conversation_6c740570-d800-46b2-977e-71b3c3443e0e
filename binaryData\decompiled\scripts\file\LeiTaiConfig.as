package file
{
   import com.mogames.utils.MathUtil;
   import mogames.ConstData;
   import mogames.gameData.base.func.LeiTaiVO;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.fuben.vo.FubenNormalVO;
   import mogames.gameData.role.battle.RoleArgVO;
   
   public class LeiTaiConfig
   {
      
      private static var _instance:LeiTaiConfig;
      
      private var _list:Array;
      
      private var _fubenVO:FubenNormalVO;
      
      public var activeVO:LeiTaiVO;
      
      public function LeiTaiConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : LeiTaiConfig
      {
         if(!_instance)
         {
            _instance = new LeiTaiConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = [];
         this._list[this._list.length] = new LeiTaiVO(new RoleArgVO(432,5000,370,120,24,50,250,0,null,0),500,[new BaseRewardVO(10000,1),new BaseRewardVO(50005,1),new BaseRewardVO(50005,1),new BaseRewardVO(50005,1),new BaseRewardVO(50005,1),new BaseRewardVO(50005,1),new BaseRewardVO(68001,1)]);
         this._list[this._list.length] = new LeiTaiVO(new RoleArgVO(525,15000,600,300,50,50,300,0,null,0),3000,[new BaseRewardVO(10000,1),new BaseRewardVO(50017,1),new BaseRewardVO(50017,1),new BaseRewardVO(50017,1),new BaseRewardVO(68002,1)]);
         this._list[this._list.length] = new LeiTaiVO(new RoleArgVO(622,25000,1500,600,50,50,350,0,null,0),5000,[new BaseRewardVO(10000,1),new BaseRewardVO(50034,1),new BaseRewardVO(50034,1),new BaseRewardVO(50034,1),new BaseRewardVO(68001,1),new BaseRewardVO(68002,1)]);
         this._fubenVO = new FubenNormalVO(200,0,null,null,"");
      }
      
      public function get winRewards() : Array
      {
         var _loc4_:int = 0;
         var _loc1_:Array = [new BaseRewardVO(10000,16666)];
         if(!this.activeVO)
         {
            return _loc1_;
         }
         var _loc2_:int = ConstData.INT1.v;
         if(MathUtil.checkOdds(200))
         {
            _loc2_ = ConstData.INT2.v;
         }
         var _loc3_:Array = this.activeVO.drops.slice(1);
         var _loc5_:int = 0;
         while(_loc5_ < _loc2_)
         {
            _loc4_ = Math.random() * _loc3_.length;
            _loc1_[_loc1_.length] = _loc3_[_loc4_];
            _loc3_.splice(_loc4_,1);
            _loc5_++;
         }
         return _loc1_;
      }
      
      public function get loseRewards() : Array
      {
         return [new BaseRewardVO(10000,6666)];
      }
      
      public function get list() : Array
      {
         return this._list;
      }
      
      public function get fubenVO() : FubenNormalVO
      {
         return this._fubenVO;
      }
   }
}

