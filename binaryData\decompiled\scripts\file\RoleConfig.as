package file
{
   import com.mogames.system.SysTimer;
   import mogames.gameAsset.AssetManager;
   import mogames.gameData.npc.NpcFactory;
   import mogames.gameData.npc.base.ConstNpcVO;
   import mogames.gameData.npc.base.GameNpcVO;
   import mogames.gameData.role.base.RoleInfoVO;
   import mogames.gameData.role.base.SkinVO;
   import mogames.gameData.role.hero.HeroInfoVO;
   
   public class RoleConfig
   {
      
      private static var _instance:RoleConfig;
      
      private var _roles:Vector.<RoleInfoVO>;
      
      private var _skins:Vector.<SkinVO>;
      
      private var _npcs:Vector.<ConstNpcVO>;
      
      public function RoleConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : RoleConfig
      {
         if(!_instance)
         {
            _instance = new RoleConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._roles = new Vector.<RoleInfoVO>();
         this._skins = new Vector.<SkinVO>();
         this._roles[this._roles.length] = new RoleInfoVO(101,0,0,1010,1011,"朴刀兵","","朴刀兵擅长近距离作战！");
         this._roles[this._roles.length] = new RoleInfoVO(102,0,0,1020,1021,"链锤兵","","链锤兵擅长近距离作战！");
         this._roles[this._roles.length] = new RoleInfoVO(103,0,0,1030,1031,"女兵","","女兵擅长近距离作战！");
         this._roles[this._roles.length] = new RoleInfoVO(104,0,0,1040,1041,"武斗兵","","武斗兵擅长近距离作战！");
         this._roles[this._roles.length] = new RoleInfoVO(105,0,0,1050,1051,"蛮兵","","蛮兵擅长近距离作战！");
         this._roles[this._roles.length] = new RoleInfoVO(106,0,0,1060,1061,"骑兵","","骑兵擅长近距离作战！");
         this._roles[this._roles.length] = new RoleInfoVO(107,0,0,1070,1071,"枪兵","","枪兵擅长近距离作战！");
         this._roles[this._roles.length] = new RoleInfoVO(108,0,0,1080,1081,"飞刀兵","","飞刀兵擅长中距离作战！");
         this._roles[this._roles.length] = new RoleInfoVO(109,0,0,1090,1091,"铁缒兵","","铁缒兵擅长近距离作战！");
         this._roles[this._roles.length] = new RoleInfoVO(110,0,0,1100,1101,"弓箭兵","","弓箭兵擅长远距离作战！");
         this._roles[this._roles.length] = new RoleInfoVO(111,0,0,1110,1111,"刺客","","刺客擅长近距离作战！");
         this._roles[this._roles.length] = new RoleInfoVO(112,0,0,1120,1121,"弩兵","","弩兵擅长远距离作战！");
         this._roles[this._roles.length] = new RoleInfoVO(166,0,0,1130,1130,"王大头","","主公");
         this._roles[this._roles.length] = new RoleInfoVO(200,0,0,0,10081,"乱党头目","ICON_ENEMY","乱党老大");
         this._roles[this._roles.length] = new RoleInfoVO(201,0,0,0,2053,"武斗兵首领","SHOULING_WD","武斗兵首领");
         this._roles[this._roles.length] = new RoleInfoVO(202,0,0,0,2054,"蛮兵首领","SHOULING_MB","蛮兵首领");
         this._roles[this._roles.length] = new RoleInfoVO(203,0,0,0,2055,"刺客首领","SHOULING_CK","刺客首领");
         this._roles[this._roles.length] = new RoleInfoVO(204,0,0,0,2056,"朴刀兵首领","SHOULING_PD","朴刀兵首领");
         this._roles[this._roles.length] = new RoleInfoVO(205,0,0,0,2057,"链锤兵首领","SHOULING_LC","链锤兵首领");
         this._roles[this._roles.length] = new RoleInfoVO(206,0,0,0,2058,"枪兵首领","SHOULING_QB","枪兵首领");
         this._roles[this._roles.length] = new RoleInfoVO(207,0,0,0,2059,"铁缒首领","SHOULING_TZ","铁缒首领");
         this._roles[this._roles.length] = new RoleInfoVO(208,0,0,0,2060,"骑兵首领","SHOULING_QIB","骑兵首领");
         this._roles[this._roles.length] = new RoleInfoVO(209,0,0,0,2061,"女兵首领","SHOULING_NVB","女兵首领");
         this._roles[this._roles.length] = new RoleInfoVO(210,0,0,0,2062,"飞刀首领","SHOULING_FD","飞刀首领");
         this._roles[this._roles.length] = new RoleInfoVO(211,0,0,0,2063,"弓兵首领","SHOULING_GB","弓兵首领");
         this._roles[this._roles.length] = new RoleInfoVO(212,0,0,0,2064,"弩兵首领","SHOULING_NB","弩兵首领");
         this._roles[this._roles.length] = new RoleInfoVO(213,0,0,0,2052,"山贼首领","SHOULING_SZ","山贼首领");
         this._roles[this._roles.length] = new RoleInfoVO(214,0,0,0,2051,"黄巾兵首领","SHOULING_HJB","黄巾兵首领");
         this._roles[this._roles.length] = new RoleInfoVO(220,0,0,0,2053,"武斗兵","","武斗兵新手");
         this._roles[this._roles.length] = new RoleInfoVO(221,0,0,0,2090,"黄巾兵","","黄巾兵擅长近距离作战！");
         this._roles[this._roles.length] = new RoleInfoVO(222,0,0,0,2091,"山贼","","山贼擅长近距离作战！");
         this._roles[this._roles.length] = new RoleInfoVO(223,0,0,0,2092,"投石车","","投石车擅长远距离作战！");
         this._roles[this._roles.length] = new RoleInfoVO(231,0,2,0,2011,"长枪僵尸","","");
         this._roles[this._roles.length] = new RoleInfoVO(232,0,2,0,2012,"自爆僵尸","","");
         this._roles[this._roles.length] = new RoleInfoVO(233,0,2,0,2013,"狼牙棒僵尸","","");
         this._roles[this._roles.length] = new RoleInfoVO(234,0,2,0,2014,"弹弓僵尸","","");
         this._roles[this._roles.length] = new RoleInfoVO(235,0,2,0,2015,"大力僵尸","","");
         this._roles[this._roles.length] = new RoleInfoVO(236,0,2,0,2016,"大锤僵尸","","");
         this._roles[this._roles.length] = new RoleInfoVO(237,0,2,0,2017,"火球僵尸","","");
         this._roles[this._roles.length] = new RoleInfoVO(238,0,2,0,2019,"毒液僵尸","","");
         this._roles[this._roles.length] = new RoleInfoVO(240,0,0,0,1145,"重骑精英","CN_JY_ZHONGQI","");
         this._roles[this._roles.length] = new RoleInfoVO(241,0,0,0,1146,"枪骑精英","CN_JY_QIANGQI","");
         this._roles[this._roles.length] = new RoleInfoVO(242,0,0,0,1147,"弓骑精英","CN_JY_GONGQI","");
         this._roles[this._roles.length] = new RoleInfoVO(243,0,1,0,10151,"弯刀首领","CN_SL_WANDAO","");
         this._roles[this._roles.length] = new RoleInfoVO(244,0,1,0,10161,"巨弩首领","CN_SL_JUNU","");
         this._roles[this._roles.length] = new RoleInfoVO(245,0,1,0,10171,"弓箭首领","CN_SL_GONG","");
         this._roles[this._roles.length] = new RoleInfoVO(246,0,1,0,10181,"巨斧首领","CN_SL_FU","");
         this._roles[this._roles.length] = new RoleInfoVO(247,0,0,0,1141,"重骑","","");
         this._roles[this._roles.length] = new RoleInfoVO(248,0,0,0,1142,"长枪骑","","");
         this._roles[this._roles.length] = new RoleInfoVO(249,0,0,0,1143,"弓骑","","");
         this._roles[this._roles.length] = new RoleInfoVO(250,0,0,0,1144,"弩车","","");
         this._roles[this._roles.length] = new RoleInfoVO(251,0,2,0,2031,"长枪僵尸首领","JSSL_QAINGBING","");
         this._roles[this._roles.length] = new RoleInfoVO(252,0,2,0,2032,"自爆僵尸首领","JSSL_ZIBAO","");
         this._roles[this._roles.length] = new RoleInfoVO(253,0,2,0,2033,"狼牙棒僵尸首领","JSSL_LANGYA","");
         this._roles[this._roles.length] = new RoleInfoVO(254,0,2,0,2034,"弹弓僵尸首领","JSSL_DANGONG","");
         this._roles[this._roles.length] = new RoleInfoVO(255,0,2,0,2035,"大力僵尸首领","JSSL_DALI","");
         this._roles[this._roles.length] = new RoleInfoVO(256,0,2,0,2036,"独角鬼王","GG_DUJIAO","");
         this._roles[this._roles.length] = new RoleInfoVO(257,0,2,0,2037,"旱魃","GG_HANBA","");
         this._roles[this._roles.length] = new RoleInfoVO(258,0,2,0,2038,"墓鬼","GG_MUGUI","");
         this._roles[this._roles.length] = new RoleInfoVO(259,0,2,0,2039,"蛇女","GG_SHENV","");
         this._roles[this._roles.length] = new RoleInfoVO(260,0,2,0,2040,"凿齿","GG_ZAOCHI","");
         this._roles[this._roles.length] = new RoleInfoVO(261,0,2,0,2041,"狸力","GG_LILI","");
         this._roles[this._roles.length] = new RoleInfoVO(262,0,1,11271,11271,"董卓","DONG_ZHUO","");
         this._roles[this._roles.length] = new RoleInfoVO(263,0,1,11321,11321,"曹操","CAO_CAO","");
         this._roles[this._roles.length] = new RoleInfoVO(264,0,1,11331,11331,"典韦","DIAN_WEI","");
         this._roles[this._roles.length] = new RoleInfoVO(265,0,2,0,2042,"白虎幼兽","","");
         this._roles[this._roles.length] = new RoleInfoVO(266,0,2,0,2043,"白虎精英","","");
         this._roles[this._roles.length] = new RoleInfoVO(267,0,2,0,2044,"小青龙","","");
         this._roles[this._roles.length] = new RoleInfoVO(268,0,2,0,2045,"青龙精英","","");
         this._roles[this._roles.length] = new RoleInfoVO(269,0,2,0,2046,"玄武幼兽","","");
         this._roles[this._roles.length] = new RoleInfoVO(270,0,2,0,2520,"玄武精英","","");
         this._roles[this._roles.length] = new RoleInfoVO(271,0,2,0,2047,"朱雀精英","","");
         this._roles[this._roles.length] = new RoleInfoVO(272,0,0,0,1151,"铁骑兵","Q_SL_TIEQI","");
         this._roles[this._roles.length] = new RoleInfoVO(273,0,0,0,1152,"狼骑","Q_SL_LANGQI","");
         this._roles[this._roles.length] = new RoleInfoVO(274,0,0,0,1153,"弓骑","Q_SL_GONGQI","");
         this._roles[this._roles.length] = new RoleInfoVO(275,0,0,0,1154,"巫医","Q_SL_WUYI","");
         this._roles[this._roles.length] = new RoleInfoVO(276,0,0,0,1156,"游牧铁骑精英.","Q_SL_TIEQI","");
         this._roles[this._roles.length] = new RoleInfoVO(277,0,0,0,1157,"游牧狼骑精英","Q_SL_LANGQI","");
         this._roles[this._roles.length] = new RoleInfoVO(278,0,0,0,1158,"游牧弓骑精英","Q_SL_GONGQI","");
         this._roles[this._roles.length] = new RoleInfoVO(279,0,0,0,1159,"游牧巫医精英","Q_SL_WUYI","");
         this._roles[this._roles.length] = new RoleInfoVO(290,0,2,0,2018,"叉戟僵尸首领","JSSL_CHAZI","");
         this._roles[this._roles.length] = new RoleInfoVO(291,0,2,0,2020,"大锤僵尸首领","JSSL_DACUI","");
         this._roles[this._roles.length] = new RoleInfoVO(292,0,2,0,2021,"毒僵尸首领","JSSL_TUDU","");
         this._roles[this._roles.length] = new RoleInfoVO(293,0,2,0,2022,"火球僵尸首领","JSSL_HUOQIU","");
         this._roles[this._roles.length] = new RoleInfoVO(298,0,2,0,2023,"黑无常","GG_HEIWUCHANG","");
         this._roles[this._roles.length] = new RoleInfoVO(299,0,2,0,2024,"白无常","GG_BAIWUCHANG","");
         this._roles[this._roles.length] = new RoleInfoVO(740,0,2,0,1224,"鬼头","FS_GUITOU","");
         this._roles[this._roles.length] = new RoleInfoVO(741,0,2,0,1225,"鬼影","FS_GUIYIN","");
         this._roles[this._roles.length] = new RoleInfoVO(742,0,2,0,1224,"鬼头首领","FS_GUITOU","");
         this._roles[this._roles.length] = new RoleInfoVO(743,0,2,0,1225,"鬼影首领","FS_GUIYIN","");
         this._roles[this._roles.length] = new RoleInfoVO(744,0,2,0,1226,"牛鬼","FS_NIUGUI","");
         this._roles[this._roles.length] = new RoleInfoVO(750,0,0,0,1161,"弯刀","","");
         this._roles[this._roles.length] = new RoleInfoVO(751,0,0,0,1162,"远程兽兵","","");
         this._roles[this._roles.length] = new RoleInfoVO(752,0,0,0,1163,"近战兽兵","","");
         this._roles[this._roles.length] = new RoleInfoVO(753,0,0,0,1164,"猛士","","");
         this._roles[this._roles.length] = new RoleInfoVO(754,0,0,0,1165,"藤甲","","");
         this._roles[this._roles.length] = new RoleInfoVO(755,0,0,0,1166,"象兵","","");
         this._roles[this._roles.length] = new RoleInfoVO(760,0,1,0,1161,"弯刀首领","NMSL_WANDAO","");
         this._roles[this._roles.length] = new RoleInfoVO(761,0,1,0,1162,"远程兽兵首领","NMSL_YCSB","");
         this._roles[this._roles.length] = new RoleInfoVO(762,0,1,0,1163,"近战兽兵首领","NMSL_JZSB","");
         this._roles[this._roles.length] = new RoleInfoVO(763,0,1,0,1164,"猛士首领","NMSL_MENGSHI","");
         this._roles[this._roles.length] = new RoleInfoVO(764,0,1,0,1165,"藤甲首领","NMSL_TENGJIA","");
         this._roles[this._roles.length] = new RoleInfoVO(765,0,1,0,1166,"象兵首领","NMSL_XIANGBIN","");
         this._roles[this._roles.length] = new RoleInfoVO(771,0,0,0,1181,"忍者","FS_RENZHE","");
         this._roles[this._roles.length] = new RoleInfoVO(772,0,0,0,1182,"太刀兵","FS_TAIDAO","");
         this._roles[this._roles.length] = new RoleInfoVO(773,0,0,0,1183,"长矛兵","FS_CHANGMAO","");
         this._roles[this._roles.length] = new RoleInfoVO(774,0,0,0,1211,"虾兵","YZ_XIAJ2","");
         this._roles[this._roles.length] = new RoleInfoVO(775,0,0,0,1212,"蟹将","YZ_XIEJ2","");
         this._roles[this._roles.length] = new RoleInfoVO(776,0,0,0,1213,"龟蛋","YZ_GUIJ2","");
         this._roles[this._roles.length] = new RoleInfoVO(777,0,1,0,1221,"虾精队长","YZ_XIAJ2","");
         this._roles[this._roles.length] = new RoleInfoVO(778,0,1,0,1222,"蟹精队长","YZ_XIEJ2","");
         this._roles[this._roles.length] = new RoleInfoVO(779,0,1,0,1223,"龟精队长","YZ_GUIJ2","");
         this._roles[this._roles.length] = new RoleInfoVO(781,0,1,0,1191,"忍者首领","FS_RENZHE","");
         this._roles[this._roles.length] = new RoleInfoVO(782,0,1,0,1192,"太刀首领","FS_TAIDAO","");
         this._roles[this._roles.length] = new RoleInfoVO(783,0,1,0,1193,"长矛首领","FS_CHANGMAO","");
         this._roles[this._roles.length] = new RoleInfoVO(785,0,2,0,1200,"河妖","FS_HEYAO","");
         this._roles[this._roles.length] = new RoleInfoVO(786,0,2,0,1201,"毒蛤蟆","FS_DUHAMA","");
         this._roles[this._roles.length] = new RoleInfoVO(787,0,2,0,1202,"百面鬼","FS_BAIMIANGUI","");
         this._roles[this._roles.length] = new RoleInfoVO(795,0,2,0,1200,"河妖首领","FS_HEYAO","");
         this._roles[this._roles.length] = new RoleInfoVO(796,0,2,0,1201,"毒蛤蟆首领","FS_DUHAMA","");
         this._roles[this._roles.length] = new RoleInfoVO(797,0,2,0,1202,"百面鬼首领","FS_BAIMIANGUI","");
         this._roles[this._roles.length] = new RoleInfoVO(798,0,2,0,1203,"鬼王","FS_GUIWANG","");
         this._roles[this._roles.length] = new RoleInfoVO(801,0,0,0,8001,"镖车","","");
         this._roles[this._roles.length] = new RoleInfoVO(802,0,1,0,11600,"孙权","","");
         this._roles[this._roles.length] = new RoleInfoVO(810,0,1,0,10151,"车纽单于","XIONGNU1","");
         this._roles[this._roles.length] = new RoleInfoVO(811,0,1,0,10161,"车犁单于","XIONGNU2","");
         this._roles[this._roles.length] = new RoleInfoVO(812,0,1,0,10171,"呼揭单于","XIONGNU3","");
         this._roles[this._roles.length] = new RoleInfoVO(813,0,1,0,10201,"迷当","MIDANG","");
         this._roles[this._roles.length] = new RoleInfoVO(814,0,1,0,10211,"强瑞","QIANGRUI","");
         this._roles[this._roles.length] = new RoleInfoVO(815,0,1,0,10221,"雅丹","YADAN","");
         this._roles[this._roles.length] = new RoleInfoVO(816,0,1,0,10231,"越吉","YUEJI","");
         this._roles[this._roles.length] = new RoleInfoVO(817,0,1,0,10232,"彻里吉","QIELIJI","");
         this._roles[this._roles.length] = new RoleInfoVO(821,0,1,0,1156,"游牧铁骑首领","Q_SL_TIEQI","");
         this._roles[this._roles.length] = new RoleInfoVO(822,0,1,0,1157,"游牧狼骑首领","Q_SL_LANGQI","");
         this._roles[this._roles.length] = new RoleInfoVO(823,0,1,0,1158,"游牧弓骑首领","Q_SL_GONGQI","");
         this._roles[this._roles.length] = new RoleInfoVO(824,0,1,0,1159,"游牧巫医首领","Q_SL_WUYI","");
         this._roles[this._roles.length] = new RoleInfoVO(830,0,1,0,10233,"杨锋","NM_YF","");
         this._roles[this._roles.length] = new RoleInfoVO(831,0,1,0,10234,"忙牙长","NM_MYC","");
         this._roles[this._roles.length] = new RoleInfoVO(832,0,1,0,10235,"阿会喃","NM_AHN","");
         this._roles[this._roles.length] = new RoleInfoVO(833,0,1,0,10236,"带来洞主","NM_DLDZ","");
         this._roles[this._roles.length] = new RoleInfoVO(834,0,1,0,10237,"金环三结","NM_JJSH","");
         this._roles[this._roles.length] = new RoleInfoVO(835,0,1,0,10238,"朵思大王","NM_SDDW","");
         this._roles[this._roles.length] = new RoleInfoVO(836,0,1,0,10239,"孟优","NM_MY","");
         this._roles[this._roles.length] = new RoleInfoVO(837,0,1,0,10240,"兀突骨","NM_WTG","");
         this._roles[this._roles.length] = new RoleInfoVO(841,0,1,0,10250,"伊达政宗","FS_YDZZ","");
         this._roles[this._roles.length] = new RoleInfoVO(842,0,1,0,10251,"毛利元就","FS_MLYJ","");
         this._roles[this._roles.length] = new RoleInfoVO(843,0,1,0,10252,"真田信繁","FS_ZTXF","");
         this._roles[this._roles.length] = new RoleInfoVO(844,0,1,0,10253,"德川家康","FS_DCJK","");
         this._roles[this._roles.length] = new RoleInfoVO(845,0,1,0,10254,"丰臣秀吉","FS_FCXJ","");
         this._roles[this._roles.length] = new RoleInfoVO(846,0,1,0,10255,"织田信长","FS_ZTXC","");
         this._roles[this._roles.length] = new RoleInfoVO(847,0,1,0,10256,"上杉谦信","FS_SSQX","");
         this._roles[this._roles.length] = new RoleInfoVO(848,0,1,0,10257,"武田信玄","FS_WTXX","");
         this._roles[this._roles.length] = new RoleInfoVO(849,0,1,0,10258,"苏贞精","YZ_SZC1","");
         this._roles[this._roles.length] = new RoleInfoVO(850,0,1,0,10259,"田鼠精","YZ_TSJ1","");
         this._roles[this._roles.length] = new RoleInfoVO(851,0,1,0,10260,"岩石精","YZ_YSJ1","");
         this._roles[this._roles.length] = new RoleInfoVO(852,0,1,0,10261,"妖树精","YZ_YSJ3","");
         this._npcs = new Vector.<ConstNpcVO>();
         this._npcs[this._npcs.length] = new ConstNpcVO(900,"酒馆老板娘","ICON_TAVERN_BOSS","这位客官，想吃点什么喝点什么跟我说哈！");
         this._npcs[this._npcs.length] = new ConstNpcVO(901,"铁匠大牛","ICON_TIE_JIANG","这位大人，如果你对打造装备一窍不通，又想得到一件神兵利器，找在下就算找对人了。只要出的起钱，东西包您满意！");
         this._npcs[this._npcs.length] = new ConstNpcVO(902,"酿造师杜康","ICON_DU_KANG","天下美酒，唯我独酿！这位兄台，尝尝我酿造的美酒，在行军打仗中还有意想不到的效果呢。");
         this._npcs[this._npcs.length] = new ConstNpcVO(903,"店小二","ICON_XIAO_ER","客官您里边儿请！要找人吗？只要您有此人的信物，我就能帮您找到他！");
         this._npcs[this._npcs.length] = new ConstNpcVO(904,"于吉","ICON_YU_JI","遇到妖魔鬼怪了？来来来，老夫这里有些好东西，对付那些鬼怪必定事半功倍！");
         this._npcs[this._npcs.length] = new ConstNpcVO(905,"郭达","ICON_GUO_DA","如果你想征服天下，成就最强霸业，就来我这！只要你有真金白银，什么样的神兵利器都能做。其他小事就去找我徒弟！");
         this._npcs[this._npcs.length] = new ConstNpcVO(906,"于夫人","ICON_CARD_NPC","装备亦有灵魂，来我这，嵌上一些特殊卡片，你的装备有了灵魂，你才能征战不休，势不可挡！");
         this._npcs[this._npcs.length] = new ConstNpcVO(907,"发爷","ICON_FA_YE","各位老板，新店开张，试试手气，玩的尽兴！");
         this._npcs[this._npcs.length] = new ConstNpcVO(908,"神秘人","ICON_SHENMI_REN","只要给我元神，我就用神通帮你创出奇珍异宝！");
         this._roles[this._roles.length] = new RoleInfoVO(1000,4001,3,2500,2501,"白虎","BAI_HU","四大神兽之一。");
         this._roles[this._roles.length] = new RoleInfoVO(1001,4002,3,2510,2511,"青龙","QING_LONG","四大神兽之一。");
         this._roles[this._roles.length] = new RoleInfoVO(1002,4003,3,2520,2520,"玄武","XUAN_WU","四大神兽之一。");
         this._roles[this._roles.length] = new RoleInfoVO(1003,4004,3,2530,2530,"朱雀","ZHU_QUE","四大神兽之一。");
         this._roles[this._roles.length] = new RoleInfoVO(1004,4005,3,2540,2540,"火麒麟","HUO_QI_LIN","麒麟出没处，必有祥瑞。");
         this._roles[this._roles.length] = new RoleInfoVO(1005,4006,3,2550,2550,"白泽","BAI_ZE","传说中昆仑山上的神兽，浑身雪白，能说人话，通万物之情。");
         this._roles[this._roles.length] = new HeroInfoVO(300,0,2,107,2,1,1,1003,10070,10071,"关平","GUAN_PING","关平（生卒年不详），东汉末年名将关羽之子，关羽率军攻打樊城时，孙权借机征讨荆州。关平与其父一同被擒，最后被斩于临沮县。");
         this._roles[this._roles.length] = new HeroInfoVO(301,2,0,101,0,1,1,1001,10100,10101,"楼玄","JUNSHI_11","楼玄，字承先，生卒年不详，沛郡蕲县（今安徽宿州东南）人，三国时期吴国官员。吴景帝孙休在位时，任监农御史。");
         this._roles[this._roles.length] = new HeroInfoVO(302,3,0,102,1,1,1,1002,10090,10091,"何进","DAOJIANG_11","何进（？－189年），字遂高，南阳宛（今河南南阳）人。东汉灵帝时外戚，官至大将军。何进的异母妹有宠于灵帝并被立为皇后，他也随之升迁。黄巾起义时，何进为大将军，总镇京师，因及时发现并镇压了马元义的密谋，被封为慎侯。");
         this._roles[this._roles.length] = new HeroInfoVO(303,3,0,104,2,1,1,1003,10070,10071,"眭固","JIANJIANG_11","眭固（？－199年），字白兔，东汉末期人物，蓟州人。原为黑山贼，与于毒、白绕齐名，与袁术合谋共攻东郡，反为曹操所败，后成为张杨部下。");
         this._roles[this._roles.length] = new HeroInfoVO(304,1,3,110,4,1,1,1023,11060,11061,"夏侯渊","XIAHOU_YUAN","夏侯渊（？－219年），字妙才，沛国谯（今安徽亳州）人，东汉末年名将，擅长千里奔袭作战，官至征西将军，封博昌亭侯。");
         this._roles[this._roles.length] = new HeroInfoVO(305,1,0,105,0,1,1,1004,10100,10101,"崔林","JUNSHI_12","崔林（？－244年），字德儒。清河郡东武城（今山东诸城）人。三国时曹魏大臣，中尉崔琰从弟。曹操平定冀州，任命崔林为坞长，后改任御史中丞。");
         this._roles[this._roles.length] = new HeroInfoVO(306,1,0,106,1,1,1,1005,10090,10091,"袁熙","DAOJIANG_12","袁熙（?－207年），字显奕（《后汉书》、《东光世系》作显雍），东汉末年人物，袁绍之子，袁谭之弟，袁尚之兄。");
         this._roles[this._roles.length] = new HeroInfoVO(307,3,1,107,1,1,1,1006,10090,10091,"宋宪","DAOJIANG_21","宋宪，生卒年不详，东汉末吕布部将八健将之一。宋宪勇猛，武力过人");
         this._roles[this._roles.length] = new HeroInfoVO(308,1,2,108,1,1,1,1007,10030,10031,"文聘","WEN_PIN","文聘，生卒年不详，字仲业，南阳宛人，三国时期曹魏名将。本来是荆州刘表的大将。");
         this._roles[this._roles.length] = new HeroInfoVO(309,3,0,109,0,1,1,1008,10100,10101,"袁谭","JUNSHI_13","袁谭（？—205年），字显思（《东光世系》中字显恩，一说显忠），汝南汝阳人。汉末政治人物，大将军袁绍长子，曾任青州刺史。");
         this._roles[this._roles.length] = new HeroInfoVO(310,3,1,102,2,1,1,1009,10070,10071,"公孙范","JIANJIANG_21","公孙范，辽西令支（今河北迁安）人。东汉末年武将，公孙瓒从弟，官至勃海太守。");
         this._roles[this._roles.length] = new HeroInfoVO(311,2,0,110,1,1,1,1010,10090,10091,"孙霸","DAOJIANG_13","孙霸（？―250年），字子威，吴郡富春（今浙江富阳）人，吴大帝孙权第四子，孙和之弟。");
         this._roles[this._roles.length] = new HeroInfoVO(312,1,3,111,2,1,1,1021,11000,11001,"曹操","CAO_CAO","曹操（155年－220年3月15日），字孟德，一名吉利，小字阿瞒，沛国谯县（今安徽亳州）人。东汉末年杰出的政治家、军事家、文学家、书法家，三国中曹魏政权的奠基人。");
         this._roles[this._roles.length] = new HeroInfoVO(313,2,1,105,0,1,1,1011,10100,10101,"祖茂","JUNSHI_21","祖茂，东汉末年武将。孙坚的心腹将领。坚常著赤罽帻，乃脱帻令亲近将祖茂著之。");
         this._roles[this._roles.length] = new HeroInfoVO(314,3,0,112,3,1,1,1013,10080,10081,"波才","QIANGJIANG_11","波才，黄巾军高级将领，统领东方黄巾军部队。曾在黄巾起义初期取得一定战果，但在长社之战败北。");
         this._roles[this._roles.length] = new HeroInfoVO(315,2,0,104,4,1,1,1014,10110,10111,"全纪","GONGJIANG_11","全纪，三国时期孙吴势力人物，时任东吴黄门侍郎，是全尚之子，东吴名将全琮之侄孙。");
         this._roles[this._roles.length] = new HeroInfoVO(316,0,0,102,1,1,1,1015,10090,10091,"黄崇","DAOJIANG_11","黄崇（？—263）三国时巴西阆中（今四川阆中西）人，蜀汉官员，黄权之子，字不详。");
         this._roles[this._roles.length] = new HeroInfoVO(317,3,0,107,0,1,1,1016,10100,10101,"刘和","JUNSHI_12","刘和，东汉汉人，出生于东海郯县，父亲是刘虞，职位是侍中。");
         this._roles[this._roles.length] = new HeroInfoVO(318,3,0,109,3,1,1,1017,10080,10081,"何仪","QIANGJIANG_12","何仪，生卒年不详，东汉时期人物，曾参与黄巾起义。黄巾之乱后，一直占壉汝南与颍川一带。");
         this._roles[this._roles.length] = new HeroInfoVO(319,1,3,106,0,1,1,1022,11020,11021,"郭嘉","GUO_JIA","郭嘉（170年－207年），字奉孝，颍川阳翟（今河南禹州）人。东汉末年曹操帐下著名谋士。史书上称他“才策谋略，世之奇士”。曹操称赞他见识过人，是自己的“奇佐”。");
         this._roles[this._roles.length] = new HeroInfoVO(320,0,1,101,1,1,1,1018,10090,10091,"赵统","DAOJIANG_22","赵统，生卒年不详。常山真定（今为河北正定）人，陈寿在正史《三国志》中记载赵云去世后，赵统袭爵永昌亭侯，官至蜀汉虎贲中郎督，加行领军。");
         this._roles[this._roles.length] = new HeroInfoVO(321,0,1,102,2,1,1,1019,10070,10071,"诸葛瞻","JIANJIANG_22","诸葛瞻（227年7月—263年11月），字思远，琅邪阳都（今山东沂南县南）人。三国时期蜀汉大臣，蜀汉丞相诸葛亮之子。");
         this._roles[this._roles.length] = new HeroInfoVO(322,1,1,105,4,1,1,1002,10110,10111,"吕虔","GONGJIANG_22","吕虔，生卒年不详，字子恪。任城（今山东济宁东南）人[1]  。三国时期曹魏将领。");
         this._roles[this._roles.length] = new HeroInfoVO(323,1,1,106,3,1,1,1003,10080,10081,"曹纯","QIANGJIANG_21","曹纯（170年－210年），字子和，沛国谯（今安徽亳州）人。东汉末年曹操麾下将领，曹仁之弟。");
         this._roles[this._roles.length] = new HeroInfoVO(324,2,1,107,2,1,1,1004,10070,10071,"全琮","JIANJIANG_23","全琮（183年－249年），字子璜，吴郡钱唐（今浙江杭州西）人，三国时期吴国名将。");
         this._roles[this._roles.length] = new HeroInfoVO(325,2,1,108,1,1,1,1005,10090,10091,"孙瑜","DAOJIANG_23","孙瑜（177年－215年），字仲异，吴郡富春（今浙江富阳）人，孙坚之弟孙静的次子，孙权的堂兄。官至奋威将军、丹杨太守。");
         this._roles[this._roles.length] = new HeroInfoVO(326,3,1,109,3,1,1,1006,10080,10081,"侯成","QIANGJIANG_22","侯成，东汉末年武将。曾任吕布部将八健将之一。");
         this._roles[this._roles.length] = new HeroInfoVO(327,0,0,111,1,1,1,1008,10090,10091,"范疆","DAOJIANG_13","蜀汉车骑将军张飞帐下将。蜀章武元年，刘备伐吴，张飞率军从阆中前往江州，出发前，张达与范疆斩S张飞，带着张飞的首级投奔了东吴。");
         this._roles[this._roles.length] = new HeroInfoVO(328,0,0,110,2,1,1,1009,10070,10071,"张达","JIANJIANG_13","三国时蜀张飞部将。蜀章武元年，刘备伐吴，张飞率军从阆中前往江州，出发前，张达与范疆斩S张飞，带着张飞的首级投奔了东吴。");
         this._roles[this._roles.length] = new HeroInfoVO(329,0,0,109,3,1,1,1010,10080,10081,"郝萌","QIANGJIANG_13","郝萌（?－196），东汉末年人物，吕布部将。");
         this._roles[this._roles.length] = new HeroInfoVO(330,0,0,108,2,1,1,1011,10070,10071,"刘禅","JIANJIANG_11","刘禅（shàn）（207年－271年），即蜀汉孝怀皇帝，又称后主。字公嗣，小名阿斗。蜀汉昭烈帝刘备之子，母亲是昭烈皇后甘氏，三国时期蜀汉第二位皇帝。");
         this._roles[this._roles.length] = new HeroInfoVO(331,1,0,107,1,1,1,1012,10090,10091,"董衡","DAOJIANG_11","曹操部将庞德的副将，与同为副将的董超是否为亲属关系不明。");
         this._roles[this._roles.length] = new HeroInfoVO(332,1,0,106,0,1,1,1013,10100,10101,"郭奕","JUNSHI_12","郭奕，字伯益，颍川阳翟（今河南禹州）人。三国时期魏臣，军师祭酒郭嘉之子，官至太子文学。");
         this._roles[this._roles.length] = new HeroInfoVO(333,2,0,105,2,1,1,1014,10070,10071,"孙亮","JIANJIANG_12","孙亮（243年－260年），字子明，三国时期吴国的第二位皇帝，公元252－258年在位。母潘皇后，他是吴大帝孙权最小的儿子（七子）。");
         this._roles[this._roles.length] = new HeroInfoVO(334,3,0,104,2,1,1,1016,10070,10071,"潘凤","JIANJIANG_13","冀州牧韩馥部下的上将。当十八路诸侯讨伐董卓之时，他奉韩馥之命前往汜水关前挑战董卓部下大将华雄，不敌被斩。");
         this._roles[this._roles.length] = new HeroInfoVO(335,3,0,102,0,1,1,1017,10100,10101,"陈琳","JUNSHI_13","陈琳（？－217年），字孔璋，广陵射阳人[1]  。东汉末年著名文学家，“建安七子”之一。生年无确考，惟知在“建安七子”中比较年长，约与孔融相当。");
         this._roles[this._roles.length] = new HeroInfoVO(336,3,0,102,1,1,1,1019,10090,10091,"郭汜","DAOJIANG_12","郭汜[sì]（？－197年），又名郭多，凉州张掖（今甘肃张掖西北）人，东汉末年将领、军阀，献帝时权臣。");
         this._roles[this._roles.length] = new HeroInfoVO(337,3,0,105,1,1,1,1003,10090,10091,"张让","DAOJIANG_13","张让（？—189年9月24日），东汉宦官，颍川（今河南禹县）人。桓帝、灵帝时，历为小黄门、中常侍等职，封列侯。在职时以搜刮暴敛、骄纵贪婪见称，灵帝极为宠信，常谓“张常侍是我父”。");
         this._roles[this._roles.length] = new HeroInfoVO(338,3,1,104,0,1,1,1005,10100,10101,"王允","JUNSHI_22","王允（137年－192年），字子师，太原祁（今山西祁县）人。东汉末年大臣。王允出身官宦世家。他十九岁就开始任公职，壮年时任豫州刺史。");
         this._roles[this._roles.length] = new HeroInfoVO(339,0,0,109,3,1,1,1010,10080,10081,"乱党头目","ICON_ENEMY","乱党老大");
         this._roles[this._roles.length] = new HeroInfoVO(340,2,1,112,0,1,1,1014,10100,10101,"张昭","JUNSHI_23","东汉末年，张昭为避战乱而南渡至扬州。孙策创业时，任命其为长史、抚军中郎将，将文武之事都委任于张昭。孙策临S前，将其弟孙权托付给张昭，张昭率群僚辅立孙权，并安抚百姓、讨伐叛军，帮助孙权稳定局势。");
         this._roles[this._roles.length] = new HeroInfoVO(341,3,2,107,1,1,1,1016,10030,10031,"张任","ZHANG_REN","张任（？－213年），益州蜀郡（治今四川省成都市）人，东汉末年益州牧刘璋的属下，官至益州从事。");
         this._roles[this._roles.length] = new HeroInfoVO(342,3,0,101,2,1,1,1001,10070,10071,"雷薄","JIANJIANG_11","雷薄，生卒年不详，本为东汉末年袁术麾下部将。离开称帝后昏庸奢侈的袁术，与陈兰一起占据嵩山为山贼。后来袭击兵败的袁术，抢夺财宝。");
         this._roles[this._roles.length] = new HeroInfoVO(343,3,0,102,2,1,1,1002,10070,10071,"陈兰","JIANJIANG_12","陈兰（？－209），《后汉书》作陈简，庐江（治今安徽庐江）人，东汉末年人物。本为袁术部将，后反叛袁术，落草为寇。");
         this._roles[this._roles.length] = new HeroInfoVO(344,3,0,104,1,1,1,1003,10090,10091,"张济","DAOJIANG_11","张济（?—196年），武威祖厉（今甘肃靖远东南）人。东汉末年割据军阀之一。原为董卓部将，董卓被诛斩后，张济与李傕一同率军攻破长安，任中郎将。不久，升任镇东将军，封平阳侯，出屯弘农。");
         this._roles[this._roles.length] = new HeroInfoVO(345,2,0,105,4,1,1,1004,10110,10111,"苏飞","GONGJIANG_12","苏飞，原为东汉末年荆州牧刘表的部将，任江夏都督。");
         this._roles[this._roles.length] = new HeroInfoVO(346,2,0,106,3,1,1,1005,10080,10081,"梁绪","QIANGJIANG_11","梁绪，三国时期人物，梁虔之兄、蜀汉大鸿胪。原为曹魏的天水功曹，后随姜维降蜀汉。官至大长秋。");
         this._roles[this._roles.length] = new HeroInfoVO(347,0,0,107,1,1,1,1006,10090,10091,"糜芳","DAOJIANG_12","麋（mí）芳（生卒年不详），字子方，东海朐（qú）县（今江苏连云港）人。本为徐州牧陶谦部下，后与其兄糜竺拒绝曹操任命，一同投奔刘备。");
         this._roles[this._roles.length] = new HeroInfoVO(348,0,0,108,3,1,1,1007,10080,10081,"龚景","QIANGJIANG_12","龚景（gōng jǐng），汉官员，官至青州太守。");
         this._roles[this._roles.length] = new HeroInfoVO(349,1,0,109,3,1,1,1008,10080,10081,"夏侯和","QIANGJIANG_13","夏侯和，生卒年不详，字义权，沛国谯（今安徽亳州）人，名将夏侯渊第七子。三国时期魏臣，魏元帝时任相国左司马、侍郎。");
         this._roles[this._roles.length] = new HeroInfoVO(350,1,0,110,2,1,1,1009,10070,10071,"秦朗","JIANJIANG_13","秦朗（生卒年不详），字元明，小字阿稣（又作阿苏），新兴（治今山西忻州）云中人。三国时期曹魏将领，官至骁骑将军、给事中，曾率兵讨伐鲜卑轲比能和步度根的叛军。");
         this._roles[this._roles.length] = new HeroInfoVO(351,3,3,109,1,1,1,1001,10030,10031,"颜良","YAN_LIANG","颜良（？－200年），东汉末年河北军阀袁绍部将，以勇而闻名。");
         this._roles[this._roles.length] = new HeroInfoVO(352,3,3,102,3,1,1,1002,10020,10021,"文丑","WEN_CHOU","文丑（？－200年），东汉末年冀州牧袁绍帐下的大将。");
         this._roles[this._roles.length] = new HeroInfoVO(353,3,2,104,2,1,1,1011,10010,10011,"皇甫嵩","HUANGFU_SONG","皇甫嵩（？—195年），字义真。安定郡朝那县（今宁夏彭阳）人。东汉末期名将，雁门太守皇甫节之子，度辽将军皇甫规之侄，出身于将门世家。最初举孝廉、茂才，汉灵帝时被征为侍郎，迁任北地太守。");
         this._roles[this._roles.length] = new HeroInfoVO(354,3,2,105,2,1,1,1010,10010,10011,"韩遂","HAN_SUI","韩遂（？－215年），字文约。凉州金城郡人。东汉末年军阀、将领，汉末群雄之一。");
         this._roles[this._roles.length] = new HeroInfoVO(355,3,2,106,4,1,1,1005,10050,10051,"成公英","CHENGGONG_YIN","成公英，复姓成公，名英。东汉末金城（治今甘肃永靖西北）人。中平末，从韩遂为其心腹。建安中，韩遂兵败华阴还湟中，部众散去，唯他相随。");
         this._roles[this._roles.length] = new HeroInfoVO(356,1,3,102,2,1,1,1026,11070,11071,"乐进","LE_JIN","乐进（？－218年），字文谦，阳平卫国（今河南清丰）人。东汉末年名将。以胆识英烈而从曹操，随军多年，南征北讨，战功无数。");
         this._roles[this._roles.length] = new HeroInfoVO(357,1,3,109,3,1,1,1024,11010,11011,"曹仁","CAO_REN","曹仁（168年－223年），字子孝，汉族，沛国谯（今安徽亳州）人，曹操从弟（从祖弟）。三国曹魏名将。");
         this._roles[this._roles.length] = new HeroInfoVO(358,0,3,107,3,1,1,1028,11090,11091,"赵云","ZHAO_YUN","赵云（？－229年），字子龙，常山真定（今河北省正定）人。身长八尺，姿颜雄伟，三国时期蜀汉名将。汉末军阀混战，赵云受本郡推举，率领义从加入白马将军公孙瓒。期间结识了汉室皇亲刘备，但不久之后，赵云因为兄长去世而离开。赵云离开公孙瓒大约七年左右的时间，在邺城与刘备相见，从此追随刘备。");
         this._roles[this._roles.length] = new HeroInfoVO(359,3,3,101,0,1,1,1025,11080,11081,"张角","ZHANG_JIAO","张角（？－184年），钜鹿（治今河北省邢台市巨鹿县）人。中国东汉末年农民起义军“黄巾军”的领袖，太平道的创始人。他因得到道士于吉等人所传《太平清领书》（即《太平经》），遂以宗教救世为己任，利用其中的某些宗教观念和社会政治思想，组织群众，约于灵帝建宁（168－172）初传道。中平元年（184），张角以“苍天已S，黄天当立，岁在甲子，天下大吉”为口号，自称“天公将军”，率领群众发动起义，史称“黄巾起义”。");
         this._roles[this._roles.length] = new HeroInfoVO(360,2,3,103,4,1,0,1030,11100,11101,"小乔","XIAO_QIAO","小桥（180年代－？），本姓桥（小乔为后世误传）。中国东汉末年时期的美女，庐江皖县（今安徽潜山）人。桥公的次女，汉末名将周瑜之妾。");
         this._roles[this._roles.length] = new HeroInfoVO(361,1,0,102,1,1,1,1002,10090,10091,"王朗","DAOJIANG_13","王朗（？－228年），本名王严，字景兴。东海郯（今山东郯城西北）人。汉末至三国曹魏时期重臣、经学家。");
         this._roles[this._roles.length] = new HeroInfoVO(362,3,1,104,1,1,1,1003,10090,10091,"马腾","DAOJIANG_23","马腾（？－212年），字寿成。扶风茂陵（今陕西兴平）人，东汉末年割据凉州一带的军阀，伏波将军马援的后代，马超、马休、马铁之父。");
         this._roles[this._roles.length] = new HeroInfoVO(363,0,2,106,2,1,1,1004,10070,10071,"王平","WANG_PING","王平（？—248年），字子均，巴西宕渠（今四川省渠县东北）人，籍贯益州。三国时蜀汉后期大将，原属曹操，曹操与刘备争汉中，得以投降刘备。诸葛亮第一次北伐时与马谡一同守街亭，之后深受诸葛亮的器重，率领蜀汉的王牌军队无当飞军，多次随诸葛亮北伐。诸葛亮S后镇守汉中，曹爽率领十万大军攻汉中时，被王平所击退。");
         this._roles[this._roles.length] = new HeroInfoVO(364,2,1,107,3,1,1,1005,10080,10081,"文钦","QIANGJIANG_21","文钦（？－257年），字仲若，谯郡（今安徽亳州）人，曹操部将文稷之子[1]  ，三国时期魏国将领。文钦仕魏时官至前将军、扬州刺史。正元二年（255年），文钦与毌丘俭等起兵勤王，讨伐专权的司马师，兵败后投奔吴国，吴国授任他为镇北大将军、幽州牧等，封谯侯。");
         this._roles[this._roles.length] = new HeroInfoVO(365,1,0,108,4,1,1,1006,10110,10111,"夏侯惠","GONGJIANG_11","夏侯惠，生卒年不详，字稚权，沛国谯（今安徽亳州）人。三国时期魏臣，文学家，为夏侯渊第六子。");
         this._roles[this._roles.length] = new HeroInfoVO(366,1,1,109,2,1,1,1007,10070,10071,"曹昂","JIANJIANG_21","曹昂（？－197年），字子脩，沛国谯县人。曹操的长子，刘氏所生，但生母早亡是以由正室丁氏抚养大。聪明且性情刚胆谦和，为曹操所喜爱，二十岁时即举孝廉。建安二年（197年）随曹操出征张绣，因张绣突然袭击，曹昂为救曹操负责断後，与大将典韦一同战S於宛城。魏文帝黄初二年（221年），追封谥号丰悼公，黄初五年（224年）又进一步追封为丰悼王。太和三年（229年）年改谥号丰愍王。");
         this._roles[this._roles.length] = new HeroInfoVO(367,1,1,110,1,1,1,1008,10090,10091,"司马炎","DAOJIANG_21","司马炎（236年－290年），字安世，河内温县（今河南省温县）人，晋朝开国皇帝（265年－290年在位）。晋宣帝司马懿之孙、晋文帝司马昭嫡长子、晋元帝司马睿从父。");
         this._roles[this._roles.length] = new HeroInfoVO(368,0,3,104,3,1,1,1027,11110,11111,"张飞","ZHANG_FEI","张飞（？－221年），字益德，幽州涿郡（今河北省保定市涿州市）人氏，三国时期蜀汉名将。刘备长坂坡败退，张飞仅率二十骑断后，据水断桥，曹军没人敢逼近；与诸葛亮、赵云扫荡西川时，于江州义释严颜；汉中之战时又于宕渠击败张合，对蜀汉贡献极大，官至车骑将军、领司隶校尉，封西乡侯，后被范强、张达刺斩。后主时代追谥为“桓侯”。");
         this._roles[this._roles.length] = new HeroInfoVO(369,0,1,111,3,1,1,1009,10080,10081,"赵广","QIANGJIANG_22","赵广（？—263年），三国时期蜀汉牙门将，赵云的次子，赵统之弟。随姜维前往沓中，官拜牙门将。曹魏司马氏派五路大军伐蜀时，随大将军姜维与魏兵战于疆川口，姜维败绩还守剑阁，赵广于沓中战S。");
         this._roles[this._roles.length] = new HeroInfoVO(370,1,0,112,0,1,1,1010,10100,10101,"华歆","JUNSHI_11","华歆（157年—232年），字子鱼，汉族。平原高唐人（今山东聊城高唐县）。 汉末魏初时名士，曹魏重臣。早年拜太尉陈球为师，与卢植、郑玄、管宁等为同门，又与管宁、邴原共称一龙，华歆为龙头。汉灵帝时华歆被举为孝廉，任郎中，因病去官。又被何进征召为尚书郎。后任豫章太守，甚得民心。孙策破刘繇后，华歆举豫章降，被奉为上宾。");
         this._roles[this._roles.length] = new HeroInfoVO(371,2,1,111,1,1,1,1011,10090,10091,"伍延","DAOJIANG_22","伍延，吴车骑将军、都督。天纪四年，晋军侵吴。吴帝孙皓以车骑将军延为都督，进兵江陵，迎敌杜预。后延出陆路，为晋军所败。延见各军皆败，乃弃城走，被伏兵捉住，缚见预。预曰：“留之无用！”叱令武士斩之。");
         this._roles[this._roles.length] = new HeroInfoVO(372,2,2,102,3,1,1,1012,10080,10081,"潘璋","PAN_ZHANG","潘璋（？－234年），字文珪，东郡发干（今山东冠县东）人。三国时期吴国将领。潘璋年轻时家贫，跟随孙权后得到其赏识，加上其作战勇猛，不断升迁，其一生为孙权东征西讨，在合肥之战、追擒关羽、夷陵之战、江陵保卫战中多次立下战功。但其为人奢侈贪财。经常设立军市，又劫斩将士以获得财物，但孙权念其有功未予深究。被陈寿盛赞为“江表之虎臣”");
         this._roles[this._roles.length] = new HeroInfoVO(373,2,0,109,3,1,1,1013,10080,10081,"文虎","QIANGJIANG_11","文虎（?－291），沛国谯县（今安徽亳州）人。魏末晋初将领，文钦之子，文鸯之幼弟。毌丘俭发起叛乱时参战，兵败投奔吴国。诸葛诞发起叛乱时，与父兄一同率领吴军参战。当文钦向诸葛诞进谏言被斩后，和二兄文鸯一起跃过城墙，重新投回魏国。");
         this._roles[this._roles.length] = new HeroInfoVO(374,2,0,108,2,1,1,1014,10070,10071,"吴景","JIANJIANG_12","吴景，本吴郡吴县（今江苏苏州）人，后迁居吴郡钱塘（今浙江杭州），孙坚妻子吴夫人（武烈皇后）之弟，孙策和孙权的舅舅，东汉末年将领。吴景因追随孙坚征伐有功，被任命为骑都尉。袁术上表举荐吴景兼任丹杨太守，讨伐前任太守周昕，占据丹杨。");
         this._roles[this._roles.length] = new HeroInfoVO(375,1,2,111,1,1,1,1017,10090,10091,"羊祜","YANG_GU","羊祜（221年－278年），字叔子，泰山南城人。著名战略家、政治家和文学家。博学能文，清廉正直，娶之女为妻。曾拒绝曹爽和司马昭的多次征辟，后为朝廷公车征拜。司马昭建五等爵制时以功封为钜平子，与荀勖共掌机密。晋代魏后司马炎有吞吴之心，乃命羊祜坐镇襄阳，都督荆州诸军事。在之后的十年里，羊祜屯田兴学，以德怀柔，深得军民之心；一方面缮甲训卒，广为戎备，做好了伐吴的军事和物质准备，并在吴将陆抗去世后上表奏请伐吴，却遭到众大臣的反对。咸宁四年（278年），羊祜抱病回洛阳，同年十一月病故，并在临终前举荐杜预自代。S后获赠侍中、太傅，谥号“成”。");
         this._roles[this._roles.length] = new HeroInfoVO(376,3,1,101,2,1,1,1015,10070,10071,"张绣","JIANJIANG_22","张绣（？—207年），武威祖厉（今甘肃靖远）人。骠骑将军张济的从子。东汉末年割据宛城的军阀，汉末群雄之一。初随张济征伐，张济S后与刘表联合。后降曹操，因不满其言行而突袭曹操，复与刘表连和。官渡之战前夕，听从贾诩的建议再次投降曹操，参加官渡之战，官至破羌将军，封宣威侯。在北征乌桓（207年）途中去世（一说为曹丕逼S），谥定侯。");
         this._roles[this._roles.length] = new HeroInfoVO(377,0,1,102,3,1,1,1016,10080,10081,"马忠","QIANGJIANG_23","马忠（？－249年），本名狐笃，字德信。巴西阆中（今四川阆中）人。三国时期蜀汉将领。建安末年，马忠被推举为孝廉，任汉昌长。曾被刘备称为贤才。丞相诸葛亮开府治事，任马忠为门下督。建兴三年（225年），马忠出任牂牁郡太守，讨平牂牁叛乱。后任庲降都督，剿灭豪帅刘胄叛乱。官至镇南大将军、平尚书事，历封博阳亭侯、彭乡侯。");
         this._roles[this._roles.length] = new HeroInfoVO(378,2,3,105,1,1,1,1029,11120,11121,"孙坚","SUN_JIAN","吴始祖武烈皇帝孙坚（155年－191年），字文台，汉族，吴郡富春（今浙江杭州富阳）人，春秋时期军事家孙武的后裔。东汉末年将领、军阀，三国中吴国的奠基人。史书说他“容貌不凡，性阔达，好奇节”。曾参与讨伐黄巾军的战役以及讨伐董卓的战役。后与刘表作战时阵亡。因官至破虏将军，又称“孙破虏”。其子孙权即为孙吴的开国皇帝。孙权称帝后，追谥孙坚为武烈皇帝。");
         this._roles[this._roles.length] = new HeroInfoVO(379,3,3,106,3,1,1,1032,11040,11041,"吕布","LV_BU","吕布（？－198），字奉先，五原郡九原县人，愿为丁原部将，被唆使斩害丁原归附董卓，与董卓誓为父子，后又被司徒王允唆使诛斩董卓。建安三年（198年），吕布先后击败刘备与夏侯惇后，曹操亲自出马征讨吕布，水淹下邳，吕布被部下叛变，城破被俘，被处S。");
         this._roles[this._roles.length] = new HeroInfoVO(380,0,2,106,0,1,1,1018,10040,10041,"马良","MA_LIANG","马良（187年－222年），字季常，因眉毛中有白毛，人称白眉马良，襄阳宜城（今湖北宜城南）人，马谡之兄，三国时期蜀汉官员。");
         this._roles[this._roles.length] = new HeroInfoVO(381,2,0,101,1,1,1,1001,10090,10091,"陶浚","DAOJIANG_12","陶璜之弟。初为徐陵督，合浦太守修允部曲督郭马反，帝皓遣徐陵督濬将七千人从西道，命交州牧兄璜部伍所领及合浦、郁林诸郡兵，当与东西军共击马。天纪元年冬，晋向江陵，龙骧将军王浚，");
         this._roles[this._roles.length] = new HeroInfoVO(382,3,0,102,1,1,1,1002,10090,10091,"张温","DAOJIANG_11","张温（？－191年），字伯慎。东汉末年南阳穰县人。官至司隶校尉、太尉，封互乡侯。");
         this._roles[this._roles.length] = new HeroInfoVO(383,1,1,104,2,1,1,1003,10070,10071,"陈群","JIANJIANG_21","陈群（？－237年），字长文。颍川许昌（今河南许昌东）人。三国时期著名政治家、曹魏重臣，魏晋南北朝选官制度“九品中正制”和曹魏律法《魏律》的主要创始人。东汉太丘长陈寔之孙、大鸿胪陈纪之子。");
         this._roles[this._roles.length] = new HeroInfoVO(384,3,0,105,4,1,1,1004,10110,10111,"王方","GONGJIANG_11","王方，东汉末年董卓部将。董卓S后，李傕、郭汜二将反攻长安，为董卓复仇。王方随同樊稠、李蒙等人陆续加入，后来事迹不详。");
         this._roles[this._roles.length] = new HeroInfoVO(385,1,1,106,2,1,1,1005,10070,10071,"荀恽","JIANJIANG_22","荀恽，生卒年不详，字长倩，颍川颍阴人，三国时期魏国大臣，荀彧之子，荀攸从弟，荀俣、荀诜、荀顗、荀粲之兄。妻为曹操之女安阳公主。");
         this._roles[this._roles.length] = new HeroInfoVO(386,3,1,107,3,1,1,1006,10080,10081,"王浚","QIANGJIANG_21","王濬（206年－286年），《宋书》作王璿（王璇），字士治，小字阿童，弘农郡湖县（今河南灵宝西）人，西晋时期名将。");
         this._roles[this._roles.length] = new HeroInfoVO(387,3,1,108,1,1,1,1007,10090,10091,"焦触","DAOJIANG_22","东汉末年冀州牧袁绍部将，属袁绍次子袁熙。袁绍S后反叛袁氏，自立为幽州刺史，不久后投降曹操，封为列侯。");
         this._roles[this._roles.length] = new HeroInfoVO(388,2,1,109,3,1,1,1008,10080,10081,"陆凯","QIANGJIANG_23","陆凯（198年—269年），字敬风。吴郡吴县（今江苏苏州）人。三国时吴国后期重臣，丞相陆逊之侄、大司马陆抗族兄。");
         this._roles[this._roles.length] = new HeroInfoVO(389,0,1,110,4,1,1,1009,10110,10111,"吴班","GONGJIANG_23","吴班，字元雄，生卒年不详，兖州陈留郡（治今河南省开封市）人。三国时期蜀汉将领。");
         this._roles[this._roles.length] = new HeroInfoVO(390,0,3,101,1,1,1,1033,11130,11131,"关羽","GUAN_YU","关羽（？－220年），本字长生，后改字云长，河东郡解县（今山西运城）人，东汉末年名将，早期跟随刘备辗转各地，曾被曹操生擒，于白马坡斩斩袁绍大将颜良，与张飞一同被称为万人敌。");
         this._roles[this._roles.length] = new HeroInfoVO(391,0,3,104,2,1,1,1035,11030,11031,"刘备","LIU_BEI","刘备(161年-223年6月10日)，字玄德，刘备早期颠沛流离，投靠过多个诸侯，后于赤壁之战与孙权联盟击败曹操，趁势夺取荆州，而后进取益州，建立蜀汉政权。陈寿评刘备机权干略不及曹操，但其弘毅宽厚，知人待士，百折不挠，终成帝业。");
         this._roles[this._roles.length] = new HeroInfoVO(392,2,3,112,3,1,1,1034,11050,11051,"太史慈","TAISHI_CI","太史慈（166年－206年），字子义，东莱黄县（今山东龙口东黄城集）人。东汉末年名将，官至建昌都尉。弓马熟练，箭法精良。原为刘繇部下，后被孙策收降，自此太史慈为孙氏大将，助其扫荡江东。孙权统事后，因太史慈能制刘磐，便将管理南方的要务委托给他。");
         this._roles[this._roles.length] = new HeroInfoVO(393,3,0,101,1,1,1,1002,10030,10031,"耿武","DAOJIANG_11","耿武（？—191）【《九州春秋》作耿彧】，字文威。是东汉末期的人物。据史书记载，耿武是韩馥部将，在韩馥企图让冀州于袁绍时曾和闵纯及李历一同劝谏韩馥，后因在袁绍将来到冀州时谋刺袁绍，袁绍令田丰将其杖斩。");
         this._roles[this._roles.length] = new HeroInfoVO(394,0,1,102,2,1,1,1003,10010,10011,"诸葛乔","JIANJIANG_22","诸葛乔，诸葛亮嗣子，诸葛瑾过继给诸葛亮，字伯松、原字仲慎，官至翊武将军。到蜀国后，任驸马都尉。诸葛亮视他如亲生儿子，深恐其成为庸人，管教甚严。诸葛亮北伐屯驻汉中，让诸葛乔同士兵们—起，参加与督运军粮地艰苦工作。");
         this._roles[this._roles.length] = new HeroInfoVO(395,3,1,104,2,1,1,1004,10010,10011,"袁尚","JIANJIANG_21","袁尚（？－207年），字显甫，汝南汝阳（今河南商水西南）人。汉末群雄之一袁绍的第三子，受到袁绍的偏爱，并于袁绍去世后继承了袁绍的官位和爵位，也因此招致长兄袁谭的怨恨，兄弟之间经常兵戈相向。");
         this._roles[this._roles.length] = new HeroInfoVO(396,0,2,107,1,1,1,1017,10030,10031,"马岱","MA_DAI","马岱，扶风茂陵（今陕西兴平）人。三国时期蜀汉将领，马超的从弟。早年追随马超大战曹操，反攻陇上，围攻成都，汉中之战等。后在诸葛亮病逝后受杨仪派遣斩斩了蜀将魏延。曾率领军队出师北伐，被魏将牛金击败而退还。官至平北将军，陈仓侯。");
         this._roles[this._roles.length] = new HeroInfoVO(397,3,0,105,3,1,1,1005,10080,10081,"梁兴","QIANGJIANG_12","建安十六年，从合马超起兵反抗曹操。梁兴率步骑五千夜袭曹军先头部队徐晃，被击退。联军战败后，梁兴逃到蓝田，劫掠周围郡县。夏侯渊进攻蓝田联合郑浑征讨梁兴，梁兴战败被斩。");
         this._roles[this._roles.length] = new HeroInfoVO(398,0,1,106,0,1,1,1006,10040,10041,"廖立","JUNSHI_21","\t廖立（生卒年不详），字公渊，武陵临沅人，刘备的荆州南部三郡（长沙、桂阳、零陵）被吕蒙偷袭后，廖立脱身奔归刘备，刘备不责备廖立，任他为巴郡太守。但是廖立自恃奇才，公然批评先帝（刘备）一再失策、导致荆州覆灭、关羽身S、夷陵之败损兵折将等等，他还诽谤众臣，最终廖立被废为民，最后在得知诸葛亮的S讯时，廖立郁郁而终。");
         this._roles[this._roles.length] = new HeroInfoVO(399,3,0,107,1,1,1,1018,10090,10091,"韩嵩","DAOJIANG_13","韩嵩，生卒年不详，字德高，义阳（今河南桐柏）人。少好学，贫不改志。因见天下将大乱，遂与好友数人隐居郦西山中。后事刘表为别驾，转从事中郎。汉献帝拜嵩侍中，迁零陵太守。曹操取荆州，征用嵩。嵩患病，在家拜授大鸿胪。");
         this._roles[this._roles.length] = new HeroInfoVO(400,3,2,109,1,1,1,1008,10030,10031,"鲍信","BAO_XIN","鲍信（151年－192年），字允诚，泰山平阳（今山东新泰）人。东汉末年济北相，讨伐董卓的诸路人马之一。鲍信受何进征召在外募兵，回到洛阳时适逢董卓进京，鲍信劝袁绍除掉董卓，袁绍不同意。后袁绍、曹操等人起兵对抗董卓，鲍信也起兵响应。后联盟破裂，鲍信劝戒曹操静观其变。在与黄巾军交战期间，鲍信为救曹操不幸战S，曹操后来追记功绩，赐封其子。");
         this._roles[this._roles.length] = new HeroInfoVO(401,1,0,108,2,1,1,1009,10070,10071,"夏侯劭","JIANJIANG_13","夏侯劭，三国名将夏侯惇曾孙，夏侯廙之子，袭爵高安乡侯。");
         this._roles[this._roles.length] = new HeroInfoVO(402,3,0,109,3,1,1,1010,10080,10081,"卑衍","QIANGJIANG_13","卑衍，三国时公孙渊的部将。公孙渊自立为燕王后，侵扰北方。景初二年（238），渊遣衍与杨祚等屯辽隧，被司马懿将军胡遵击破。衍夜走襄平，与司马懿军相遇于首山。卑衍S战，再次大败。经过此战，司马懿得以进军造城下，为围堑，辽水暴涨，败亡。");
         this._roles[this._roles.length] = new HeroInfoVO(403,2,1,110,2,1,1,1011,10010,10011,"吕据","QIANGJIANG_23","吕据（？-256年），字世议，汝南细阳（今安徽太和）人，三国时期吴国将领，大司马吕范次子。初因父亲功勋，授任郎官。吕范卧病时，任副军校尉，辅助掌管军事。黄武七年（228年），吕范去世，吕据袭封南昌侯爵位，拜安军中郎将。多次征讨山越，每次皆能取胜。随潘濬讨伐五谿，并立战功；随朱然进攻樊城，与朱异攻破樊城外围。回师后任偏将军，入朝任马闲右部督、越骑校尉。");
         this._roles[this._roles.length] = new HeroInfoVO(404,1,3,101,3,1,1,1031,11150,11151,"张颌","ZHANG_HE","张颌（？－231年），河间人。东汉末年，应募参加镇压黄巾起义，后属冀州牧韩馥为军司马。191年，袁绍取冀州，张颌率兵投归，任校尉。因破公孙瓒有功，迁为宁国中郎将。后在官渡之战中投降曹操。此后，随曹操攻乌桓、破马超、降张鲁，屡建战功。");
         this._roles[this._roles.length] = new HeroInfoVO(405,1,1,112,1,1,1,1012,10030,10031,"夏侯威","DAOJIANG_22","夏侯威（生卒年不详，）字季权，沛国谯（今安徽亳州）人。三国时期曹魏将领，征西将军夏侯渊第四子，右将军之弟。夏侯威性格任侠，历荆、兖二州刺史，并与曹丕、曹植等相友善。其时泰山人羊祜，博学能属文，善谈论；夏侯威深异其才，乃以其兄之女嫁于羊祜为妻，後羊祜果成一代名将。");
         this._roles[this._roles.length] = new HeroInfoVO(406,1,1,101,2,1,1,1013,10010,10011,"王戎","JIANJIANG_21","王戎（234年－305年），字濬冲。琅玡临沂（今山东临沂白沙埠镇诸葛村）人。西晋名士、官员，“竹林七贤”之一。王戎出身琅玡王氏。长于清谈，以精辟的品评与识鉴而著称。最初袭父爵贞陵亭侯，被司马昭辟为掾属。历任吏部黄门侍郎、散骑常侍、河东太守、荆州刺史，因事被免，又改任豫州刺史、建威将军。后参与晋灭吴之战，吴国平定后，因功进封安丰县侯。在荆州拉拢士人，颇有成效。");
         this._roles[this._roles.length] = new HeroInfoVO(407,3,1,102,1,1,1,1014,10030,10031,"田楷","DAOJIANG_23","田楷（？－199年），公孙瓒部属、青州刺史。陶谦为曹操所攻，楷率刘备救援。初平二年，瓒与袁绍反目，自率主力攻绍，分兵平青、兖，楷奉瓒命据有齐地。绍与楷连战二年，士卒疲困，粮食并尽，互掠百姓，野无青草。后为袁绍之子袁谭至青州，田楷在与袁谭的交战中败退，逃往幽州，后在易京之战中阵亡。");
         this._roles[this._roles.length] = new HeroInfoVO(408,1,2,110,1,1,1,1011,10030,10031,"曹洪","CAO_HONG","曹洪，曹操从弟，很早就跟随曹操南征北战，数十年战功赫赫，多次就曹操于危难，史书中却与曹丕不和，以至于被免职，直到曹睿时期才复职。");
         this._roles[this._roles.length] = new HeroInfoVO(409,2,0,104,2,1,1,1016,10070,10071,"傅婴","JIANJIANG_12","傅婴，孙翊心腹。孙翊被属下的戴员，妫览，边洪等人谋斩后，与孙翊之妻徐氏，及孙翊旧将孙高合力，斩斩了元凶妫览，戴员等二将，因为此项功绩被封为牙门将，与孙高共守丹阳。");
         this._roles[this._roles.length] = new HeroInfoVO(410,1,1,105,2,1,1,1019,10010,10011,"和洽","JIANJIANG_23","和洽，字阳士，汝南西平（今河南舞阳东南）人。三国时期曹魏大臣。汉末被举为孝廉，初时投刘表。曹操夺取荆州，拜和洽为丞相掾属。");
         this._roles[this._roles.length] = new HeroInfoVO(411,0,0,106,4,1,1,1020,10110,10111,"马延","GONGJIANG_12","马延，蜀汉平北将军马岱之子，官至 振威将军。延熙十九年，随大将军姜维攻打上卦，S于段谷之战。");
         this._roles[this._roles.length] = new HeroInfoVO(412,1,3,105,3,1,1,1036,11140,11141,"于禁","YU_JIN","于禁（？－221年），字文则，泰山钜平（今山东泰安南）人。三国时期魏国武将。本为鲍信部将，后属曹操，曾于张绣造反时讨伐不守军纪的青州兵，同时为迎击敌军而固守营垒，因此曹操称赞他可与古代名将相比。然而在建安二十四年（219年）的襄樊之战中，于禁在败给关羽后投降，致使晚节不保。关羽败亡后，于禁从荆州获释到了吴国。黄初二年（公元221年），孙权遣还于禁回魏，同年去世。");
         this._roles[this._roles.length] = new HeroInfoVO(413,3,0,112,1,1,1,1019,10090,10091,"薛礼","DAOJIANG_12","本为彭城相，后为陶谦所逼，屯兵秣陵，与笮融同奉刘繇为盟主。孙策攻刘繇，笮融、薛礼被击败，薛礼后被笮融所斩。");
         this._roles[this._roles.length] = new HeroInfoVO(414,3,0,111,2,1,1,1018,10070,10071,"宋果","JIANJIANG_13","宋果，字仲乙，扶风郡（今陕西兴平县东南南佐村）人，东汉末年名士。");
         this._roles[this._roles.length] = new HeroInfoVO(415,3,1,110,0,1,1,1016,10100,10101,"许汜","JUNSHI_23","许汜，生卒年不详，襄阳人。东汉末年名士，有国士之名，吕布帐下谋士。兴平元年为兖州从事中郎，与张超、陈宫等背曹操而迎吕布为兖州牧。吕布败亡之后，前往荆州投靠刘表。");
         this._roles[this._roles.length] = new HeroInfoVO(416,0,1,109,4,1,1,1015,10050,10051,"吴懿","GONGJIANG_21","吴懿（又作吴壹）（？－237年），字子远，兖州陈留郡（治今河南省开封市）人。三国时期蜀汉将领，蜀汉穆皇后吴氏兄长。随刘焉入蜀，刘璋时任中郎将。刘备进攻刘璋，吴懿归降刘备，历任讨逆将军、护军、关中都督。后随蜀汉丞相诸葛亮北伐曹魏，并与魏延在阳溪大破魏将郭淮、费曜，升任左将军，进封高阳乡侯。");
         this._roles[this._roles.length] = new HeroInfoVO(417,0,2,107,3,1,1,1017,10020,10021,"李严","LI_YAN","李严（？－234年），后改名李平，字正方，南阳人。三国时期蜀汉重臣，与诸葛亮同为刘备临终前的托孤之臣。公元231年，蜀军北伐时，李严押运粮草因为下雨道路泥泞延误时日，为推卸责任反而怪罪诸葛亮的北伐，使诸葛亮不得不退兵，因而获罪，最终被废为平民，迁徙到梓潼郡（治今四川梓潼）。");
         this._roles[this._roles.length] = new HeroInfoVO(418,3,0,107,1,1,1,1014,10090,10091,"毛炅","DAOJIANG_12","毛炅，汉族，建宁（治今云南曲靖）人，西晋初年的交阯太守。");
         this._roles[this._roles.length] = new HeroInfoVO(419,3,1,106,2,1,1,1013,10070,10071,"周昂","JIANJIANG_23","周昂，会稽人，东汉末年袁绍的部将。任九江太守。曾被袁绍派遣攻夺孙坚所屯阳城，并断孙坚粮草。周昕之弟，周喁之兄。");
         this._roles[this._roles.length] = new HeroInfoVO(420,1,0,105,3,1,1,1012,10080,10081,"徐质","QIANGJIANG_12","徐质（？—254年），使一双大斧，曾任讨蜀护军，颇有战功，斩蜀将张嶷。并多次随魏雍州刺史陈泰出征，拒蜀大将姜维。在襄武S于姜维之手。");
         this._roles[this._roles.length] = new HeroInfoVO(421,0,0,104,1,1,1,1011,10090,10091,"刘永","DAOJIANG_13","刘永，字公寿，涿郡涿县（今河北涿州）人，蜀汉昭烈帝刘备之子，蜀汉后主刘禅之弟。[1]  章武元年（221年）六月，封鲁王。建兴八年（230年），改封甘陵王。咸熙元年（264年），蜀汉灭亡，刘永被迁往洛阳，被任命为奉车都尉，封乡侯。");
         this._roles[this._roles.length] = new HeroInfoVO(422,0,3,101,1,1,1,1039,11170,11171,"魏延","WEI_YAN","刘备入川时魏延因数有战功被任命为牙门将军，刘备攻下汉中后又将其破格提拔为镇远将军，领汉中太守，镇守汉中，成为独当一方的大将。魏延镇守汉中近十年，之后又屡次随诸葛亮北伐，功绩显著。");
         this._roles[this._roles.length] = new HeroInfoVO(423,3,1,101,1,1,1,1009,10090,10091,"杨彪","DAOJIANG_22","杨彪（142年—225年），字文先。弘农郡华阴县（今陕西华阴东）人。东汉末年名臣，太尉杨赐之子、名士杨修之父，出身东汉名门“弘农杨氏”。");
         this._roles[this._roles.length] = new HeroInfoVO(424,3,0,108,2,1,1,1008,10070,10071,"李封","JIANJIANG_11","吕布部属，任治中。兴平二年（195），曹操、吕布相攻，封与薛兰屯巨野，招曹操部将李乾叛降，乾不听，斩之。既而。曹操遣诸将击败封与薛兰。");
         this._roles[this._roles.length] = new HeroInfoVO(425,3,0,102,3,1,1,1007,10080,10081,"郭援","QIANGJIANG_13","郭援（？－202年），沛国（治今安徽濉溪人）。东汉末年人物，袁尚部下，钟繇之甥，袁尚自封其为河东郡太守。");
         this._roles[this._roles.length] = new HeroInfoVO(426,3,1,112,4,1,1,1006,10050,10051,"陈登","GONGJIANG_22","陈登，字元龙，下邳淮浦（今江苏涟水西）人。东汉末年将领、官员。为人爽朗，性格沈静，智谋过人，少年时有扶世济民之志，并且博览群书，学识渊博。");
         this._roles[this._roles.length] = new HeroInfoVO(427,2,2,111,1,1,1,1020,10030,10031,"董袭","DONG_XI","董袭（？—213年），字元代，会稽馀姚（今浙江余姚）人。东汉末年江东孙氏部将。身高八尺，武力过人。孙策进入会稽郡，董袭在高迁亭迎接，孙策见他甚为奇伟，十分欣赏，派他到地方担任门下贼曹。后孙策讨伐山阴贼黄龙罗、周勃，董袭亲自斩斩黄龙罗、周勃二人首级，升为别部司马、扬武都尉。");
         this._roles[this._roles.length] = new HeroInfoVO(428,1,0,110,2,1,1,1005,10070,10071,"吕旷","JIANJIANG_12","吕旷（生卒年不详），与吕翔同是袁绍属下，袁绍去世后，为袁尚守东平，后来投降曹操，并被封为列侯。");
         this._roles[this._roles.length] = new HeroInfoVO(429,0,1,109,3,1,1,1004,10020,10021,"傅彤","QIANGJIANG_21","傅彤（?-222）在正史中名为傅肜，三国时期蜀汉将领，南阳义阳（今湖北枣阳）人。傅彤为蜀汉将军，章武元年为中军护卫，随刘备伐吴。刘备被陆逊火烧连营，傅彤为保护刘备率军断后，S战吴军，因精疲力竭吐血而S。");
         this._roles[this._roles.length] = new HeroInfoVO(430,3,0,108,1,1,1,1003,10090,10091,"李蒙","DAOJIANG_11","李蒙，东汉末年时为董卓部将。逝世于兴平二年（195年）二月。");
         this._roles[this._roles.length] = new HeroInfoVO(431,3,0,107,0,1,1,1002,10100,10101,"耿纪","JUNSHI_13","耿纪（？－218年），字季行，东汉末期人物。祖先为耿弇。少有美名，初为丞相掾，后迁侍中，守少府。建安二十三年（218年）正月，耿纪与太医令吉本、司直韦晃、金祎等发动叛乱，趁夜攻打在许都（今河南许昌）的丞相长史王必，焚烧大门，并射中王必肩膀。最后，被王必和颍川典农中郎将严匡平定，耿纪、吉本等兵败被斩斩。");
         this._roles[this._roles.length] = new HeroInfoVO(432,0,3,112,0,1,0,1038,11190,11191,"黄月英","HUANGYUE_YIN","黄夫人，本名不详，沔阳名士黄承彦之女，诸葛亮之妻。");
         this._roles[this._roles.length] = new HeroInfoVO(433,1,3,105,1,1,1,1037,11180,11181,"典韦","DIAN_WEI","典韦（？－197年），陈留己吾（今河南商丘市宁陵县己吾城村）人。东汉末年曹操部将，相貌魁梧，膂力过人。本属张邈，后归曹操。曹操征讨吕布时被募为陷阵，表现英勇，被拜为校尉，宿卫曹操。建安二年（197年），张绣背叛曹操，典韦为保护曹操而独挡叛军，击斩多人，但最终因寡不敌众而战S。");
         this._roles[this._roles.length] = new HeroInfoVO(434,3,2,110,2,1,1,1019,10010,10011,"高顺","GAO_SHUN","高顺，东汉末年吕布帐下中郎将。史载高顺为人清白有威严，不好饮酒，所统率的部队精锐非常，号称“陷阵营”。屡进忠言于吕布，吕布虽知其忠而不能用。曹操击破吕布后，高顺被曹操所斩");
         this._roles[this._roles.length] = new HeroInfoVO(435,0,0,109,1,1,1,1020,10090,10091,"费诗","DAOJIANG_11","费诗，字公举，生卒年不详，益州犍为郡南安县（今四川省乐山市）人。三国时期蜀汉官员。刘璋占据益州时，以费诗为绵竹县县令。刘备进攻刘璋夺取益州，费诗举城而降，后受拜督军从事，转任牂牁郡太守，再为州前部司马。");
         this._roles[this._roles.length] = new HeroInfoVO(436,2,0,108,2,1,1,1019,10070,10071,"孙登","JIANJIANG_11","孙登（209年－241年），字子高。吴郡富春（今浙江富阳）人。三国孙吴皇太子，吴大帝孙权长子，会稽王孙亮、景帝孙休异母兄。");
         this._roles[this._roles.length] = new HeroInfoVO(437,1,1,107,3,1,1,1018,10020,10021,"诸葛诞","QIANGJIANG_21","诸葛诞（？－258年），字公休，琅邪阳都（今山东沂南）人。三国时期魏国将领，汉司隶校尉诸葛丰之后，蜀汉丞相诸葛亮的族弟。在魏官至征东大将军。曾与司马师一同平定毌丘俭、文钦的叛乱。");
         this._roles[this._roles.length] = new HeroInfoVO(438,1,0,106,1,1,1,1017,10090,10091,"夏侯咸","DAOJIANG_12","夏侯咸（生卒年不详），三国时期魏国将领。曾以军司马之职随钟会伐蜀。在堵截姜维时，夏侯咸等经从剑阁出新都、大渡截其前，姜维遂降");
         this._roles[this._roles.length] = new HeroInfoVO(439,3,1,105,2,1,1,1016,10010,10011,"周旨","JIANJIANG_21","周旨，西晋时期人物，在晋初伐吴时为牙门将。在攻吴战役中，西晋镇南将军杜预伐吴至乐乡，命牙门将管定、伍巢、周旨等伏兵于乐乡城外。吴都督孙歆遣军拒晋将王濬于上流，大败而还。周旨等伏兵，随孙歆军而入，直至帐下，虏获孙歆。故军中为之谣曰：“以计代战一当万。”之后进逼江陵。吴将伍延诈降失败被晋军攻克。");
         this._roles[this._roles.length] = new HeroInfoVO(440,3,0,104,3,1,1,1015,10080,10081,"刘勋","QIANGJIANG_11","刘勋，字子璜，汉京兆虎牙都尉，曾效力过袁绍，后被袁绍所害。");
         this._roles[this._roles.length] = new HeroInfoVO(441,3,1,102,1,1,1,1014,10030,10031,"徐荣","DAOJIANG_21","徐荣（？－192年），玄菟人（辽东襄平人，《公孙度传》中说公孙度本辽东襄平人，迁居玄菟，为同郡徐荣所举，任辽东太守。同郡当是同“玄菟”郡），东汉末年将领。本为中郎将，曾向董卓推举同郡出身的公孙度出任辽东太守。");
         this._roles[this._roles.length] = new HeroInfoVO(442,2,1,101,2,1,1,1013,10010,10011,"吾彦","JIANJIANG_22","吾彦，生卒年不详，字士则，吴郡吴县（今江苏苏州）人。三国时期吴国及西晋初年将领。初任通江县吏，后得到大司马陆抗的提拔重用，逐渐升至建平太守。");
         this._roles[this._roles.length] = new HeroInfoVO(443,3,0,112,3,1,1,1012,10080,10081,"蒋斌","QIANGJIANG_12","蒋斌（？－264年），零陵湘乡（今湖南湘乡）人，蒋琬长子，三国时期蜀汉将领。");
         this._roles[this._roles.length] = new HeroInfoVO(444,3,2,111,2,1,1,1011,10010,10011,"卢植","LU_ZHI","卢植（139年—192年），字子干。涿郡涿县（今河北涿州）人。东汉末年经学家、将领。卢植性格刚毅，师从太尉陈球、大儒马融等，为郑玄、管宁、华歆的同门师兄。曾先后担任九江、庐江太守，平定蛮族叛乱。后与马日磾、蔡邕等一起在东观校勘儒学经典书籍，并参与续写《汉记》。");
         this._roles[this._roles.length] = new HeroInfoVO(445,3,1,110,1,1,1,1010,10030,10031,"公孙康","DAOJIANG_22","公孙康（生卒年不详），辽东襄平（今辽宁辽阳）人。东汉末年辽东地区割据军阀，辽东太守公孙度长子、车骑将军公孙恭之兄。");
         this._roles[this._roles.length] = new HeroInfoVO(446,1,1,109,2,1,1,1009,10010,10011,"朱光","JIANJIANG_22","魏庐江太守。建安中，曹公遣朱光为庐江太守，屯皖，大开稻田，又令间人招诱鄱阳贼帅，使作内应。建安十九年，吕蒙荐甘宁为升城督，督攻在前，蒙以精锐继之。");
         this._roles[this._roles.length] = new HeroInfoVO(447,0,1,108,3,1,1,1008,10020,10021,"张南","QIANGJIANG_21","张南（？－公元222年），字文进，三国时期蜀汉将领。刘备攻伐吴国时，以张南为前部。后刘备被陆逊击败，张南战S。[1] ");
         this._roles[this._roles.length] = new HeroInfoVO(448,3,1,107,1,1,1,1007,10030,10031,"陈应","DAOJIANG_23","陈应，为桂阳太守赵范的部将，与鲍隆并列。刘备命赵云攻去桂阳，鲍隆与陈应自恃武艺过人，出战赵云，被赵云所败。");
         this._roles[this._roles.length] = new HeroInfoVO(449,1,2,106,1,1,1,1006,10030,10031,"曹彰","CAO_ZHANG","曹彰（？－223年），字子文。沛国谯县（安徽亳州）人。三国时期曹魏宗室、将领，魏武帝曹操与武宣卞皇后所生第二子、魏文帝曹丕之弟、陈王曹植之兄。");
         this._roles[this._roles.length] = new HeroInfoVO(450,3,1,105,2,1,1,1005,10010,10011,"许贡","JIANJIANG_23","许贡是东汉末官吏。先后任吴郡都尉、太守，欲送密信给曹操，要曹操注意孙策，却被孙策发现而被斩。");
         this._roles[this._roles.length] = new HeroInfoVO(451,0,1,104,3,1,1,1003,10020,10021,"伊籍","QIANGJIANG_22","伊籍，字机伯，生卒年不详，兖州山阳郡（今山东金乡县）人，三国时期蜀汉官员。年少时依附于同乡刘表。刘备落难到荆州时，伊籍时常拜访，托请刘备照顾。建安十三年（208年），刘表病S，伊籍便转投刘备，一起渡江南下。");
         this._roles[this._roles.length] = new HeroInfoVO(452,3,0,102,1,1,1,1011,10090,10091,"孙秀","DAOJIANG_13","孙秀（？－301），字俊忠，琅琊（今山东临沂）人，西晋大臣。世奉五斗米道，为道徒。少为司马伦小吏，善谄媚，作书疏得伦意，因而得宠。");
         this._roles[this._roles.length] = new HeroInfoVO(453,3,1,101,2,1,1,1008,10010,10011,"公孙修","JIANJIANG_21","公孙修（？－238），三国时期人物，公孙渊之子。随父公孙渊反叛曹魏，后兵败，与公孙渊一同被斩首。");
         this._roles[this._roles.length] = new HeroInfoVO(454,1,3,105,1,1,1,1042,11220,12221,"张辽","ZHANG_LIAO","张辽（169年－222年），字文远，雁门马邑（今山西朔州）人。汉末三国时期曹魏名将。曾从属丁原、董卓、吕布。吕布下邳败亡后，归顺曹操。此后随曹操四处征讨，战功累累。");
         this._roles[this._roles.length] = new HeroInfoVO(455,3,3,111,0,1,1,1040,11200,11201,"华佗","HUA_TUO","华佗（约公元145年－公元208年），字元化，一名旉，沛国谯县人，东汉末年著名的医学家。华佗与董奉、张仲景并称为“建安三神医”。少时曾在外游学，行医足迹遍及安徽、河南、山东、江苏等地，钻研医术而不求仕途。");
         this._roles[this._roles.length] = new HeroInfoVO(456,0,3,110,4,1,0,1041,11210,11211,"关凤","GUAN_FEN","关凤，关羽的女儿。据史书记载，孙权曾经派使者向关羽请求通婚，希望关羽能将女儿嫁给孙权的儿子，但关羽却对使者大加辱骂，因而惹得孙权大怒。在大多数民间传说中，关羽的女儿被称作“关银屏”。");
         this._roles[this._roles.length] = new HeroInfoVO(457,0,3,109,3,1,1,1043,11230,11231,"马超","MA_CHAO","马超(176-222)字孟起，扶风茂陵(今陕西兴平)人。三国时期蜀汉名将。马超是东汉初年伏波将军马援的后人，起初随父亲马腾在西凉为一方军阀，后与韩遂一同进攻潼关，被曹操以离间计击败。此后马超又起兵攻斩凉州刺史韦康，不久被韦康故吏杨阜击败，投奔张鲁。刘备入蜀后马超投奔刘备，并为刘备作前驱，进入成都。刘备称汉中王后拜马超为左将军，假节;公元221年刘备称帝，又进马超为骠骑将军，领凉州牧，封斄乡侯。公元222年，马超病逝，终年47岁。");
         this._roles[this._roles.length] = new HeroInfoVO(458,0,3,108,2,1,1,1044,11240,11241,"刘禅","LIU_CHAN","刘禅（207年－271年），即蜀汉孝怀皇帝，又称后主。字公嗣，小名阿斗。蜀汉昭烈帝刘备之子，母亲是昭烈皇后甘氏，三国时期蜀汉第二位皇帝。");
         this._roles[this._roles.length] = new HeroInfoVO(459,0,2,108,2,1,1,1044,12810,12810,"老者","ICON_COMMON","");
         this._roles[this._roles.length] = new HeroInfoVO(460,0,0,109,0,1,1,1020,10100,10101,"陈震","JUNSHI_11","陈震（？—公元235年），字孝起。荆州南阳郡（今河南南阳）人。三国时期蜀汉官员。刘备领荆州牧时，辟陈震为从事。后随刘备入蜀，为蜀郡北部都尉、汶山太守、犍为太守。建兴三年（225年），拜尚书，迁尚书令。建兴七年（229年），孙权称帝。蜀汉以陈震为卫尉，前往祝贺，与孙权开坛歃盟，交分天下。还蜀，封城阳亭侯。建兴十三年（235年），卒。");
         this._roles[this._roles.length] = new HeroInfoVO(461,1,1,107,1,1,1,1019,10030,10031,"夏侯玄","DAOJIANG_21","夏侯玄（209年—254年），字泰初。沛国谯（今安徽亳州）人。三国时期曹魏玄学家、文学家、官员，征南大将军夏侯尚之子、右将军之侄、大将军曹爽表弟。");
         this._roles[this._roles.length] = new HeroInfoVO(462,2,1,106,2,1,1,1018,10010,10011,"陆景","JIANJIANG_21","陆景（250年～280年），字士仁，吴郡吴县（今苏州）人，东吴丞相陆逊之孙，大司马陆抗次子。陆机、陆云之仲兄。生于吴大帝赤乌十三年（250年），天纪四年（280年），晋伐吴时被晋将张尚马到斩之，年三十一岁。陆景著书数十篇，《隋书·经籍志》注有《陆景集》一卷，已亡佚。");
         this._roles[this._roles.length] = new HeroInfoVO(463,3,0,105,1,1,1,1017,10030,10031,"王昌","DAOJIANG_12","王昌，东汉末李傕部下。");
         this._roles[this._roles.length] = new HeroInfoVO(464,1,0,104,2,1,1,1016,10010,10011,"陈圭","JIANJIANG_13","陈圭，魏国司马。以参谋的身份参加了司马懿平定辽东公孙渊的战役。");
         this._roles[this._roles.length] = new HeroInfoVO(465,3,1,102,2,1,1,1015,10010,10011,"王沈","JIANJIANG_22","王沈（？—266年），字处道，东汉护匈中郎将王柔之孙，东郡太守王机之子，司空王昶之侄。三国曹魏后期至西晋时期大臣、史学家。");
         this._roles[this._roles.length] = new HeroInfoVO(466,0,1,101,0,1,1,1014,10100,10101,"刘巴","JUNSHI_21","刘巴（？－222年），字子初，荆州零陵郡烝阳县（今湖南省衡阳县、邵东县一带）人，汉末三国时期官员、名士。");
         this._roles[this._roles.length] = new HeroInfoVO(467,2,0,112,1,1,1,1013,10030,10031,"周善","DAOJIANG_13","周善，为吴侯孙权的家将，奉命前往荆州接孙夫人回吴，在半路被张飞、赵云所截，被张飞斩S。");
         this._roles[this._roles.length] = new HeroInfoVO(468,1,0,111,2,1,1,1012,10010,10011,"张允","JIANJIANG_12","张允，生卒年不详。东汉末年人物，本为荆州牧刘表的部将，同时也是刘表少子刘琮的支党。后归曹操。");
         this._roles[this._roles.length] = new HeroInfoVO(469,2,2,108,0,1,1,1008,10040,10041,"虞翻","YU_FAN","虞翻（164年－233年），字仲翔，会稽余姚（今浙江余姚）人。日南太守虞歆之子。三国时期吴国学者、官员。他本是会稽太守王朗部下功曹，后投奔孙策，自此仕于东吴。他于经学颇有造诣，尤其精通《易》学。");
         this._roles[this._roles.length] = new HeroInfoVO(470,0,0,110,0,1,1,1010,10100,10101,"尹赏","JUNSHI_12","尹赏，三国时期人物，本为魏天水郡主簿，于诸葛亮初次北伐时降蜀汉，官至执金吾。于蜀汉灭亡前去世。");
         this._roles[this._roles.length] = new HeroInfoVO(471,2,0,109,2,1,1,1009,10010,10011,"于诠","JIANJIANG_13","三国时期吴将。为了增援在魏国发动叛乱的诸葛诞，率领援军出征。虽然魏将王基劝说他投降，但他还是为国尽忠，战S沙场。");
         this._roles[this._roles.length] = new HeroInfoVO(472,3,1,108,1,1,1,1008,10030,10031,"王威","DAOJIANG_22","王威，东汉末年人。荆州刺史刘表部下，乃忠义之士。刘表亡后，刘琮投降曹操，曹操表刘琮为青州刺史，使远离故乡，时只有王威追随。曹操复遣于禁追斩刘琮等人，王威亦于乱军中殉主。");
         this._roles[this._roles.length] = new HeroInfoVO(473,3,1,106,2,1,1,1007,10010,10011,"杨肇","JIANJIANG_23","杨肇，字秀初，荥阳（今河南荥阳）人。晋朝将领。中领军杨暨之子，杨潭、杨歆之父，有才能，任荆州刺史。凤皇元年（272年），西陵督步阐降于晋，吴陆抗伐兵讨之，肇至西陵抵挡陆抗。因晋军在此役中战败，杨肇被免去官职，成为平民。");
         this._roles[this._roles.length] = new HeroInfoVO(474,2,2,107,2,1,1,1012,10010,10011,"陈武","CHEN_WU","陈武（178年－215年），字子烈，庐江郡松滋县（今安徽省宿松县）人，东汉末年孙策、孙权部下的猛将。陈武年轻时仪表堂堂，跟随孙策征战江东，因战功封为别部司马，后来在孙权部下任职。由于陈武仁厚好施，故受到上至孙权，下至乡里的敬爱。陈武又屡建战功，每战皆所向无前，封为偏将军。建安二十年（215）在合肥战役中，张辽奇袭孙权，陈武战S，孙权对此十分哀痛。被陈寿盛赞为“江表之虎臣”。");
         this._roles[this._roles.length] = new HeroInfoVO(475,3,0,105,0,1,1,1005,10100,10101,"荀谌","JUNSHI_13","荀谌，（谌，念chén），字友若，荀彧之兄（一说荀彧之弟），荀绲之子，颍川人。曾任军阀袁绍的幕僚。");
         this._roles[this._roles.length] = new HeroInfoVO(476,3,1,104,4,1,1,1003,10050,10051,"巩志","GONGJIANG_23","巩志，在武陵太守金旋手下担任从事，后来刘备派张飞攻打武陵时，劝金旋降刘但不被接受，金旋败给张飞之后，便一箭射斩金旋投与百姓一起归顺刘备，后刘备任命其为武陵太守。");
         this._roles[this._roles.length] = new HeroInfoVO(477,2,1,102,1,1,1,1011,10030,10031,"陈表","DAOJIANG_23","陈表（204年—237年），字文奥，庐江松滋（今安徽宿松）人。三国时期吴国将领，偏将军陈武庶子，校尉、解烦督陈脩异母弟。");
         this._roles[this._roles.length] = new HeroInfoVO(478,0,1,101,0,1,1,1008,10040,10041,"向朗","JUNSHI_22","向朗（约167年—247年），字巨达。襄阳郡宜城县（今湖北宜城）人，三国时期蜀汉官员、藏书家、学者。");
         this._roles[this._roles.length] = new HeroInfoVO(479,2,3,106,1,1,1,1045,11250,11251,"甘宁","GAN_NIN","甘宁（？—220年），字兴霸，巴郡临江（今重庆忠县）人，三国时期孙吴名将，官至西陵太守，折冲将军。建安十三年（208年），甘宁率部投奔孙权，开始建功立业。先后随孙权破黄祖据楚关，随周瑜攻曹仁取夷陵，随鲁肃镇益阳拒关羽，守西陵、擒朱光，率百余人夜袭曹营，战功赫赫。孙权曾说：“孟德有张辽，孤有甘兴霸，足相敌也。”他为人仗义疏财，深得士卒拥戴，被陈寿盛赞为“江表之虎臣”。建安二十五年（220年），甘宁去世。");
         this._roles[this._roles.length] = new HeroInfoVO(480,2,3,112,4,1,1,1046,11260,11261,"黄盖","HUANG_GAI","黄盖（生卒年不详），字公覆，零陵泉陵（今湖南省永州市零陵区）人。东汉末年名将，历仕孙坚、孙策、孙权三任。早年为郡吏，后追随孙坚走南闯北。孙权即位，诸山越不宾，黄盖活跃在镇抚山越的一线，前后九县，所在悉平，迁丹杨都尉。黄盖为人严肃，善于训练士卒，每每征讨，他的部队皆勇猛善战。");
         this._roles[this._roles.length] = new HeroInfoVO(481,3,3,109,1,1,1,1048,11270,11271,"董卓","DONG_ZHUO","董卓（？－192年5月22日），字仲颖，陇西临洮（今甘肃省岷县）人，生于颍川[1]  。东汉末年献帝时军阀、权臣，官至太师，封郿侯。于桓帝末年先后担任并州刺史，河东太守，利用汉末战乱和朝廷势弱占据京城，废少帝立汉献帝并挟持号令，东汉政权从此名存实亡。");
         this._roles[this._roles.length] = new HeroInfoVO(482,3,3,102,3,1,1,1051,11280,11281,"南华老仙","NANHUA_LAOXIAN","南华老仙，张角的师傅，将三卷天书太平要术传给张角，让他普救世人。");
         this._roles[this._roles.length] = new HeroInfoVO(483,2,3,107,3,1,1,1050,11290,11291,"徐盛","XU_SHENG","徐盛（生卒年不详），字文向，琅邪莒县（今山东莒县）人，三国时吴国名将。早年徐盛抗击黄祖，因功升为中郎将。刘备伐吴时，徐盛跟随陆逊攻下蜀军多处屯营；曹休伐吴时，徐盛在形势不利的情况下以少抗多，成功防御。因前后战功，徐盛先后升任建武将军、安东将军，任庐江太守。后来，曹丕大举攻吴，吴国依徐盛的建议在建业外围筑上围墙，曹丕被迫退走。黄武年间，徐盛病逝，被陈寿盛赞为“江表之虎臣”。其官爵由儿子徐楷继承。");
         this._roles[this._roles.length] = new HeroInfoVO(484,2,0,112,1,1,1,1020,10030,10031,"濮阳兴","DAOJIANG_13","濮阳兴（？-264年），字子元，陈留（治今河南开封）人，三国时期东吴大臣，吴景帝孙休末年至末帝孙皓初年任丞相。孙权时为上虞县令，后升任尚书左曹、五官中郎将、会稽大守。孙休即位，征召为太常卫将军、平军国事，封外黄侯。永安三年（260年），力主建丹杨湖田，事倍功半，百姓大怨。后升任丞相。");
         this._roles[this._roles.length] = new HeroInfoVO(485,1,1,111,2,1,1,1019,10010,10011,"杜袭","JIANJIANG_21","杜袭（生卒年不详），字子绪。颍川郡定陵县（今河南省襄城县）人。东汉末年及三国时曹魏官员。杜袭投奔曹操之后历任西鄂县令、议郎、丞相府军祭酒、侍中、丞相府长史、驸马都尉。魏文帝时期出任督军粮御史、尚书，累封关内侯、武平亭侯。魏明帝时期，出任曹真和司马懿的军师、太中大夫，晋封平阳乡侯。杜袭去世后，获赠少府，谥号定侯。");
         this._roles[this._roles.length] = new HeroInfoVO(486,2,1,110,3,1,1,1018,10020,10021,"张尚","QIANGJIANG_21","张尚（生卒年不详），是广陵郡（今江苏省扬州市）人，为张纮之孙、吴国南郡太守张玄之子，在孙皓时担任侍郎，《江表传》称他有俊才。因为词辩敏捷而闻名，被拔擢升为侍中、中书令。");
         this._roles[this._roles.length] = new HeroInfoVO(487,2,0,109,1,1,1,1017,10030,10031,"孟宗","DAOJIANG_12","孟宗（218年—271年），湖北江夏鄂城（今武汉市武昌区）人，后因避孙皓字讳，改名孟仁，字恭武。少年时，从师南阳李肃读书，后官居吴国司空。素仁孝，二十四孝之一的“哭竹生笋”指的就是孟仁为其母求笋的故事。");
         this._roles[this._roles.length] = new HeroInfoVO(488,1,1,108,2,1,1,1016,10010,10011,"田豫","JIANJIANG_22","田豫（171年－252年），字国让，渔阳雍奴（今天津市武清区东北）人。三国时期曹魏将领。初从刘备，因母亲年老回乡，后跟随公孙瓒，公孙瓒败亡，劝说鲜于辅加入曹操。曹操攻略河北时，田豫正式得到曹操任用，历任颖阴、郎陵令、弋阳太守等。");
         this._roles[this._roles.length] = new HeroInfoVO(489,1,1,107,3,1,1,1015,10020,10021,"钟进","QIANGJIANG_21","魏将，钟繇之弟。随钟繇一起守备长安，夜间发现长安西门起火，他前去救火时，由于缺乏心理准备，他不幸被早已混进城中的马超部将庞德一刀斩于马下。");
         this._roles[this._roles.length] = new HeroInfoVO(490,1,0,106,0,1,1,1014,10100,10101,"张缉","JUNSHI_12","张缉（？—254年），字敬仲，冯翊高陵（今陕西西安高陵）人。凉州刺史张既之子，魏少帝曹芳的岳父。三国后期曹魏大臣，外戚。");
         this._roles[this._roles.length] = new HeroInfoVO(491,0,0,105,1,1,1,1013,10030,10031,"邓良","DAOJIANG_11","邓良，邓芝之子，其字不详，荆州义阳郡新野县人。活跃于蜀汉中后期至西晋初期。蜀汉时官至汉书左选郎，西晋时官至广汉太守。汉司徒邓禹后人。父邓芝，字伯苗，蜀汉中期顶梁柱，官至车骑将军，封阳武亭侯。邓良为家中独子。");
         this._roles[this._roles.length] = new HeroInfoVO(492,3,0,104,2,1,1,1012,10010,10011,"张英","JIANJIANG_13","东汉末扬州刺史刘繇属将。屯当利口以拒袁术，术遣孙贲、吴景击之，未能拔。后为孙策所破。");
         this._roles[this._roles.length] = new HeroInfoVO(493,0,2,102,3,1,1,1008,10020,10021,"关索","GUAN_SUO","蜀汉名将关羽的第三子（毛本）或长子（周本），荆州失陷后逃难到鲍家庄养病，伤愈后听说东吴仇人已S，乃回归蜀国，并随同诸葛亮南征。");
         this._roles[this._roles.length] = new HeroInfoVO(494,3,0,101,0,1,1,1010,10100,10101,"阎象","JUNSHI_11","阎象，东汉末期人物，袁术的主簿。献帝兴平二年（195），手执玉玺的袁术要称帝时，问于部下，只有阎象引用周文王虽拥有三分之二的天下还向殷称臣的故事进行劝谏。却未被采纳。");
         this._roles[this._roles.length] = new HeroInfoVO(495,3,0,112,1,1,1,1009,10030,10031,"费观","DAOJIANG_11","费观，字宾伯，江夏鄳人，生年不祥，为刘璋的女婿，是蜀汉四相之一费祎的族父。以参军随李严据守绵竹，后与李严一同投降刘备。后为巴郡太守、江州都督。建兴元年封都亭侯，加振威将军，为人善於交接。费观小李严二十余岁，跟李严交往时却像同辈一样。卒年三十七岁。");
         this._roles[this._roles.length] = new HeroInfoVO(496,3,1,111,2,1,1,1008,10010,10011,"张松","JIANJIANG_23","张松（？－212年），字子乔[1]  （字永年为演义混淆，实为蜀中另一位名臣彭羕的字）。蜀郡成都（今四川成都）人[1]  。建安十三年（208年），为益州牧刘璋别驾从事，被派遣至曹操处而不为其所存录，因而怀怨恨。回蜀后，劝刘璋与曹操断绝关系，并说璋连好刘备；其后，又说璋迎备以击张鲁，皆为璋所采纳。建安十七年（212年），暗助刘备，为其兄张肃所告发，刘璋怒而将他斩斩。");
         this._roles[this._roles.length] = new HeroInfoVO(497,3,0,110,3,1,1,1007,10020,10021,"尹礼","QIANGJIANG_13","尹礼，又叫尹卢，一名卢儿，汉末三国人物，原本是臧霸、吕布手下的部将，在吕布败S后归顺曹操，被曹操任命为东莞郡太守。");
         this._roles[this._roles.length] = new HeroInfoVO(498,0,2,109,1,1,1,1012,10030,10031,"周仓","ZHOU_CANG","周仓，正史无字，野史记载字元福。其形象为身材高大、黑面虬髯的关西大汉，本是黄巾军出身，关羽千里寻兄之时请求跟随，自此对关羽忠心不二；在听说关羽兵败被斩后，周仓也自刎而S。");
         this._roles[this._roles.length] = new HeroInfoVO(499,3,0,108,2,1,1,1005,10010,10011,"赵范","JIANJIANG_12","赵范，生卒年不详，他是东汉末年荆南桂阳郡太守，刘备攻荆南时投降。后因怀有异心逃走，不知所踪。");
         this._roles[this._roles.length] = new HeroInfoVO(500,2,0,107,3,1,1,1003,10020,10021,"丁封","QIANGJIANG_13","丁封，三国时期吴国将领。于蜀汉灭亡前，为了牵制魏国，丁封与孙异一同进驻汉水流域。后为后将军，较其兄丁奉早亡。");
         this._roles[this._roles.length] = new HeroInfoVO(501,3,0,106,3,1,1,1011,10020,10021,"陈就","QIANGJIANG_12","（？—208）黄祖部将。黄祖对阵东吴。祖令苏飞为大将，陈就、邓龙为先锋。甘宁飞上艨艟，将邓龙砍S。陈就弃船而走。吕蒙追赶，当胸一刀砍翻。");
         this._roles[this._roles.length] = new HeroInfoVO(502,0,1,105,1,1,1,1008,10030,10031,"罗宪","DAOJIANG_21","罗宪（218年—270年），字令则，荆州襄阳（今湖北襄阳）人。西晋开国将领，巴东太守（郡府在永安白帝城）。于蜀汉灭亡后降魏，成功抵御孙吴的入侵，守住入魏国的要冲永安。后仕晋官至冠军将军、假节，封西鄂县侯。泰始六年（270年）去世，谥烈侯。");
         this._roles[this._roles.length] = new HeroInfoVO(503,2,3,104,1,1,1,1052,11300,11301,"周泰","ZHOU_TAI","周泰，字幼平，九江下蔡（今安徽凤台）人。三国时期吴国武将。孙策平定江东时与同郡蒋钦一起加入孙策军，随孙策左右，后孙权爱其为人，向孙策请求让周泰跟随自己。周泰多次于战乱当中保护孙权的安危，身上受的伤多达几十处，就像在皮肤上雕画一样，吴将朱然、徐盛等因此对其拜服。后来孙权为了表彰周泰为了东吴出生入S的功绩，而赐给他青罗伞盖。官至汉中太守、奋威将军，封陵阳侯。S于黄武中年，有子周邵，亦数有战功，S于黄龙二年。");
         this._roles[this._roles.length] = new HeroInfoVO(504,1,3,110,2,1,1,1053,11310,11311,"郭淮","GUO_HUAI","郭淮（？—255年2月23日），字伯济。太原阳曲（今山西太原）人。三国时期魏国名将。东汉末年，郭淮被举为孝廉，先后任平原郡府丞、五官中郎将门下贼曹、丞相兵曹议令史、征西将军司马。征西将军夏侯渊战S时，郭淮收集残兵，与杜袭共推张郃为主将，得以稳定局势。曹魏建立后，获封关内侯，又任镇西长史。诸葛亮伐魏时，郭淮料敌准确，多立战功。正始元年（240年），击退姜维，升任左将军、前将军。嘉平二年（250年），升任车骑将军，进封阳曲侯。正元二年（255年），郭淮去世。追赠大将军，谥号“贞”。\t");
         this._roles[this._roles.length] = new HeroInfoVO(505,3,1,101,1,1,1,1020,10030,10031,"韩浩","DAOJIANG_21","韩浩，字元嗣 ，河内郡（治所在今河南武陟西南）人[2]  ，东汉末年曹操麾下将领。东汉末年天下大乱，韩浩聚徒众护县。");
         this._roles[this._roles.length] = new HeroInfoVO(506,0,1,102,0,1,1,1007,10040,10041,"费伟","JUNSHI_21","费伟,字文伟,与董允齐名，刘禅为太子时二人均为其舍人。禅嗣位，迁黄门侍郎，受诸葛亮器重。");
         this._roles[this._roles.length] = new HeroInfoVO(507,1,1,104,2,1,1,1003,10010,10011,"诸葛靓","JIANJIANG_21","诸葛靓，字仲思 ，琅琊阳都（今山东沂南县）人，西汉司隶校尉诸葛丰之后，曹魏征东大将军诸葛诞少子。诸葛诞叛乱后入仕东吴。吴亡后投降晋朝，但因父仇而终身不仕，时人称许他至孝。");
         this._roles[this._roles.length] = new HeroInfoVO(508,3,1,105,3,1,1,1016,10020,10021,"邓龙","QIANGJIANG_21","邓龙（？—208）江夏太守黄祖部将。黄祖对阵东吴。祖令苏飞为大将，陈就、邓龙为先锋。甘宁飞上艨艟，将邓龙砍S。陈就弃船而走。吕蒙追赶，当胸一刀砍翻。");
         this._roles[this._roles.length] = new HeroInfoVO(509,1,1,106,4,1,1,1008,10050,10051,"赵昂","GONGJIANG_21","赵昂，字伟章（一作伟璋），天水冀人。汉末时曹操部下。初为羌道令，建安中转参军事徒居州治冀城。");
         this._roles[this._roles.length] = new HeroInfoVO(510,3,1,107,1,1,1,1008,10030,10031,"关靖","DAOJIANG_22","公孙瓒的长史。谄媚阿谀，眼光短浅。易京受到袁绍包围夹攻时，曾阻止公孙瓒从背后袭击袁绍。公孙瓒被袁绍包围自斩後，他说：“听闻君子使他人陷于危难之际，必当患难与共，吾等岂可以独存”，率一军斩入袁绍军中，战S殉主。");
         this._roles[this._roles.length] = new HeroInfoVO(511,1,1,108,2,1,1,1018,10010,10011,"李孚","JIANJIANG_21","李孚，三国时期魏国人，曾任魏阳平太守。本姓冯，后改为李。");
         this._roles[this._roles.length] = new HeroInfoVO(512,3,1,109,3,1,1,1009,10020,10021,"张纯","QIANGJIANG_22","张纯，东汉末年人物，曾为中山相。灵帝光和中，逃入辽西乌丸丘力居部中，自号弥天将军、安定王、弥天安定王，为三郡乌丸元帅，寇略青、徐、幽、冀四州，斩掠吏民，所至残破。灵帝末，以刘虞为幽州牧，募胡人斩纯首，北州乃定。");
         this._roles[this._roles.length] = new HeroInfoVO(513,2,0,110,0,1,1,1013,10100,10101,"程秉","JUNSHI_11","程秉（生卒年不详），字德枢，汝南南顿（今河南项城西）人。三国时期吴国官员、儒学家。");
         this._roles[this._roles.length] = new HeroInfoVO(514,1,2,111,1,1,1,1008,10030,10031,"高览","GAO_LAN","高览，一名高奂，本属袁绍部将，后淳于琼被曹操击破，与张郃一同投降曹操。曾与许褚、徐晃大战不分胜负，后刘备败走荆州时，被冲阵而来的赵云刺斩。");
         this._roles[this._roles.length] = new HeroInfoVO(515,1,1,112,4,1,1,1019,10050,10051,"李丰","GONGJIANG_22","李丰（？－254年2月）字安国（一说字宣国），冯翊东县（今陕西大荔一带）人。三国时期曹魏大臣。卫尉李义之子。");
         this._roles[this._roles.length] = new HeroInfoVO(516,1,0,101,0,1,1,1009,10100,10101,"司马朗","JUNSHI_12","司马朗（171 — 217年），字伯达，河内郡温县人，东汉末年政治家，“司马八达”之一。司马朗年少时就表现得很有见识，汉末动乱之际，受父命带领家属逃离董卓，又迁往黎阳，成功躲避战乱。曹操任司空后，司马朗被辟为司空属官，又历任成皋令、堂阳长、元城令、丞相主簿、兖州刺史等职，所在皆有政绩，深受百姓爱戴。");
         this._roles[this._roles.length] = new HeroInfoVO(517,2,0,102,0,1,1,1005,10100,10101,"张布","JUNSHI_13","张布（？-264）三国时吴将领。孙休为王时，布为左右将督，素见信爱。孙休元年（258），布由长水校尉迁辅义将军，封永康侯。旋为左将军。");
         this._roles[this._roles.length] = new HeroInfoVO(518,1,1,104,1,1,1,1010,10030,10031,"司马攸","DAOJIANG_23","司马攸（248年—283年），字大猷（yóu），小字桃符 。河内郡温县（今河南温县）人，晋文帝司马昭次子，晋武帝司马炎同母弟，齐武闵王司马冏的父亲，母文明皇后王元姬，西晋宗室、诸侯王、书法家。");
         this._roles[this._roles.length] = new HeroInfoVO(519,3,2,105,2,1,1,1006,10010,10011,"严颜","YAN_YAN","严颜，东汉末年武将，初为刘璋部下，担任巴郡太守。建安十九年，刘备进攻江州，严颜战败被俘，张飞对严颜说：“大军至，何以不降而敢拒战？”，严颜回答说：“卿等无状，侵夺我州，我州但有断头将军，无降将军也！”，张飞生气，命左右将严颜牵去砍头，严颜表情不变地说：“砍头便砍头，何为怒邪！”张飞敬佩严颜的勇气，遂释放严颜并以严颜为宾客。");
         this._roles[this._roles.length] = new HeroInfoVO(520,1,1,106,2,1,1,1012,10010,10011,"胡质","JIANJIANG_23","胡质（？—250年），字文德，寿春（今安徽寿县）人，三国时曹魏大臣，官至荆州刺史、征东将军。");
         this._roles[this._roles.length] = new HeroInfoVO(521,3,1,107,3,1,1,1017,10020,10021,"王匡","QIANGJIANG_23","王匡（生卒年不详），字公节，中国东汉末年的地方军阀。兖州泰山郡（位于当今中国山东省泰安市东北方）人。官至河内郡太守。");
         this._roles[this._roles.length] = new HeroInfoVO(522,3,1,108,0,1,1,1014,10040,10041,"许劭","JUNSHI_22","许劭（shào）（约150年—约195年），字子将。汝南平舆（今河南平舆县射桥镇）人。东汉末年著名人物评论家。据说他每月都要对当时人物进行一次品评，人称为“月旦评”。曾任汝南郡功曹，后南渡投靠扬州刺史刘繇。刘繇被孙策击败后，许劭随其逃往豫章郡，并在豫章去世。");
         this._roles[this._roles.length] = new HeroInfoVO(523,0,1,109,4,1,1,1012,10050,10051,"赵累","GONGJIANG_23","赵累，在蜀汉大将关羽部下任都督一职。后来吴将吕蒙袭取荆州，赵累被吴将潘璋等在临沮擒获。");
         this._roles[this._roles.length] = new HeroInfoVO(524,2,3,106,2,1,1,1054,11340,11341,"陆抗","LU_KANG","陆抗（226年－274年），字幼节，吴郡吴县（今江苏苏州）人。三国时期吴国名将，丞相陆逊次子。陆抗袭父爵为江陵侯，为建武校尉，领其父众五千人。后迁立节中郎将、镇军将军等。孙皓为帝，任镇军大将军、都督西陵、信陵、夷道、乐乡、公安诸军事，驻乐乡（今湖北江陵西南）。凤凰元年（272年），击退晋将羊祜进攻，并攻斩叛将西陵督步阐。后拜大司马、荆州牧，卒于官，终年49岁。与陆逊皆是吴国的中流砥柱，并称“逊抗”，被誉为吴国最后的名将。");
         this._roles[this._roles.length] = new HeroInfoVO(525,1,3,103,4,1,0,1055,11350,12351,"辛宪英","XIN_XIANY","辛氏（191年—269年），字宪英，名不详。颍川阳翟人。辛毗之女，羊耽之妻、辛敞之姐。魏晋时期才女。辛宪英聪朗有才鉴，曾劝弟尽忠职守，又预言钟会将会叛变。");
         this._roles[this._roles.length] = new HeroInfoVO(526,3,3,104,2,1,1,1056,11360,11361,"左慈","ZUO_CI","左慈（156？--289？），字元放，汉族，道号乌角先生，东汉末年著名方士，少居天柱山，研习炼丹之术。明五经，兼通星纬，学道术，明六甲，传说能役使鬼神，坐致行厨。《后汉书》说他少有神道。");
         this._roles[this._roles.length] = new HeroInfoVO(527,1,3,109,1,1,1,1057,11370,11371,"文鸯","WEN_YANG","文鸯（238年？－291年），字次骞（cì qiān），小名阿鸯，世称文鸯。谯郡人（治今安徽亳州）。魏末晋初名将。");
         this._roles[this._roles.length] = new HeroInfoVO(528,3,3,104,2,1,1,1058,11380,11381,"袁绍","YUAN_SHAO","袁绍（？－202年6月28日），字本初，汝南汝阳（今河南省周口市商水县袁老乡袁老村）人。东汉末年军阀，汉末群雄之一。袁绍出身东汉名门”汝南袁氏“，自袁绍曾祖父起，袁氏四代有五人位居三公，他自己也居三公之上，其家族也因此有“四世三公”之称。袁绍早年任中军校尉、司隶校尉，曾指挥诛斩宦官。初平元年（190年），与董卓对立，被推举为关东联军首领。");
         this._roles[this._roles.length] = new HeroInfoVO(529,0,1,112,1,1,1,1020,10030,10031,"董允","DAOJIANG_23","董允（？－246年），字休昭，南郡枝江（今湖北枝江）人，三国时期蜀汉重臣，掌军中郎将董和之子。东汉末年，其父董和跟随刘璋，担任益州太守。刘备册立太子刘禅，以为太子洗马，后为黄门侍郎。延熙六年（公元243年），加辅国将军。延熙七年（公元244年），以侍中守尚书令，担任大将军费祎的副手。延熙九年（公元246年），去世。");
         this._roles[this._roles.length] = new HeroInfoVO(530,3,1,111,2,1,1,1007,10010,10011,"张鲁","JIANJIANG_23","张鲁（？—216年），字公祺（《后汉书》作公旗）。祖籍沛国丰县（今江苏丰县）。东汉末年割据汉中一带的军阀，据传是西汉留侯张良的十世孙、天师道（五斗米道）教祖张陵之孙。张鲁为五斗米道的第三代天师（称系师），在斩张修后继续在汉中一带传播五斗米道，并自称“师君”。雄据汉中近三十年，后投降曹操，官拜镇南将军，封阆中侯，食邑万户。张鲁去世后谥号为“原”，葬于邺城东。");
         this._roles[this._roles.length] = new HeroInfoVO(531,1,1,110,3,1,1,1003,10020,10021,"牵招","QIANGJIANG_23","牵招（？—231年），字子经，安平观津（今河北武邑东）人。三国时期曹魏将领，初从袁绍，袁绍S后跟随袁尚，后因为高干加害而加入曹操，与田豫常年镇守边陲，而功绩次于田豫。");
         this._roles[this._roles.length] = new HeroInfoVO(532,1,1,109,4,1,1,1016,10040,10041,"杨秋","GONGJIANG_23","杨秋，汉末凉州军阀之一，三国时期魏国将领。建安十六年，从附马超起兵反抗曹操，兵败于渭南后，逃到安定。曹操兵围安定，杨秋降曹操，成为魏之名将。曹丕称帝后，秋为冠军将军，畤乡侯。征讨郑甘、卢水、平定关中。官至讨寇将军，封临泾侯。");
         this._roles[this._roles.length] = new HeroInfoVO(533,3,1,108,1,1,1,1008,10030,10031,"李乐","DAOJIANG_22","汉征北将军。初为白波帅，建安元年，董卓余党李傕、郭汜交兵，后悔令天子东，乃来救段煨，因欲劫帝而西。董承、杨奉密遣间使至河东，招故白波帅乐、韩暹、胡才及南匈奴右贤王去卑，并率其众数千骑来，与承、奉共击傕等，大破之，斩首数千级，乘舆乃得进。乐、承拥卫左右，才、奉、暹、去卑为后距。后兵败，承、奉等夜乃潜议过河，使乐先度具舟舡，举火为应。帝以暹为征东、才为征西、乐征北将军，并与奉、承持政。后曹公救驾，迎天子到许，才、乐留河东，后乐自病S。");
         this._roles[this._roles.length] = new HeroInfoVO(534,2,1,107,2,1,1,1008,10010,10011,"骆统","JIANJIANG_22","骆统（193年—228年），字公绪，会稽乌伤（今浙江义乌）人。三国时吴国将领、学者。陈国相骆俊之子。年二十，就担任乌程相，任内有政绩，使得民户过万。又迁为功曹，行骑都尉。曾劝孙权尊贤纳士，省役息民。后出任为建忠中郎将。卒，统领其部曲。因战功迁偏将军，封新阳亭侯，任濡须督。黄武七年（228年）卒，年三十六。");
         this._roles[this._roles.length] = new HeroInfoVO(535,3,1,106,3,1,1,1018,10020,10021,"孙观","QIANGJIANG_22","孙观（？～公元217年），字仲台，兖州泰山（今山东泰安东北）人。东汉末年泰山寇之一，后来投降曹操。随曹操征孙权，为流矢所中，仍坚持奋战，为曹操赞赏。不久伤重逝世。官至振威将军、青州刺史，爵吕都亭侯。");
         this._roles[this._roles.length] = new HeroInfoVO(536,0,1,105,4,1,1,1009,10040,10041,"杨仪","GONGJIANG_22","杨仪（约189年－235年），字威公，襄阳（今湖北襄阳）人，三国时期蜀汉政治家。最初，为荆州刺史傅群的主簿，后投奔关羽，任为功曹。羽遣其至成都，大受刘备赞赏，擢为尚书。因与尚书令刘巴不和，调为弘农太守。建兴三年（225年）任丞相参军，此后一直跟随诸葛亮战斗。亮卒，他部署安全退军。亮生前定蒋琬继己任，仪仅拜中军师。建兴十三年（235年），因多出怨言，被削职流放至汉嘉郡。但杨仪仍不自省，又上书诽谤，言辞激烈，最后下狱，自斩身亡。");
         this._roles[this._roles.length] = new HeroInfoVO(537,1,1,104,1,1,1,1013,10030,10031,"孔秀","DAOJIANG_21","孔秀曹操置东岭关守将。关羽千里走单骑前往寻找义兄刘备，经过东岭关，被孔秀阻拦。孔秀出言不逊，激怒关羽，被关羽斩于马下。");
         this._roles[this._roles.length] = new HeroInfoVO(538,3,2,102,2,1,1,1011,10010,10011,"张梁","ZHANG_LIANG","张梁（？－184年），（袁宏《后汉纪》作张良）钜鹿（治今河北巨鹿）人，东汉末年黄巾起义首领之一，张角的三弟。中平元年（184）随兄起义，号称“人公将军”。遭到朝廷所派左中郎将皇甫嵩进攻时，他率军在广宗（今河北威县）进行反击。后因警戒疏忽，遭皇甫嵩率军夜袭，其率领的义军仓猝应战，义军被击溃，张梁也一同战S。");
         this._roles[this._roles.length] = new HeroInfoVO(539,0,1,101,3,1,1,1019,10020,10021,"王甫","QIANGJIANG_21","王甫（？—222年），字国山，广汉郪（今四川三台县）人。刘璋时，为益州书佐，之后归降刘备，先后担任绵竹令、荆州议曹从事，并在夷陵之战中阵亡。其子王祐，官至尚书右选郎。");
         this._roles[this._roles.length] = new HeroInfoVO(540,2,1,102,4,1,1,1009,10040,10041,"朱治","GONGJIANG_21","朱治（156年―224年），字君理。丹杨故鄣（今浙江安吉）人。三国时期吴国武将，东吴名将朱然之嗣父。早年随从孙坚、孙策征伐，又辅助孙权，稳定江东，功勋卓著。此后长期居住在吴郡，孙权为吴王，拜其为安国将军，配金印紫绶，徙封毗陵侯。每次进见，孙权都亲自迎接。黄武三年（224年）卒，年六十九。");
         this._roles[this._roles.length] = new HeroInfoVO(541,1,1,104,1,1,1,1005,10030,10031,"曹彦","DAOJIANG_22","曹彦，三国时历史人物。大司马曹真之子，大将军曹爽的弟弟，司马懿发动政变，曹爽曹彦兄弟被剥夺兵权，以谋反罪谋，曹氏兄弟遭诛。");
         this._roles[this._roles.length] = new HeroInfoVO(542,1,0,105,0,1,1,1010,10100,10101,"邵悌","JUNSHI_12","邵悌，字元伯，阳平（治今河北大名县东）人，生卒年不详，是曹魏权臣司马昭的心腹。咸熙元年（264），为西曹属。时司马昭欲遣钟会伐蜀，悌进言谏止，昭不纳。");
         this._roles[this._roles.length] = new HeroInfoVO(543,3,2,106,1,1,1,1008,10030,10031,"张宝","ZHANG_BAO","张宝（？－184年），钜鹿（治今河北平乡）人，东汉末黄巾起义首领之一，张角的弟弟，张梁的哥哥。中平元年（184）随兄张角起义，号称“地公将军”。后来张宝在曲阳（今河北晋州）被皇甫嵩、郭典击败，被斩。");
         this._roles[this._roles.length] = new HeroInfoVO(544,3,1,107,2,1,1,1012,10010,10011,"鲍忠","JIANJIANG_21","鲍忠，字叔义。讨伐董卓时为鲍信军中任“秉中将军”。鲍信因惧怕长沙太守孙坚抢了头功击退汜水关守将华雄，暗中遣鲍忠出战华雄。华雄与鲍忠在汜水关前对峙许久，华雄抬刀斩来，鲍忠举金镋抵挡，谁知华雄力气逼人，将金镋斩断，一刀将鲍忠劈S");
         this._roles[this._roles.length] = new HeroInfoVO(545,1,1,108,0,1,1,1017,10040,10041,"王肃","JUNSHI_23","王肃（195年—256年），字子雍。东海郡郯县 （今山东临沂市郯城西南） 人。三国时期曹魏著名经学家，司徒王朗之子、晋文帝司马昭岳父。王肃早年任散骑黄门侍郎，袭封兰陵侯。任散骑常侍，又兼秘书监及崇文观祭酒，屡次对时政提出建议。后历任广平太守、侍中、河南尹等职。曹芳被废时，他以迎接曹髦继位。又帮助司马师平定毌丘俭之乱，再迁中领军，加散骑常侍。");
         this._roles[this._roles.length] = new HeroInfoVO(546,3,1,109,3,1,1,1014,10020,10021,"刘琮","QIANGJIANG_23","刘琮（生卒年不详），山阳高平（今山东微山两城乡）人。东汉末年荆州牧刘表次子，刘琦之弟。刘表S后继承刘表官爵，当曹操大军南下之时，他在蔡瑁等人的劝说之下举荆州而降，被曹操封为青州刺史，后迁谏议大夫，爵封列侯。");
         this._roles[this._roles.length] = new HeroInfoVO(547,3,1,110,4,1,1,1012,10040,10041,"马隆","GONGJIANG_22","马隆，生卒年不详，字孝兴，东平平陆（今山东汶上）人，西晋名将，兵器革新家，官至东羌校尉，封奉高县侯。");
         this._roles[this._roles.length] = new HeroInfoVO(548,2,3,102,0,1,1,1047,11390,11391,"鲁肃","LU_XIAO","鲁肃（172年－217年），字子敬，汉族，临淮郡东城县（今安徽定远）人，中国东汉末年杰出战略家、外交家。出生于一士族家庭；幼年丧父，由祖母抚养长大。他体貌魁伟，性格豪爽，喜读书、好骑射。东汉末年，他眼见朝廷昏庸，官吏腐败，社会动荡，常召集乡里青少年练兵习武。他还仗义疏财，深得乡人敬慕。当时，周瑜为居巢长，因缺粮向鲁肃求助，鲁肃将一仓三千斛粮食慷慨赠给周瑜。从此，二人结为好友，共谋大事。");
         this._roles[this._roles.length] = new HeroInfoVO(549,0,3,110,4,1,1,1059,11400,11401,"黄忠","HUANG_ZHONG","黄忠（？－220年），字汉升（一作“汉叔”），南阳（今河南南阳）人。东汉末年名将。本为刘表部下中郎将，后归刘备，并助刘备攻益州刘璋。建安二十四年（219年），黄忠在定军山一战中阵斩曹操部下名将夏侯渊，升任征西将军，刘备称汉中王后改封后将军，赐关内侯。次年，黄忠病逝。景耀三年（260年），追谥刚侯。");
         this._roles[this._roles.length] = new HeroInfoVO(550,1,3,108,1,1,1,1060,11410,11411,"许诸","XU_CHU","许褚字仲康，谯国谯人（今安徽亳州）。长八尺馀，腰大十围，容貌雄毅，勇力绝人。后追随曹操，自典韦战S之后，主要负责曹操的护卫工作。当曹操去世时许褚哭至吐血，曹丕其迁作武卫将军，负责宫中安全。曹叡继位时封其为牟乡侯，不久去世，谥曰壮侯。");
         this._roles[this._roles.length] = new HeroInfoVO(551,3,1,101,1,1,1,1020,10030,10031,"邢道荣","DAOJIANG_21","零陵太守刘度部下上将。使一柄开山大斧。刘备攻刘度时，邢道荣和刘备将张飞、赵云单挑，被败俘。他诈降刘备，想诱刘备军进入圈套，但失策了，在随后的战斗中被赵云斩S。");
         this._roles[this._roles.length] = new HeroInfoVO(552,0,0,102,0,1,1,1007,10100,10101,"尹默","JUNSHI_11","尹黙，字思潜，生卒年不详，益州梓潼郡涪县（今四川省绵阳市）人。三国时期蜀汉学者、官员。尹默曾远游至荆州，跟从司马徽、宋忠学习古文经学，尤其精于《春秋左氏传》。历任劝学从事、太子仆，教授刘禅《春秋左氏传》，又任谏议大夫、军祭酒、太中大夫，是蜀汉当朝的一代学士。");
         this._roles[this._roles.length] = new HeroInfoVO(553,3,1,104,2,1,1,1003,10010,10011,"蒋奇","JIANJIANG_21","蒋奇，东汉末年河北霸主袁绍麾下战将，官渡之战时，袁绍遣淳于琼等将兵万馀人北迎运车，沮授说绍：“可遣将蒋奇别为支军於表，以断曹公之钞。”袁绍不听从，后来被乱军所斩（有人说是被张辽在乌巢所斩）。");
         this._roles[this._roles.length] = new HeroInfoVO(554,1,1,105,3,1,1,1016,10020,10021,"吴子兰","QIANGJIANG_21","吴子兰（？~200年），后汉的昭信将军，参与了董承暗斩曹操的计划，被曹操发现后处刑。");
         this._roles[this._roles.length] = new HeroInfoVO(555,1,1,106,4,1,1,1008,10050,10051,"司马伷","GONGJIANG_21","司马伷（zhòu）（227年—283年6月12日），字子将，河内郡温县（今河南温县）人。西晋宗室、将领，晋宣帝司马懿第五子，伏太妃所生。晋景帝司马师、文帝司马昭的同父异母弟，晋武帝司马炎的叔父。");
         this._roles[this._roles.length] = new HeroInfoVO(556,3,1,107,1,1,1,1008,10030,10031,"薛兰","DAOJIANG_22","薛兰，豫州沛国相县人。东汉末年吕布手下将领，驻扎在钜野，被曹操所斩。");
         this._roles[this._roles.length] = new HeroInfoVO(557,2,1,108,2,1,1,1018,10010,10011,"盛曼","JIANJIANG_22","东吴建平太守。永安七年二月，盛曼与镇军将军陆抗、抚军将军步协、征西将军留平，率众围蜀巴东守将罗宪");
         this._roles[this._roles.length] = new HeroInfoVO(558,2,1,109,0,1,1,1009,10040,10041,"严畯","JUNSHI_21","严畯（生卒年不详），字曼才，彭城（治今江苏徐州）人，三国时期孙吴官员、学者。性情忠厚，待人以诚。");
         this._roles[this._roles.length] = new HeroInfoVO(559,2,1,110,3,1,1,1013,10020,10021,"朱据","QIANGJIANG_22","朱据（194年－250年），字子范，吴郡吴县（今江苏苏州）人，三国时期吴国重要官员及将领，前将军、青州牧朱桓从弟、大都督朱异的堂叔");
         this._roles[this._roles.length] = new HeroInfoVO(560,3,0,111,0,1,1,1011,10100,10101,"士孙瑞","JUNSHI_12","士孙瑞(?—195)，字君荣（一说字君策），东汉末年大臣。少传家业，博达无所不通，仕历显位。永汉三年四月，为尚书仆射、与司徒王允、董卓将吕布共谋诛卓。卓既诛，迁大司农，为国三老。每三公缺，瑞常在选中。太尉周忠、皇甫嵩，司徒淳于嘉、赵温，司空杨彪、张喜等为公，皆辞拜让瑞。后卓余党李傕、郭汜交兵，瑞为尚书令，为乱兵所害。天子都许，追论瑞功，封子萌澹津亭侯。");
         this._roles[this._roles.length] = new HeroInfoVO(561,3,1,112,4,1,1,1019,10050,10051,"鲍隆","GONGJIANG_23","鲍隆为桂阳管军校尉，出身桂阳岭山乡猎户，曾射斩双虎。东汉末年桂阳太守赵范的手下将士。");
         this._roles[this._roles.length] = new HeroInfoVO(562,3,0,107,1,1,1,1009,10090,10091,"吴巨","DAOJIANG_11","吴巨（？-211），一作吴臣 （《三国志集解》引《江表传》原作吴臣，现多被改成吴巨）。或字子卿（即与吴子卿为一人，按古人取字，臣与卿相对，符合取字法，比如召信臣字翁卿、叶清臣字道卿、俞献卿字谏臣，若如此则其名当为吴臣，吴巨为传抄错误）。东汉末刘表部将。长沙（治今湖南长沙）人。刘表任为苍梧太守。因与刘表所遣交州刺史赖恭失和，举兵逐恭。建安十五年（210），孙权遣步骘为交州刺史，吴巨外附内违。次年为步骘诱斩。");
         this._roles[this._roles.length] = new HeroInfoVO(563,0,1,108,2,1,1,1005,10010,10011,"蒋舒","JIANJIANG_23","三国时期人物，初为蜀汉武兴督。景耀六年，魏伐蜀，钟会攻围汉、乐二城，遣别将进攻关口。舒为武兴督，在事无称。蜀命人代之，因留舒助汉中守。舒恨，开城出降。");
         this._roles[this._roles.length] = new HeroInfoVO(564,1,1,109,3,1,1,1010,10020,10021,"程武","QIANGJIANG_23","程武，生卒年不详，程昱之子。三国时期魏臣，继承其父安乡侯的爵位。在《三国演义》中，程武在诸葛亮首次北伐时登场，是魏军参军，他定下策略对蜀军进行反击，令赵云一度身陷险境，几乎命丧。");
         this._roles[this._roles.length] = new HeroInfoVO(565,1,2,107,2,1,1,1008,10010,10011,"夏侯霸","XIAHOU_BA","夏侯霸，生卒年不详，字仲权，沛国谯（今安徽亳州）人，三国时期魏国和蜀汉后期的重要将领，征西将军夏侯渊次子，其母为曹操妻室丁氏的妹妹。在魏国官至右将军、讨蜀护军，封爵博昌亭侯，屯驻陇西；在蜀汉时为主要北伐将领，多次参加御蜀和伐魏战争。");
         this._roles[this._roles.length] = new HeroInfoVO(566,1,1,102,0,1,1,1012,10040,10041,"戏志才","JUNSHI_22","戏志才，或志才为字，名不详，东汉颍川郡（今河南禹州）人。经荀彧推荐，成为曹操手下谋士。为人多谋略，曹操十分器重，不幸早卒。他S后，荀彧推荐了郭嘉。");
         this._roles[this._roles.length] = new HeroInfoVO(567,3,1,105,1,1,1,1017,10030,10031,"杨任","DAOJIANG_23","杨任，三国时期汉中军阀张鲁的武将。曹操攻伐汉中时，他奉命与杨昂一同镇守阳平关，后来杨昂出关战曹操，阵亡，他逃回汉中报告张鲁。曹操攻取阳平关后，他自告奋勇出战，不幸被曹操部将夏侯渊用拖刀计斩于马下。");
         this._roles[this._roles.length] = new HeroInfoVO(568,1,1,104,2,1,1,1014,10010,10011,"王颀","JIANJIANG_21","王颀，字孔硕，青州东莱郡（治今山东莱州）人。三国时期曹魏武将，历任裨将军、玄菟太守、带方太守、天水太守。入晋后任汝南太守。");
         this._roles[this._roles.length] = new HeroInfoVO(569,1,1,106,3,1,1,1012,10020,10021,"曹遵","QIANGJIANG_21","曹遵,（？－229年） 沛国谯（今安徽亳州）人。三国时期魏大司马曹真之宗族。少与宗人大司马曹真、真乡人朱赞并事太祖。遵、赞早亡，真愍之，乞分所食邑封遵、赞子。真请分真邑赐遵、赞子爵关内侯，各百户。");
         this._roles[this._roles.length] = new HeroInfoVO(570,1,3,101,1,1,1,1061,11420,11421,"徐晃","XU_HUANG","徐晃（？－227年），字公明，河东杨（今山西洪洞东南）人。三国时期曹魏名将。本为杨奉帐下骑都尉，杨奉被曹操击败后转投曹操，在曹操手下多立功勋，参与官渡、赤壁、关中征伐、汉中征伐等几次重大战役。樊城之战中徐晃作为曹仁的援军击败关羽，因于此役中治军严整而被曹操称赞“有周亚夫之风”。曹丕称帝后，徐晃被加为右将军，于公元227年病逝，谥曰壮侯。");
         this._roles[this._roles.length] = new HeroInfoVO(571,2,3,111,2,1,1,1062,11480,11481,"周瑜","ZHOU_YU","周瑜（175年-210年），字公瑾，庐江舒县（今安徽省合肥市舒县）人 。东汉末年名将，洛阳令周异之子，堂祖父周景、堂叔周忠，都官至太尉。身体长壮有姿貌、精音律，江东有“曲有误，周郎顾”之语。周瑜少与孙策交好，21岁追随孙策奔赴战场平定江东。孙策遇刺身亡，孙权继任，周瑜将兵赴丧，以中护军与长史张昭共掌众事。建安十三年 （208年），周瑜率军与刘备联合，于赤壁之战中大败曹军，由此奠定了“三分天下”的基础。建安十四年（209年），拜偏将军，领南郡太守。建安十五年（210年）病逝于巴丘，年仅36岁。");
         this._roles[this._roles.length] = new HeroInfoVO(572,2,3,112,4,1,1,1063,11490,11491,"吕蒙","LV_MENG","吕蒙（179年—220年），字子明，东汉末年名将，汝南富陂人（今安徽阜南吕家岗）。少年时依附姊夫邓当，随孙策为将。以胆气称，累封别部司马。孙权统事后，渐受重用，从破黄祖作先登，封横野中郎将。从围曹仁于南郡，破朱光于皖城，累功拜庐江太守。后进占荆南三郡，计擒郝普，在逍遥津之战中奋勇抵抗张辽军追袭，并于濡须数御魏军，以功除左护军、虎威将军。鲁肃去世后，代守陆口，设计袭取荆州，击败蜀汉名将关羽，使东吴国土面积大增，拜南郡太守，封孱陵侯，受勋殊隆。不久后因病去世，享年四十二岁。");
         this._roles[this._roles.length] = new HeroInfoVO(573,0,1,104,1,1,1,1001,10030,10031,"阎宇","DAOJIANG_21","阎宇，字文平，生卒年不详，荆州南郡（治今湖北省荆州市）人。三国时期蜀汉将领。阎宇素有才干，处事勤勉，历任庲降都督，永安都督，官至右（大）将军。");
         this._roles[this._roles.length] = new HeroInfoVO(574,3,1,105,2,1,1,1002,10010,10011,"韩莒子","JIANJIANG_11","韩莒子（?－200），淳于琼部下的督将，协助淳于琼守备乌巢，终日与淳于琼等人聚饮，终招致乌巢之败。");
         this._roles[this._roles.length] = new HeroInfoVO(575,0,1,106,3,1,1,1003,10020,10021,"高干","QIANGJIANG_21","高干（？－206年），字元才。东汉末年官员、将领，蜀郡太守高躬之子、大将军袁绍的外甥。");
         this._roles[this._roles.length] = new HeroInfoVO(576,0,1,107,4,1,1,1004,10050,10051,"董璜","GONGJIANG_21","董璜（?－192），陇西临洮（今甘肃临洮）人。东汉末年权臣董卓之侄，卓兄董擢之子。官至侍中。董卓被诛后也一同被斩。");
         this._roles[this._roles.length] = new HeroInfoVO(577,1,1,108,1,1,1,1005,10030,10031,"田畴","DAOJIANG_21","田畴（169年－214年），字子泰，右北平无终（今河北省玉田县人，现玉田县为古无终国）人，东汉末年隐士。好读书。初为幽州牧刘虞从事。建安十二年（207）曹操北征乌桓时投曹操，任司空户曹掾。因为向导平定乌丸有功，封亭侯，不受。后从征荆州，有功，以前爵封之，仍不受，拜为议郎。建安十九年（214）去世，年四十六。");
         this._roles[this._roles.length] = new HeroInfoVO(578,1,1,109,2,1,1,1006,10010,10011,"田章","JIANJIANG_21","田章，晋奋威护军。景元五年，章等随镇西将军钟会从剑阁西，径出江由。未至百里，章先破蜀伏兵三校，征西将军邓艾使章先登。及入晋，为奋威护军。泰始六年六月戊午，秦州刺史胡烈击叛虏于万斛堆，力战，S之。诏遣尚书石鉴行安西将军、都督秦州诸军事，与章讨之。");
         this._roles[this._roles.length] = new HeroInfoVO(579,2,1,110,3,1,1,1007,10020,10021,"鲁淑","QIANGJIANG_22","鲁淑（217年－274年），吴国大臣鲁肃的遗腹子，濡须督张承说他将来必能前途会很远大。永安年间，升为昭武将军、都亭侯。历任武昌督、夏口督。凤凰三年（274年）病逝，享年58岁。儿子鲁睦继承爵位，统领鲁淑的军队。");
         this._roles[this._roles.length] = new HeroInfoVO(580,0,1,111,4,1,1,1008,10050,10051,"霍弋","GONGJIANG_22","霍弋（生卒年不详），字绍先，南郡枝江（今湖北枝江）人，霍峻之子，三国蜀汉至西晋初时将领。刘备时为太子舍人。后主登基为谒者。诸葛亮北驻汉中时用为丞相府记室，诸葛亮S后为黄门侍郎，刘禅立太子后为中庶子。尽言规谏太子，甚为得体。");
         this._roles[this._roles.length] = new HeroInfoVO(581,3,1,101,1,1,1,1009,10030,10031,"陈元","DAOJIANG_23","字长孙，苍梧广信人。生卒年不详，约东汉初年前后在世。父钦习左氏《春秋》，元少传父业。建武初，（公元25年）与桓谭、杜林、郑兴俱为学者所宗。以父任为郎，抗疏请立左氏学，又请勿令司隶校尉督察三公。");
         this._roles[this._roles.length] = new HeroInfoVO(582,1,1,102,2,1,1,1010,10010,10011,"胡遵","JIANJIANG_21","胡遵（？-256年），安定临泾（今甘肃镇原南）人，魏卫将军。景初二年（238），辽东公孙渊反，司马懿遣遵等击破其将卑衍、杨祚。");
         this._roles[this._roles.length] = new HeroInfoVO(583,1,1,104,0,1,1,1011,10100,10101,"桓范","JUNSHI_21","桓范（？-249年），字元则，沛国龙亢（今安徽省怀远县西龙亢镇北）人。三国时期曹魏大臣、文学家，画家。");
         this._roles[this._roles.length] = new HeroInfoVO(584,1,0,106,0,1,1,1012,10100,10101,"蒋干","JUNSHI_12","蒋干，字子翼，汉末三国时期的人物，九江（治今安徽寿县）人。历史上的蒋干是当时的名士、辩论家。");
         this._roles[this._roles.length] = new HeroInfoVO(585,3,1,108,3,1,1,1013,10020,10021,"韩猛","QIANGJIANG_23","韩猛，又名韩若、韩荀、韩??（上荀下大）[1]  ，东汉末年人物，袁绍部将。");
         this._roles[this._roles.length] = new HeroInfoVO(586,3,0,110,1,1,1,1014,10030,10031,"牛辅","DAOJIANG_13","牛辅，董卓的女婿，曾任中郎将，征讨白波军，不能取胜。董卓被斩时，牛辅别屯于陕地。吕布派李肃前去征讨牛辅，被牛辅击败。后来，牛辅营中有士兵半夜背叛出逃，造成内乱，牛辅以为整营皆叛，于是带着金银珠宝，独与亲信胡赤儿等五六人出逃。胡赤儿等人谋财害命，于途中将其斩首送往长安。");
         this._roles[this._roles.length] = new HeroInfoVO(587,2,2,105,1,1,1,1015,10030,10031,"蒋钦","JIANG_QING","蒋钦（？－220年），字公奕，九江寿春（今安徽寿县）人。汉末东吴名将。早年随孙策平定丹阳、吴郡、会稽和豫章四郡。平盗贼，迁西部都尉。讨会稽贼吕合、秦狼等，徙讨越中郎将。又与贺齐并力讨平黟贼。从征合肥，因功迁荡寇将军，领濡须督，被陈寿盛赞为“江表之虎臣”[1]  。后召还都拜右护军，典领辞讼。蒋钦贵守约，性豁达。");
         this._roles[this._roles.length] = new HeroInfoVO(588,1,1,107,0,1,1,1016,10100,10101,"梁习","JUNSHI_23","梁习（？－230年），字子虞，陈郡柘（今商丘柘城）人。初为郡主薄，后被曹操任命为县令，因有政绩升任司空西曹令史。后任并州刺史，封关内侯。建安十八年（210年）拜议郎，魏文帝继位，梁习复为并州刺史，晋封申门亭侯，其政绩常为天下州郡之最。");
         this._roles[this._roles.length] = new HeroInfoVO(589,1,1,109,2,1,1,1017,10010,10011,"董禧","JIANJIANG_22","董禧，三国时期人物，濮阳郡人，曾效力过的势力 吕布、魏。原为吕布帐下郎将，吕布兵败身亡，遂投降夏侯渊，在定军山时充任检粮官，后夏侯渊被斩，董禧任南安粮曹。");
         this._roles[this._roles.length] = new HeroInfoVO(590,0,1,111,3,1,1,1018,10020,10021,"士祗","QIANGJIANG_21","自称交阯太守的士徽之兄。随兄投降吕岱后，伏诛。");
         this._roles[this._roles.length] = new HeroInfoVO(591,3,1,101,4,1,1,1019,10050,10051,"尚弘","GONGJIANG_22","任行军校尉，时天下大乱，献帝逃出长安，弘力大，背献帝登船，渡河。");
         this._roles[this._roles.length] = new HeroInfoVO(592,1,3,111,3,1,1,1064,11500,11501,"李典","LI_DIAN","李典（生卒年不详），字曼成，东汉末年名将。山阳郡钜野县（今山东巨野）人。李典深明大义，不与人争功，崇尚学习，尊敬儒雅，尊重博学之士，在军中被称为长者。李典有长者之风，官至破虏将军，三十六岁时去世。魏文帝曹丕继位后追谥号为愍侯。");
         this._roles[this._roles.length] = new HeroInfoVO(593,1,3,112,1,1,1,1065,11510,11511,"邓艾","DENG_AI","邓艾（约197年－264年），字士载，义阳棘阳（今河南新野）人。三国时期魏国杰出的军事家、将领。其人文武全才，深谙兵法，对内政也颇有建树。本名邓范，后因与同乡人同名而改名。邓艾多年在曹魏西边战线防备蜀汉姜维。公元263年他与钟会分别率军攻打蜀汉，最后他率先进入成都，使得蜀汉灭亡。后因遭到钟会的污蔑和陷害，被司马昭猜忌而被收押，最后与其子邓忠一起被卫瓘派遣的武将田续所斩害。他被推崇为古今六十四名将之一。");
         this._roles[this._roles.length] = new HeroInfoVO(594,1,3,112,0,1,1,1066,11520,11521,"荀攸","YUN_YOU","荀攸（157年－214年），字公达，颍川颍阴（今河南许昌）人。荀彧之侄，东汉末年谋士。荀攸在何进掌权时期任黄门侍郎，在董卓进京时曾因密谋刺斩董卓而入狱，后弃官回家。 曹操迎天子入许都之后，荀攸成为曹操的军师。 曹操征伐0时荀攸劝阻了曹操退兵，并献奇计水淹下邳城，活捉吕布。官渡之战荀攸献计声东击西，斩斩颜良和文丑。又策奇兵，派徐晃烧袁绍粮草，同时力主曹操接纳许攸，画策乌巢，立下大功。平定河北期间，荀攸力排众议，主张曹操消灭袁绍诸子，被曹操上奏朝廷封为陵树亭侯。荀攸行事周密低调，计谋百出，深受曹操称赞。建安十九年（214年），荀攸在曹操伐吴途中去世。");
         this._roles[this._roles.length] = new HeroInfoVO(595,1,3,107,1,1,1,1067,11530,11531,"夏侯敦","XIAHOU_DUN","夏侯敦（？－220年），字元让，沛国谯（今安徽亳州）人。汉末年三国时期曹魏名将，西汉开国元勋夏侯婴的后代。少年时以勇气闻名于乡里。曹操起兵，夏侯敦是其最早的将领之一。与吕布军交战时，曾一度被擒为人质，又被流矢射瞎左眼。多次为曹操镇守后方，曾率军民阻断太寿河水，筑陂塘灌溉农田，使百姓受益，功勋卓著。历任折冲校尉、济阴太守、建武将军，官至大将军，封高安乡侯，追谥忠侯。青龙元年（233年），得以配享太祖（曹操）庙庭。");
         this._roles[this._roles.length] = new HeroInfoVO(596,3,1,111,1,1,1,1001,10030,10031,"乐就","DAOJIANG_23","乐就（？－197），在袁术为攻徐州而大兴七军之际，以督战官之身份担任联络之役。但是，袁术军不幸战败，其也在寿春被曹操军逮捕并遭到斩首。");
         this._roles[this._roles.length] = new HeroInfoVO(597,0,0,110,2,1,1,1002,10010,10011,"金旋","JIANJIANG_12","金旋（？─209年？），字元机，东汉末年京兆人，或为西汉名臣金日磾后裔，有一子金祎。担任过黄门侍郎、汉阳太守，先后官拜议郎、中郎将等官职，最后成为荆州的武陵太守。");
         this._roles[this._roles.length] = new HeroInfoVO(598,0,1,109,3,1,1,1003,10020,10021,"邹靖","QIANGJIANG_22","邹靖（一作雏靖），东汉末年时期的破虏校尉、北军中侯。曾与公孙瓒讨伐北方的胡人，又率领刘备等人讨伐黄巾。");
         this._roles[this._roles.length] = new HeroInfoVO(599,1,1,108,4,1,1,1004,10050,10051,"强端","GONGJIANG_21","强端， 雍州阴平人（今甘肃文县），东汉末年盘踞阴平的氐王 。曹刘汉中争霸，刘备以马超在氐人的威信，遣使者企图通好阴平氐人，时阴平氐人雷定响应马超，而阴平氐王强端却亲曹魏，吴兰在沮水被曹洪打败，逃至阴平时被强端所斩斩，并将其首级送交曹军。");
         this._roles[this._roles.length] = new HeroInfoVO(600,3,1,107,1,1,1,1005,10030,10031,"边章","DAOJIANG_22","边章，凉州金城人，汉末凉州军阀之一，本名边允，因造反被汉朝通缉，遂改名为边章，官至新安令。早年与同郡韩遂俱著名西州，朝廷封为为督军从事。中平元年，凉州宋扬、北宫玉、李文侯等反，推举边章、韩遂遂为首领，斩刺史郡守陈懿起兵反叛，拥兵十多万，先后连败皇甫嵩、张温、董卓、孙坚等名将，汉王朝天下为之骚动。");
         this._roles[this._roles.length] = new HeroInfoVO(601,1,1,106,2,1,1,1006,10010,10011,"荀恺","JIANJIANG_21","荀恺，字茂伯，小字虎子，颍川颖阴（河南许昌市）人。魏末晋初官员、外戚，太尉荀彧曾孙，骠骑将军荀霬与南阳公主长子，晋宣帝司马懿外孙，护军将军荀悝之兄。以门荫，历任侍中、司隶校尉、尚书左仆射、征西大将军。咸熙年间，封为南顿县开国子。");
         this._roles[this._roles.length] = new HeroInfoVO(602,1,1,105,3,1,1,1007,10020,10021,"徐商","QIANGJIANG_23","徐商大中十三年，中进士，释褐秘书省校书郎。累迁侍御史，改礼部员外郎。不久改知制诰，转郎中，会昌三年（843年）充翰林学士，后拜中书舍人，累官山南东道（今湖北西北部）节度使。入京为御史大夫。宣宗大中八年（854年）拜河中节度使。咸通初年，担任刑部尚书，充诸道盐铁转运使，迁兵部尚书。咸通四年（863年），拜相。六年罢相，历官检校右仆射、江陵尹、荆南（今湖北江陵）节度观察使。后来入京担任吏部尚书，不久卒。");
         this._roles[this._roles.length] = new HeroInfoVO(603,2,1,104,0,1,1,1008,10040,10041,"士壹","JUNSHI_21","士壹（？——226年），苍梧广信（今广西苍梧县）人，士燮之弟。曾因恭送刺史丁宫而后被宫召入朝廷，因董卓之乱，士壹逃回故里，领合浦太守。后随兄降吴，最后因其侄士徽叛乱牵连而被免为庶人，数年后因犯法而被斩。");
         this._roles[this._roles.length] = new HeroInfoVO(604,1,1,102,4,1,1,1009,10050,10051,"董昭","GONGJIANG_22","董昭（156年－236年7月4日），字公仁，济阴定陶（今山东定陶）人。东汉末至三国曹魏初年谋士，重臣。曹魏的开国元勋。董昭年轻时被举为孝廉，后担任袁绍帐下参军。多有战功，但是袁绍听信谗言，董昭不得已离开袁绍投奔张杨。张杨率军迎接汉献帝时，董昭随行，并拜为议郎。后与曹操在洛阳相见，又建议曹操将汉献帝迎接到许昌。董昭自此成为曹操的谋士。");
         this._roles[this._roles.length] = new HeroInfoVO(605,1,1,101,1,1,1,1010,10030,10031,"牵弘","DAOJIANG_21","牵弘（？-271年），安平观津（今河北武邑县）人 。魏晋时期将领，雁门太守牵招次子。曹魏景元年间，为陇西太守。随邓艾伐蜀有功，拜蜀郡太守。咸熙中，为振威护军。西晋建立，历任扬州、凉州刺史，以果烈S事于边。");
         this._roles[this._roles.length] = new HeroInfoVO(606,1,1,102,2,1,1,1011,10010,10011,"丘建","JIANJIANG_22","三国时人。魏大将钟会帐下督。景元五年，镇西将军钟会破蜀，阴反。会伪矫太后遗诏，欲起兵废文王，使所亲信代领诸军。所请群官，悉闭著益州诸曹屋中，城门宫门皆闭，严兵围守。会帐下督建本属胡烈，烈荐之文王，会请以自随，任爱之。建愍烈独坐，启会，使听内一亲兵出取饮食，诸牙门随例各内一人。烈绐语亲兵及疏与其子曰：“丘建密说消息，会已作大坑，白棓数千，欲悉呼外兵入，人赐白，拜为散将，以次棓斩坑中。”诸牙门亲兵亦咸说此语，一夜传相告，皆遍。");
         this._roles[this._roles.length] = new HeroInfoVO(607,2,1,104,3,1,1,1012,10020,10021,"凌操","QIANGJIANG_21","凌操（？—203年），东汉末年将领，吴郡余杭（今浙江余杭）人，凌统之父。其人“轻便有胆气”。早年跟随孙策转战江东。每从征伐，常奋勇当先。驻守永平（今江苏溧阳），平治山越，百姓威服，迁为破贼校尉。");
         this._roles[this._roles.length] = new HeroInfoVO(608,2,1,105,4,1,1,1013,10050,10051,"步协","GONGJIANG_23","步协，临淮淮阴人，东吴丞相步骘之子。赤乌十年步骘逝世，步协嗣父之任，继统步骘所领，加为抚军将军。后来蜀国被魏国所灭，东吴闻知此事，见百城无主，遂有兼蜀之志，命步协率众西征，却为罗宪所阻。");
         this._roles[this._roles.length] = new HeroInfoVO(609,3,1,106,1,1,1,1014,10030,10031,"成宜","DAOJIANG_22","成宜东汉末年凉州军阀之一。建安十六年，从附马超起兵反抗曹操。同年九月，曹操发动渭南大决战，成宜战S。");
         this._roles[this._roles.length] = new HeroInfoVO(610,1,2,107,1,1,1,1010,10030,10031,"王基","DAOJIANG_23","王基（190年－261年），字伯舆。青州东莱曲城人。三国时期曹魏将领。王基文武兼备，才高于世，德溥于时，深得司马懿、司马师、司马昭的器重，尤其在南征毌丘俭、文钦之乱及东征诸葛诞之叛等大规模军事活动中，王基与司马师、司马昭结下了深厚的军友情谊。官至征南将军、都督荆州诸军事，封东武侯。景元二年（261年），王基去世，追赠司空，谥号景侯。");
         this._roles[this._roles.length] = new HeroInfoVO(611,1,0,108,2,1,1,1016,10010,10011,"管辂","JIANJIANG_13","管辂（209年－256年），字公明，平原（今山东德州平原县）人。三国时期曹魏术士。年八九岁，便喜仰观星辰。成人后，精通《周易》，善于卜筮、相术，习鸟语，相传每言辄中，出神入化。体性宽大，常以德报怨。正元初，为少府丞。北宋时被追封为平原子。");
         this._roles[this._roles.length] = new HeroInfoVO(612,3,1,109,0,1,1,1017,10040,10041,"王累","JUNSHI_22","王累，广汉人，刘璋的部下，益州从事，曾倒悬于城门劝谏刘璋不要迎接刘备入蜀，然而不被听从。于是其自断绳索，摔S。");
         this._roles[this._roles.length] = new HeroInfoVO(613,1,1,110,3,1,1,1018,10020,10021,"王浑","QIANGJIANG_21","王浑（223年－297年），字玄冲，太原晋阳（今山西太原）人。三国曹魏至西晋初年名臣、将领，东汉代郡太守王泽之孙、曹魏司空王昶之子。王浑早年为大将军曹爽帐下掾吏，高平陵政变之后被免官。不久复出，历任怀县县令、参军、散骑侍郎等职，并袭封京陵县侯。西晋建立后，改任扬烈将军。之后又担任东中郎将、征虏将军、豫州刺史等职，积极筹划伐吴方略。");
         this._roles[this._roles.length] = new HeroInfoVO(614,3,1,111,4,1,1,1019,10050,10051,"关纯","GONGJIANG_21","关纯(?-191)本名闵纯，东汉末年冀州牧韩馥的部下。袁绍欲取冀州，韩馥即差别驾关纯去请袁绍。耿武与关纯伏于城外，以待袁绍。耿武、关纯拔刀而出，欲刺斩绍。绍将颜良立斩耿武，文丑砍S关纯。");
         this._roles[this._roles.length] = new HeroInfoVO(615,0,3,106,3,1,1,1068,11540,11541,"姜维","JIANG_WEI","姜维（202年－264年），字伯约，天水冀县（今甘肃甘谷东南）人。三国时蜀汉名将，官至大将军。少年时和母亲住在一起，喜欢儒家大师郑玄的学说。因为父亲姜冏战S，姜维被郡里任命为中郎。");
         this._roles[this._roles.length] = new HeroInfoVO(616,3,3,110,1,1,0,1069,11550,11551,"貂蝉","DIAO_CHAN","中国古代四大美女之一，东汉（?～公元220年）末年，董卓掌握了朝廷大权，他是个奸臣，很不得人心，大臣王允于是就想除掉他，王允想到了貂蝉。貂蝉在战乱中和父亲、哥哥离散，最后和母亲一起被董卓收留为妃。因为貂蝉，吕布与董卓的矛盾激化，最后吕布刺S了奸臣董卓。貂蝉与王允献策无关。王允献策完全是因为得知了吕布与董卓的内部矛盾，从而才说服吕布斩了董卓。吕布斩了董卓，带着貂蝉，逃出了京城长安。但吕布也没有大的成就，最后被曹操打败。有人劝说他突围，可是吕布舍不得扔下貂蝉，最后被属下出卖给曹操，被曹操斩S。");
         this._roles[this._roles.length] = new HeroInfoVO(617,2,3,108,4,1,0,1073,11590,11591,"孙尚香","SUN_SHANGX","孙夫人，吴郡富春（今浙江杭州富阳）人，东汉末年讨虏将军孙权之妹，曾为左将军刘备之妻。");
         this._roles[this._roles.length] = new HeroInfoVO(618,2,3,109,2,1,1,1071,11570,11571,"孙策","SUN_CE","孙策（175年—200年5月5日  ），字伯符，吴郡富春（今浙江杭州富阳区）人。破虏将军孙坚长子、吴大帝孙权长兄。东汉末年割据江东一带的军阀，汉末群雄之一，三国时期孙吴的奠基者之一。建安二年（197年），袁术僭越称帝后，孙策与袁术决裂。建安二年（197年）夏，曹操下诏书给孙策，要孙策讨伐袁术，任命他为骑都尉，袭父爵乌程侯，兼任会稽太守，曹操奏许朝廷任命策为讨逆将军，并封为吴侯，后统一江东。孙策为将，有智有勇，英姿勃发，其治军严整，军纪严明。但在征战中由于年轻气盛，难免出现处事不慎、好勇斗狠的弱点，这为其结怨和遇刺种下了祸根。");
         this._roles[this._roles.length] = new HeroInfoVO(619,0,3,112,0,1,1,1072,11580,11581,"诸葛亮","ZHUGE_LIANG","诸葛亮（181年-234年10月8日），字孔明，号卧龙（也作伏龙），徐州琅琊阳都（今山东临沂市沂南县）人 ，三国时期蜀汉丞相，杰出的政治家、军事家、外交家、文学家、书法家、发明家。早年随叔父诸葛玄到荆州，诸葛玄S后，诸葛亮就在襄阳隆中隐居。刘备三顾茅庐请出，辅佐刘备建立蜀汉。蜀汉建立后，诸葛亮被封为丞相、武乡侯，对内抚百姓，示仪轨，约官职，从权制，开诚心，布公道，对外联吴抗魏，为实现兴复汉室的政治理想，数次北伐，但因各种不同因素而失败，最后于蜀汉建兴十二年（234年）病逝于五丈原（今陕西宝鸡岐山境内），享年54岁。");
         this._roles[this._roles.length] = new HeroInfoVO(620,3,3,108,4,1,1,1070,11810,11811,"司马徽","SIMA_HUI6","司马徽（？—208年），字德操，颍川阳翟（今河南禹州）人。东汉末年名士，精通道学、奇门、兵法、经学。有“水镜先生”之称。司马徽为人清雅，学识广博，有知人之明，并向刘备推荐了诸葛亮、庞统等人，受到世人的敬重");
         this._roles[this._roles.length] = new HeroInfoVO(621,2,3,106,2,1,1,1074,11600,11601,"孙权","SUN_QUAN","孙权（182年－252年5月21日），字仲谋，吴郡富春（今浙江杭州富阳区）人。三国时代孙吴的建立者（229年—252年在位）。孙权的父亲孙坚和兄长孙策，在东汉末年群雄割据中打下了江东基业。建安五年（200年），孙策遇刺身亡，孙权继之掌事，成为一方诸侯。建安十三年（208年），与刘备建立孙刘联盟，并于赤壁之战中击败曹操，奠定三国鼎立的基础。建安二十四年（219年），孙权派吕蒙成功袭取刘备的荆州，使其领土面积大大增加。孙权晚年在继承人问题上反复无常，引致群下党争，朝局不稳。太元元年（252年）病逝，享年七十一岁，在位二十四年，谥号大皇帝，庙号太祖，葬于蒋陵。");
         this._roles[this._roles.length] = new HeroInfoVO(622,2,3,102,4,1,0,1075,11610,11611,"大乔","DA_QIAO","大乔（乔字古作“桥”）（180年－？），庐江郡皖县人（今安徽安庆潜山），中国东汉末三国时期的女性，系乔公之女、孙策之妾、小乔之姊。与小乔并称为“江东二乔”，传说为绝世美女。");
         this._roles[this._roles.length] = new HeroInfoVO(623,0,0,101,0,1,1,1001,10040,10041,"吕凯","JUNSHI_12","吕凯（？―225年），字季平，永昌郡不韦县（今云南保山东北）人 ，三国时期蜀汉官员。初任永昌郡五官掾功曹。章武三年（223年），建宁太守雍闿反叛，投降吴国，吴国任雍闿为永昌太守，吕凯闭境抗拒雍闿。建兴三年（225年），丞相诸葛亮南征，表奏吕凯功劳，任命他为云南太守，封阳迁亭侯。吕凯还未上任，便被叛乱的少数民族斩害。");
         this._roles[this._roles.length] = new HeroInfoVO(624,3,0,102,4,1,1,1002,10050,10051,"穆顺","GONGJIANG_23","东汉末宦官。献帝欲修书与国舅伏完，共谋图曹公。因顺为宦官中之忠义可托者，乃命顺往送书。顺藏书于发中，潜出禁宫，径至完宅，将书呈上。及完回书付顺，顺乃藏于头髻内，辞完回宫。然公闻信，先于宫门等候，顺回遇公，公喝左右，遍搜身上，并无夹带，放行。忽然风吹落其帽。公又唤回，取帽视之，遍观无物，还帽令戴。顺双手倒戴其帽。公心疑，令左右搜其头发中，搜出伏完书来。公见书大怒，执下顺于密室问之，顺不肯招。当晚将顺、完等宗族二百余口，皆斩于市。");
         this._roles[this._roles.length] = new HeroInfoVO(625,1,0,104,1,1,1,1003,10030,10031,"丁仪","DAOJIANG_12","丁仪（？一220年），字正礼，沛国（治今安徽滩溪）人，丁冲之子，丁廙之兄。建安中期，丁仪与曹操的长女清河公主曾有婚约，但曹丕却以丁仪有眼病为理由，向曹操提出把清河公主嫁给夏侯楙，结果丁仪不能娶公主为妻。后来被曹操聘任为西曹椽。丁仪兄弟与曹植交好，拥护曹植为曹操的太子。曹丕成为太子之后丁仪转任右刺奸掾。曹丕自立为帝之后，丁仪被满门抄斩。曹植著有一诗《又赠丁仪王粲》。");
         this._roles[this._roles.length] = new HeroInfoVO(626,3,0,105,0,1,1,1004,10040,10041,"刘琦","JUNSHI_11","刘琦（？－209年）。兖州山阳郡高平县（今山东省济宁市微山县两城镇）人。荆州牧刘表的长子、谏议大夫刘琮兄。官至荆州刺史。建安十四年（209年）病逝。");
         this._roles[this._roles.length] = new HeroInfoVO(627,1,1,106,3,1,1,1005,10020,10021,"蒋班","QIANGJIANG_21","蒋班，三国时期人物，原为曹魏征东大将军诸葛诞部将。先随诸葛诞讨伐司马氏不果，逃往东吴，不久复归魏。后来参与了晋灭吴之战。");
         this._roles[this._roles.length] = new HeroInfoVO(628,0,1,107,2,1,1,1006,10010,10011,"陈到","JIANJIANG_23","陈到，字叔至，生卒年不详，豫州汝南（今河南驻马店平舆县）人。 三国时期蜀汉将领，刘备帐下白毦兵统领，名位常亚于赵云，以忠勇著称。蜀汉建兴年间，任征西将军、永安都督，封亭侯。在任期间去世。");
         this._roles[this._roles.length] = new HeroInfoVO(629,2,1,108,1,1,1,1007,10030,10031,"孙歆","DAOJIANG_22","（？—280）三国时期孙吴宗室。征虏将军孙贲之孙、威远将军孙邻之子。曾为乐乡督，晋军灭吴时，孙歆为周旨所斩。");
         this._roles[this._roles.length] = new HeroInfoVO(630,3,1,109,4,1,1,1008,10050,10051,"苏由","GONGJIANG_22","东汉末冀州牧袁尚的部将。建安九年（204），袁尚攻袁谭，由与审配守邺城（今河南安阳）。曹操北上攻邺，由降操，助破邺城。");
         this._roles[this._roles.length] = new HeroInfoVO(631,1,1,110,2,1,1,1009,10010,10011,"姜叙","JIANJIANG_21","姜叙，字伯奕，天水郡冀县人。东汉时期汉人，出生于天水郡冀县，是抚夷将军。");
         this._roles[this._roles.length] = new HeroInfoVO(632,2,2,111,0,1,1,1010,10040,10041,"诸葛瑾","ZHUGE_JIN","诸葛瑾（174年－241年），字子瑜，汉族，琅邪阳都（今山东沂南）人。三国时期吴国重臣，诸葛亮之兄，诸葛恪之父。经弘咨推荐，为东吴效力。胸怀宽广，温厚诚信，得到孙权的深深信赖，称为“神交”，并努力缓和蜀汉与东吴的关系。建安二十五年（220年）吕蒙病逝，诸葛瑾代吕蒙领南郡太守，驻守公安。孙权称帝后，诸葛瑾官至大将军，领豫州牧。");
         this._roles[this._roles.length] = new HeroInfoVO(633,1,1,112,3,1,1,1011,10020,10021,"贾范","QIANGJIANG_22","贾范（？－238年），公孙渊部下，将军。直谏公孙渊不应反叛曹魏，被公孙渊斩首。司马懿入襄平城后厚待其子孙。");
         this._roles[this._roles.length] = new HeroInfoVO(634,1,1,106,1,1,1,1012,10030,10031,"典满","DAOJIANG_21","典满，陈留己吾（今河南宁陵西南）人，三国时期曹魏武将，典韦之子。典韦S后，曹操任典满为司马，曹丕即位后拜为都尉，赐爵关内侯。");
         this._roles[this._roles.length] = new HeroInfoVO(635,3,0,107,2,1,1,1013,10010,10011,"张资","JIANJIANG_12","敦煌人张资，自小喜爱读书，聪颖好学，成年后博览群书，满腹经纶，精通文略。五凉时，被吕光征召入仕，官拜中书监，为他出谋划策，人称温雅之士，很受器重。");
         this._roles[this._roles.length] = new HeroInfoVO(636,1,1,108,1,1,1,1014,10030,10031,"任峻","DAOJIANG_22","任峻（？—204年），字伯达，河南郡中牟县人。董卓作乱时，任峻劝说中牟令杨原治理河南，抵抗暴乱。适逢曹操起兵，任峻征集宗族家客数百人归附曹操。之后曹操每次出征，任峻通常在后方补给军队。后来发生饥荒，枣祗建议实施屯田，任峻被任命为典农中郎将，招募百姓在许下屯田，结果连年丰收，积谷足以装满全部粮仓。官渡之战，任峻主持军备和粮草运输，敌军企图抄绝粮道，但在任峻的设法保护下，敌军不能得逞。曹操亦知任峻劳苦功高，任命他为长水校尉，封都亭侯。任峻经常周济他人，其信义为人称道。卒于204年，曹操哭泣良久。");
         this._roles[this._roles.length] = new HeroInfoVO(637,0,2,110,3,1,1,1010,10020,10021,"张苞","ZHANG1_BAO","张苞（bāo），蜀汉名将张飞的长子，早夭。他和关羽次子关兴是关系很好的结拜兄弟，两人并称“小关张”。");
         this._roles[this._roles.length] = new HeroInfoVO(638,3,1,112,0,1,1,1016,10040,10041,"魏攸","JUNSHI_21","魏攸，东汉末年人物，幽州牧刘虞的部下，反对刘虞向公孙瓒展开讨伐的计划");
         this._roles[this._roles.length] = new HeroInfoVO(639,2,1,109,2,1,1,1017,10010,10011,"张承","JIANJIANG_23","张承（178年－244年），字仲嗣。徐州彭城县（今江苏省徐州市）人。三国时孙吴大臣，辅吴将军张昭长子，其妻为诸葛瑾之女。张承年少时以才学知名，与诸葛瑾、步骘、严畯交好。历任骠骑将军西曹掾、长沙西部都尉等职，后任濡须都督、奋威将军，封都乡侯，故又称张奋威。赤乌七年（244年）卒，享年六十七岁，谥号定。《全三国文》辑录有一篇《与吕岱书》。张承为人勇壮刚毅、忠诚正直，能甄识人物，勤于提携后进之士。周昭将其与顾邵、诸葛瑾、步骘、严畯并称为“五君”。");
         this._roles[this._roles.length] = new HeroInfoVO(640,1,1,101,1,1,1,1018,10030,10031,"王则","DAOJIANG_23","王则，曹操部下，曹操欲攻张绣又怕吕布来犯，故担任赠与平东将军印授给吕布的使者。");
         this._roles[this._roles.length] = new HeroInfoVO(641,3,1,102,4,1,1,1019,10050,10051,"李堪","GONGJIANG_21","李堪（？-211），河东人，东汉末年凉州军阀之一。建安十六年，从附马超起兵反抗曹操。同年九月，在渭南大决战中，李堪战S。");
         this._roles[this._roles.length] = new HeroInfoVO(642,0,3,102,3,1,1,1076,11620,11621,"关兴","GUAN_XING","关兴，字安国，河东解县（今山西运城）人，三国时期蜀汉官员，东汉末年名将关羽次子，关平之弟。蜀汉后期重要将领，在诸葛亮第六次北伐之前病逝。");
         this._roles[this._roles.length] = new HeroInfoVO(643,0,3,106,0,1,1,1077,11680,11681,"庞统","PANG_TONG","庞统（179年－214年），字士元，号凤雏，汉时荆州襄阳（治今湖北襄阳）人。东汉末年刘备帐下重要谋士，与诸葛亮同拜为军师中郎将。与刘备一同入川，于刘备与刘璋决裂之际，献上上中下三条计策，刘备用其中计。进围雒县时，庞统率众攻城，不幸中流矢而亡，年仅三十六岁，追赐统为关内侯，谥曰靖侯。后来庞统所葬之处遂名为落凤坡。");
         this._roles[this._roles.length] = new HeroInfoVO(644,1,3,112,2,1,1,1078,11690,11691,"曹丕","CAO_PI","魏文帝曹丕（187年冬—226年6月29日），字子桓，豫州沛国谯县 （今安徽省亳州市）人。三国时期著名的政治家、文学家，曹魏开国皇帝（220年—226年在位）。魏武帝曹操次子，与正室卞夫人的嫡长子 。建安二十五年（220年），曹操逝世，曹丕继任丞相、魏王。同年，受禅登基，以魏代汉，结束了汉朝四百多年的统治，建立了魏国。曹丕在位期间，采纳吏部尚书陈群的意见，于黄初元年 （220年）命其制定九品中正制，成为魏晋南北朝时期主要的选官制度。而且平定了青州、徐州一带的割据势力，最终完成了北方的统一。对外平定边患，击退鲜卑，和匈奴、氐、羌等外夷修好，恢复在西域的建置。");
         this._roles[this._roles.length] = new HeroInfoVO(645,1,3,104,1,1,1,1079,11710,11711,"庞德","PANG_DE","庞德（？－219年），字令明，南安郡狟道县（今甘肃天水市武山县四门镇）人，约在初平年间，投奔马腾帐下，在平定羌民的征伐中屡立战功。建安年间，庞德跟随马超征战平阳，抵御袁将郭援、高干，在马上亲斩郭援首级。张白骑在弘农反叛时，庞德也参与战斗。每次出征常冲锋陷阵，勇冠凉州三军。后几经辗转，随张鲁归降于曹操麾下，被授官立义将军，封关内亭侯，食邑三百户。");
         this._roles[this._roles.length] = new HeroInfoVO(646,1,3,102,0,1,1,1080,11720,11721,"徐庶","XU_SHU","徐庶（生卒年不详），字元直。颍川郡长社县（今河南许昌长葛东）人。东汉末年刘备帐下谋士，后归曹操，并仕于曹魏。徐庶本名徐福，为寒门子弟。早年为人报仇，获救后改名徐庶，拜师求道。后与同郡石广元避难于荆州，与司马徽、诸葛亮、崔州平等道友来往密切。刘备屯驻新野时，徐庶前往投奔，并向刘备推荐诸葛亮。徐庶南下时因母亲被曹操所掳获，徐庶不得已辞别刘备，进入曹营。后来此事被艺术加工为“徐庶进曹营，一言不发”等歇后语，被广为流传。而徐庶也成为孝子的典范被加以称赞。魏文帝时，徐庶官至右中郎将、御史中丞。");
         this._roles[this._roles.length] = new HeroInfoVO(647,1,3,102,0,1,1,1081,11730,11731,"司马懿","SIMA_YI","司马懿（179年—251年9月7日），字仲达，河内郡温县孝敬里（今河南省焦作市温县）人。三国时期魏国政治家、军事谋略家，魏国权臣，西晋王朝的奠基人。嘉平三年（251年），司马懿病S，享年73岁，辞郡公和殊礼，葬于首阳山，谥号宣文。次子司马昭封晋王后，追谥司马懿为宣王；司马炎称帝后，追尊司马懿为宣皇帝，庙号高祖 。");
         this._roles[this._roles.length] = new HeroInfoVO(648,1,3,110,4,1,0,1082,11740,11741,"甄姬","ZHEN_JI","文昭甄皇后（183年1月26日—221年8月4日），名不明，相传为甄宓，实则无记载  。史称甄夫人。中山无极（今河北省无极县）人，上蔡令甄逸之女。魏文帝曹丕的妻子，魏明帝曹叡的生母。");
         this._roles[this._roles.length] = new HeroInfoVO(649,2,3,110,2,1,1,1083,11750,11751,"程普","CHEN_PU","程普（？—215年），字德谋，右北平土垠（今河北丰润东）人。东汉末年名将，历仕孙坚、孙策、孙权三代。他曾跟随孙坚讨伐过黄巾、董卓，斩华雄、破吕布，又助孙策平定江东。孙策S后，他与张昭等人共同辅佐孙权，并讨伐江东境内的山贼，功勋卓著。赤壁之战与周瑜分任左右都督打败曹操，之后大破曹仁于南郡。程普在东吴诸将中年岁最长，被人们尊称为“程公”。");
         this._roles[this._roles.length] = new HeroInfoVO(650,2,3,101,3,1,1,1084,11760,11761,"凌统","LIN_TONG","凌统（189年－217年），字公绩，吴郡馀杭（今浙江余杭）人，三国时期吴国名将。凌操之子，少有名盛，为人有国士之风，多次战役中表现出色，官至偏将军。被陈寿盛赞为“江表之虎臣”  。");
         this._roles[this._roles.length] = new HeroInfoVO(651,2,3,107,2,1,1,1085,11770,11771,"陆逊","LU_XUN","陆逊（183年—245年3月19日），本名陆议，字伯言，吴郡吴县（今江苏苏州）人。三国时期吴国政治家、军事家。建安八年（203年），入孙权幕府，历任海昌屯田都尉、定威校尉、帐下右部督。章武二年（222年），孙权以陆逊为大都督，在夷陵之战中火烧连营击败刘备，一战成名。孙权称帝后，以陆逊为上大将军、辅佐太子孙登并掌管陪都武昌事宜后卷入“二宫之争”。赤乌七年（244年）拜为丞相、荆州牧、右都护、总领三公事务，领武昌事。次年去世，终年六十三岁，追谥昭侯。陆逊跟随孙权四十余年，统领吴国军政十余年，深得孙权器重。深谋远虑，忠诚耿直。一生出将入相，被赞为”社稷之臣“。");
         this._roles[this._roles.length] = new HeroInfoVO(652,3,3,106,4,1,1,1086,11780,11780,"霍去病","HUO_QU_BING","霍去病（公元前117年—140年），抗匈奴武将之一。汉族，河东平阳（今山西临汾西南）人   ，西汉名将、杰出的军事家、爱国将领、民族英雄，官至大司马骠骑将军，封冠军侯。元狩六年，霍去病因病去世，年仅23岁。武帝很悲伤，调遣边境五郡的铁甲军，从长安到茂陵排列成阵，给霍去病修的坟墓外形像祁连山的样子，把勇武与扩地两个原则加以合并，追谥为景桓侯。");
         this._roles[this._roles.length] = new HeroInfoVO(653,3,3,101,3,1,1,1087,11790,11791,"卫青","WEI_QING6","卫青（？—前106年），抗匈奴武将之一。字仲卿，河东平阳（今山西临汾市）人。西汉时期名将，汉武帝第二任皇后卫子夫的弟弟，汉武帝在位时官至大司马大将军，封长平侯。卫青的首次出征是奇袭龙城，揭开汉匈战争反败为胜的序幕，曾七战七捷，收复河朔、河套地区，击破单于，为北部疆域的开拓做出重大贡献。卫青善于以战养战，用兵敢于深入 ，为将号令严明，对将士爱护有恩，对同僚大度有礼，位极人臣而不立私威。元封五年卫青逝世，起冢如庐山，葬于茂陵东北1000米处，谥号为“烈”。");
         this._roles[this._roles.length] = new HeroInfoVO(654,3,3,104,1,1,1,1088,11820,11820,"白起","BAI_QI6","白起（？—公元前257年）， 战国时期秦国郿县（今陕西省眉县常兴镇白家村）人，出自芈姓。楚国白公胜后裔。中国战国时代军事家、秦国名将，兵家代表人物。白起善于用兵，与来自楚国的秦宣太后异父同母的长弟－穰侯魏冉的关系很好。白起在秦昭王时征战六国，为秦国统一六国做出了巨大的贡献。曾在伊阕之战大破魏韩联军，攻陷楚国国都郢城，长平之战重创赵国主力，功勋赫赫。白起担任秦国将领30多年，攻城70余座，歼灭近百万敌军，被封为武安君。白起是继中国历史上自孙武、吴起之后又一个杰出的军事家、统帅，《千字文》将他与廉颇、李牧、王翦并称为战国四大名将，位列战国四大名将之首。");
         this._roles[this._roles.length] = new HeroInfoVO(701,3,3,102,1,1,1,1001,10151,10151,"车纽单于","XIONGNU1","");
         this._roles[this._roles.length] = new HeroInfoVO(702,3,3,102,1,1,1,1001,10161,10161,"车犁单于","XIONGNU2","");
         this._roles[this._roles.length] = new HeroInfoVO(703,3,3,102,1,1,1,1001,10161,10161,"呼揭单于","XIONGNU3","");
         this._roles[this._roles.length] = new HeroInfoVO(704,3,3,102,1,1,1,1001,10201,10201,"迷当","MIDANG","");
         this._roles[this._roles.length] = new HeroInfoVO(705,3,3,102,1,1,1,1001,10211,10211,"强瑞","QIANGSUI","");
         this._roles[this._roles.length] = new HeroInfoVO(706,3,3,102,1,1,1,1001,10221,10221,"雅丹","YADAN","");
         this._roles[this._roles.length] = new HeroInfoVO(707,3,3,102,1,1,1,1001,10231,10231,"越吉","YUEJI","");
         this._roles[this._roles.length] = new HeroInfoVO(708,3,3,102,1,1,1,1001,10232,10232,"越吉","YUEJI","");
         this._skins[this._skins.length] = new SkinVO(1010,0,300,32,13,14,40,"PO_DAO_BING_BLUE","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(1011,0,300,32,13,14,40,"PO_DAO_BING_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(1020,0,300,40,13,16,38,"LIAN_CHUI_BING_BLUE","ATK_LIAN_CHUI_BING","DIE_LIAN_CHUI_BING");
         this._skins[this._skins.length] = new SkinVO(1021,0,300,40,13,16,38,"LIAN_CHUI_BING_RED","ATK_LIAN_CHUI_BING","DIE_LIAN_CHUI_BING");
         this._skins[this._skins.length] = new SkinVO(1030,0,300,45,14,16,40,"NV_BING_BLUE","ATK_NV_BING","DIE_NV_BING");
         this._skins[this._skins.length] = new SkinVO(1031,0,300,45,14,16,40,"NV_BING_RED","ATK_NV_BING","DIE_NV_BING");
         this._skins[this._skins.length] = new SkinVO(1040,0,300,34,12,24,54,"WU_DOU_BING_BLUE","ATK_WU_DOU_BING","DIE_WU_DOU_BING");
         this._skins[this._skins.length] = new SkinVO(1041,0,300,34,12,24,54,"WU_DOU_BING_RED","ATK_WU_DOU_BING","DIE_WU_DOU_BING");
         this._skins[this._skins.length] = new SkinVO(1050,0,300,42,11,22,42,"MAN_BING_BLUE","ATK_MAN_BING","DIE_MAN_BING");
         this._skins[this._skins.length] = new SkinVO(1051,0,300,42,11,22,42,"MAN_BING_RED","ATK_MAN_BING","DIE_MAN_BING");
         this._skins[this._skins.length] = new SkinVO(1060,0,300,40,15,34,50,"QI_BING_BLUE","ATK_QI_BING","DIE_QI_BING");
         this._skins[this._skins.length] = new SkinVO(1061,0,300,40,15,34,50,"QI_BING_RED","ATK_QI_BING","DIE_QI_BING");
         this._skins[this._skins.length] = new SkinVO(1070,0,300,40,12,14,40,"QIANG_BING_BLUE","ATK_QIANG_BING","DIE_QIANG_BING");
         this._skins[this._skins.length] = new SkinVO(1071,0,300,40,12,14,40,"QIANG_BING_RED","ATK_QIANG_BING","DIE_QIANG_BING");
         this._skins[this._skins.length] = new SkinVO(1080,102,300,235,12,14,38,"FEI_DAO_BING_BLUE","ATK_FEI_DAO_BING","DIE_FEI_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(1081,102,300,235,12,14,38,"FEI_DAO_BING_RED","ATK_FEI_DAO_BING","DIE_FEI_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(1090,0,300,50,13,16,38,"TIE_ZHUI_BING_BLUE","ATK_TIE_ZHUI_BING","DIE_TIE_ZHUI_BING");
         this._skins[this._skins.length] = new SkinVO(1091,0,300,50,13,16,38,"TIE_ZHUI_BING_RED","ATK_TIE_ZHUI_BING","DIE_TIE_ZHUI_BING");
         this._skins[this._skins.length] = new SkinVO(1100,104,300,350,12,16,38,"GONG_JIAN_BING_BLUE","ATK_GONG_JIAN_BING","DIE_GONG_JIAN_BING");
         this._skins[this._skins.length] = new SkinVO(1101,104,300,350,12,16,38,"GONG_JIAN_BING_RED","ATK_GONG_JIAN_BING","DIE_GONG_JIAN_BING");
         this._skins[this._skins.length] = new SkinVO(1110,0,300,22,10,14,34,"CI_KE_BLUE","ATK_CI_KE","DIE_CI_KE");
         this._skins[this._skins.length] = new SkinVO(1111,0,300,22,10,14,34,"CI_KE_RED","ATK_CI_KE","DIE_CI_KE");
         this._skins[this._skins.length] = new SkinVO(1120,103,300,290,12,14,34,"NU_BING_BLUE","ATK_NU_BING","DIE_NU_BING");
         this._skins[this._skins.length] = new SkinVO(1121,103,300,290,12,14,34,"NU_BING_RED","ATK_NU_BING","DIE_NU_BING");
         this._skins[this._skins.length] = new SkinVO(1130,0,150,65,14,33,60,"WANG_DATOU_BLUE","ATK_WANG_DATOU","DIE_WANG_DATOU");
         this._skins[this._skins.length] = new SkinVO(1141,0,300,62,16,46,70,"XIONG_ZHONGQI0_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(1142,0,300,72,13,40,76,"XIONG_QIANGQI0_RED","ATK_QIANG_BING","DIE_QIANG_BING");
         this._skins[this._skins.length] = new SkinVO(1143,172,450,400,14,42,68,"XIONG_GONGQI0_RED","ATK_GONG_JIAN_BING","DIE_GONG_JIAN_BING");
         this._skins[this._skins.length] = new SkinVO(1144,171,500,450,15,72,78,"XIONG_NU_CHE0_RED","ATK_NU_BING","DIE_TOU_SHI_CHE");
         this._skins[this._skins.length] = new SkinVO(1145,0,300,64,16,50,86,"XIONG_ZHONGQI_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(1146,0,300,74,13,40,86,"XIONG_QIANGQI_RED","ATK_QIANG_BING","DIE_QIANG_BING");
         this._skins[this._skins.length] = new SkinVO(1147,172,450,400,14,42,86,"XIONG_GONGQI_RED","ATK_GONG_JIAN_BING","DIE_GONG_JIAN_BING");
         this._skins[this._skins.length] = new SkinVO(1151,0,300,72,13,38,68,"QIANG_TIEQI0_RED","ATK_QIANG_BING","DIE_QIANG_BING");
         this._skins[this._skins.length] = new SkinVO(1152,0,300,50,15,39,74,"QIANG_LANGQI0_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(1153,140,450,400,14,38,75,"QIANG_GONGQI0_RED","ATK_GONG_JIAN_BING","DIE_GONG_JIAN_BING");
         this._skins[this._skins.length] = new SkinVO(1154,193,500,450,12,43,73,"QIANG_WUYI0_RED","ATK_Shan_Jiang","DIE_TIE_ZHUI_BING");
         this._skins[this._skins.length] = new SkinVO(1156,0,300,62,16,46,70,"QIANG_TIEQI_RED","ATK_QIANG_BING","DIE_QIANG_BING");
         this._skins[this._skins.length] = new SkinVO(1157,0,300,72,13,40,76,"QIANG_LANGQI_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(1158,140,450,400,14,42,68,"QIANG_GONGQI_RED","ATK_GONG_JIAN_BING","DIE_GONG_JIAN_BING");
         this._skins[this._skins.length] = new SkinVO(1159,193,500,450,12,72,78,"QIANG_WUYI_RED","ATK_Shan_Jiang","DIE_TIE_ZHUI_BING");
         this._skins[this._skins.length] = new SkinVO(1161,0,500,61,13,22,52,"NM_WD0_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(1162,213,500,400,13,21,60,"NM_SBY0_RED","ATK_GONG_JIAN_BING","ATK_GONG_JIAN_BING");
         this._skins[this._skins.length] = new SkinVO(1163,0,500,57,14,24,50,"NM_SBJ0_RED","ATK_GONG_JIAN_BING","DIE_GONG_JIAN_BING");
         this._skins[this._skins.length] = new SkinVO(1164,0,500,56,14,16,53,"NM_MS0_RED","ATK_WU_DOU_BING","DIE_WU_DOU_BING");
         this._skins[this._skins.length] = new SkinVO(1165,0,500,55,14,20,50,"NM_TJ0_RED","ATK_QIANG_BING","DIE_QIANG_BING");
         this._skins[this._skins.length] = new SkinVO(1166,0,500,48,15,29,62,"NM_XB0_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(1171,0,500,74,13,28,62,"NM_WD_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(1172,213,500,400,13,28,68,"NM_SBY_RED","ATK_GONG_JIAN_BING","ATK_GONG_JIAN_BING");
         this._skins[this._skins.length] = new SkinVO(1173,0,500,72,14,27,62,"NM_SBJ_RED","ATK_GONG_JIAN_BING","DIE_GONG_JIAN_BING");
         this._skins[this._skins.length] = new SkinVO(1174,0,500,71,14,26,61,"NM_MS_RED","ATK_WU_DOU_BING","DIE_WU_DOU_BING");
         this._skins[this._skins.length] = new SkinVO(1175,0,500,68,14,29,70,"NM_TJ_RED","ATK_QIANG_BING","DIE_QIANG_BING");
         this._skins[this._skins.length] = new SkinVO(1176,0,500,46,15,36,65,"NM_XB_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(1181,225,500,350,14,22,60,"FS_RZ0_RED","ATK_QIANG_BING","DIE_QIANG_BING");
         this._skins[this._skins.length] = new SkinVO(1182,0,500,52,13,29,64,"FS_TD0_RED","ATK_GONG_JIAN_BING","ATK_GONG_JIAN_BING");
         this._skins[this._skins.length] = new SkinVO(1183,0,500,64,10,26,64,"FS_CM0_RED","ATK_QIANG_BING","DIE_QIANG_BING");
         this._skins[this._skins.length] = new SkinVO(1191,225,500,350,14,25,70,"FS_RZ_RED","ATK_QIANG_BING","DIE_QIANG_BING");
         this._skins[this._skins.length] = new SkinVO(1192,0,500,62,13,32,74,"FS_TD_RED","ATK_GONG_JIAN_BING","ATK_GONG_JIAN_BING");
         this._skins[this._skins.length] = new SkinVO(1193,0,500,74,10,28,74,"FS_CM_RED","ATK_QIANG_BING","DIE_QIANG_BING");
         this._skins[this._skins.length] = new SkinVO(1200,0,500,40,12,41,80,"FS_HY1_RED","ATK_WU_DOU_BING","DIE_WU_DOU_BING");
         this._skins[this._skins.length] = new SkinVO(1201,225,500,138,13,60,79,"FS_DHM1_RED","ATK_QIANG_BING","DIE_QIANG_BING");
         this._skins[this._skins.length] = new SkinVO(1202,0,500,450,12,53,93,"FS_BM1_RED","ATK_Shan_Jiang","DIE_TIE_ZHUI_BING");
         this._skins[this._skins.length] = new SkinVO(1203,0,500,155,14,63,130,"FS_GW1_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(1211,0,500,131,11,42,116,"YZ_XB2_RED","ATK_QIANG_BING","DIE_QIANG_BING");
         this._skins[this._skins.length] = new SkinVO(1212,0,500,70,11,35,85,"YZ_XJ2_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(1213,237,500,400,11,51,91,"YZ_WANGB2_RED","ATK_Shan_Jiang","DIE_TIE_ZHUI_BING");
         this._skins[this._skins.length] = new SkinVO(1221,0,500,140,11,32,121,"YZ_XB3_RED","ATK_QIANG_BING","DIE_QIANG_BING");
         this._skins[this._skins.length] = new SkinVO(1222,0,500,90,11,49,95,"YZ_XJ3_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(1223,237,500,450,11,47,104,"YZ_WANGB3_RED","ATK_Shan_Jiang","DIE_TIE_ZHUI_BING");
         this._skins[this._skins.length] = new SkinVO(1224,0,500,75,15,25,69,"FS_GT3_RED","ATK_QIANG_BING","DIE_WU_DOU_BING");
         this._skins[this._skins.length] = new SkinVO(1225,0,500,155,13,59,122,"FS_GY3_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(1226,0,500,156,13,64,155,"FS_NG3_RED","ATK_WU_DOU_BING","DIE_TIE_ZHUI_BING");
         this._skins[this._skins.length] = new SkinVO(2011,0,300,56,11,24,36,"JS_CHANGQIANG_RED","ATK_QIANG_BING","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2012,0,300,80,17,28,40,"JS_ZIBAO_RED","ATK_ZIBAO_JS","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2013,0,300,69,12,28,42,"JS_LANGYABANG_RED","ATK_QIANG_BING","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2014,205,300,280,12,18,38,"JS_DANGONG_RED","ATK_DANGONG_JS","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2015,0,300,54,12,28,42,"JS_DALI_RED","ATK_DALI_JS","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2016,0,300,54,11,30,62,"JS_DACUI_RED","ATK_QIANG_BING","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2017,176,300,400,13,26,42,"JS_HUOQIU_RED","ATK_DANGONG_JS","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2018,0,300,72,11,28,74,"JS_CHAZI_RED","ATK_QIANG_BING","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2019,0,300,44,15,26,56,"JS_DUYE_RED","ATK_DALI_JS","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2020,0,300,105,13,38,80,"JSSL_DACUI_RED","ATK_DALI_JS","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2021,0,300,50,14,34,68,"JSSL_TUDU_RED","ATK_DALI_JS","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2022,189,300,350,13,22,52,"JSSL_HUOQIU_RED","ATK_DANGONG_JS","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2023,0,300,170,12,58,150,"GG_HEIWC_RED","ATK_DALI_JS","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2024,190,300,400,13,58,150,"GG_BAIWC_RED","ATK_DALI_JS","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2031,0,500,63,11,26,50,"JSSL_CHANGQIANG_RED","ATK_QIANG_BING","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2032,0,500,160,19,32,46,"JSSL_ZIBAO_RED","ATK_ZIBAO_JS","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2033,0,500,95,12,30,50,"JSSL_LANGYABANG_RED","ATK_LANGYA_JS","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2034,206,500,380,12,22,40,"JSSL_DANGONG_RED","ATK_DANGONG_JS","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2035,0,500,67,12,30,50,"JSSL_DALI_RED","ATK_DALI_JS","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2036,0,500,112,14,40,124,"GG_DUJIAO_RED","ATK_DALI_JS","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2037,0,500,102,16,42,128,"GG_HANBA_RED","ATK_DALI_JS","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2038,208,500,400,16,44,135,"GG_MUGUI_RED","ATK_DALI_JS","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2039,207,500,400,17,38,114,"GG_SHENV_RED","ATK_DANGONG_JS","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2040,0,500,112,11,76,106,"GG_ZAOCHI_RED","ATK_DALI_JS","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2041,0,500,98,12,96,110,"GG_LILI_RED","ATK_DALI_JS","DIE_JIANG_SHI");
         this._skins[this._skins.length] = new SkinVO(2042,210,500,350,13,54,58,"SS_BAIHUY_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(2043,210,500,350,13,54,58,"SS_BAIHUY1_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(2044,0,300,220,11,90,202,"SS_QINGLONG_RED","ATK_WU_DOU_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(2045,0,300,96,12,46,90,"SS_QINGLONG1_RED","ATK_WU_DOU_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(2046,0,300,130,13,40,55,"SS_XUANWU1_BLUE","ATK_DALI_JS","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(2047,0,300,120,13,60,130,"JY_ZHUQUE_BLUE","ATK_DALI_JS","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(2051,0,400,40,11,20,40,"SHOU_LING_HUANGJIN_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(2052,0,400,47,14,26,36,"SHOU_LING_SHANZEI_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(2053,0,400,40,13,35,60,"SHOU_LING_WUDOU_RED","ATK_WU_DOU_BING","DIE_WU_DOU_BING");
         this._skins[this._skins.length] = new SkinVO(2054,0,400,54,11,38,50,"SHOU_LING_MANBING_RED","ATK_MAN_BING","DIE_MAN_BING");
         this._skins[this._skins.length] = new SkinVO(2055,0,400,39,14,26,40,"SHOU_LING_CIKE_RED","ATK_CI_KE","DIE_CI_KE");
         this._skins[this._skins.length] = new SkinVO(2056,0,400,44,11,28,46,"SHOU_LING_PODAOBING_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(2057,0,400,59,13,20,42,"SHOU_LING_LIANCHUIBING_RED","ATK_LIAN_CHUI_BING","DIE_LIAN_CHUI_BING");
         this._skins[this._skins.length] = new SkinVO(2058,0,400,60,12,18,42,"SHOU_LING_QIANGBING_RED","ATK_QIANG_BING","DIE_QIANG_BING");
         this._skins[this._skins.length] = new SkinVO(2059,0,400,66,12,24,42,"SHOU_LING_TIEZHUIBING_RED","ATK_TIE_ZHUI_BING","DIE_TIE_ZHUI_BING");
         this._skins[this._skins.length] = new SkinVO(2060,0,400,43,16,39,58,"SHOU_LING_QIBING_RED","ATK_QI_BING","DIE_QI_BING");
         this._skins[this._skins.length] = new SkinVO(2061,0,400,52,13,18,44,"SHOU_LING_NVBING_RED","ATK_NV_BING","DIE_NV_BING");
         this._skins[this._skins.length] = new SkinVO(2062,102,400,335,16,24,48,"SHOU_LING_FEIDAOBING_RED","ATK_FEI_DAO_BING","DIE_FEI_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(2063,104,400,450,16,26,44,"SHOU_LING_GONGJIANBING_RED","ATK_GONG_JIAN_BING","DIE_GONG_JIAN_BING");
         this._skins[this._skins.length] = new SkinVO(2064,103,400,390,14,20,42,"SHOU_LING_NUBING_RED","ATK_NU_BING","DIE_NU_BING");
         this._skins[this._skins.length] = new SkinVO(2090,0,300,28,12,24,34,"HUANG_JIN_BING","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(2091,0,300,28,12,24,44,"SHAN_ZEI","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(2092,209,500,500,17,72,76,"TOUSHI_CHE","ATK_TOU_SHI_CHE","DIE_TOU_SHI_CHE");
         this._skins[this._skins.length] = new SkinVO(2500,0,300,168,12,130,128,"SS_BAIHU_BLUE","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(2501,0,300,168,12,130,128,"SS_BAIHU_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(2510,0,300,220,11,90,190,"SS_QINGLONG_BLUE","ATK_WU_DOU_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(2511,0,300,220,11,90,190,"SS_QINGLONG_RED","ATK_WU_DOU_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(2520,0,300,210,13,100,150,"SS_XUANWU_BLUE","ATK_DALI_JS","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(2530,0,300,255,13,196,268,"SS_ZHUQUE_BLUE","ATK_DALI_JS","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(2540,0,300,155,15,96,140,"SS_QILIN_BLUE","ATK_DALI_JS","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(2550,0,300,155,15,96,140,"SS_BAIZE_BLUE","ATK_DALI_JS","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(10010,0,400,47,15,36,56,"JIAN_JIANG_QI_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(10011,0,400,47,15,36,56,"JIAN_JIANG_QI_RED","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(10020,0,400,70,13,36,56,"CHANG_QIANG_HERO_QI_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(10021,0,400,70,13,35,56,"CHANG_QIANG_HERO_QI_RED","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(10030,0,400,66,16,36,56,"DAO_JIANG_QI_BLUE","ATK_DAO_BING","DIE_DAO_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(10031,0,400,66,16,36,56,"DAO_JIANG_QI_RED","ATK_DAO_BING","DIE_DAO_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(10040,100,500,450,12,36,56,"MOU_SHI_QI_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(10041,100,500,450,12,36,56,"MOU_SHI_QI_RED","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(10050,101,400,400,14,36,56,"GONG_JIANG_QI_BLUE","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(10051,101,400,400,14,36,56,"GONG_JIANG_QI_RED","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(10060,0,400,66,14,36,56,"JIAN_JIANG_NV_QI_BLUE","ATK_DAO_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(10061,0,400,66,14,36,56,"JIAN_JIANG_NV_QI_RED","ATK_DAO_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(10070,0,400,52,15,22,44,"JIAN_JIANG_BU_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG");
         this._skins[this._skins.length] = new SkinVO(10071,0,400,52,15,22,44,"JIAN_JIANG_BU_RED","ATK_DAO_BING","DIE_JIAN_JIANG");
         this._skins[this._skins.length] = new SkinVO(10080,0,400,76,11,22,44,"QIANG_JIANG_BU_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(10081,0,400,76,11,22,44,"QIANG_JIANG_BU_RED","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(10090,0,400,80,15,30,50,"DAO_JIANG_BU_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(10091,0,400,80,15,30,50,"DAO_JIANG_BU_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(10100,100,400,380,11,22,52,"MOU_SHI_BU_BLUE","ATK_Shan_Jiang","DIE_MOU_JIANG");
         this._skins[this._skins.length] = new SkinVO(10101,100,400,380,11,22,52,"MOU_SHI_BU_RED","ATK_Shan_Jiang","DIE_MOU_JIANG");
         this._skins[this._skins.length] = new SkinVO(10110,101,400,330,12,26,48,"GONG_JIANG_BU_BLUE","ATK_GONG_JIAN_BING","DIE_GONG_JIANG");
         this._skins[this._skins.length] = new SkinVO(10111,101,400,330,12,26,48,"GONG_JIANG_BU_RED","ATK_GONG_JIAN_BING","DIE_GONG_JIANG");
         this._skins[this._skins.length] = new SkinVO(10120,0,400,52,12,22,44,"JIAN_JIANG_NV_BU_BLUE","ATK_DAO_BING","DIE_NV_BING");
         this._skins[this._skins.length] = new SkinVO(10121,0,400,52,12,22,44,"JIAN_JIANG_NV_BU_RED","ATK_DAO_BING","DIE_NV_BING");
         this._skins[this._skins.length] = new SkinVO(10151,0,400,85,12,50,94,"XIONG_WJ_WANDAO0_RED","ATK_DAO_BING","DIE_NV_BING");
         this._skins[this._skins.length] = new SkinVO(10161,173,500,450,12,45,100,"XIONG_WJ_JUNU0_RED","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(10171,174,450,400,14,34,76,"XIONG_WJ_GONG0_RED","ATK_GONG_JIAN_BING","DIE_GONG_JIAN_BING");
         this._skins[this._skins.length] = new SkinVO(10181,0,400,88,15,46,76,"XIONG_WJ_FU0_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(10201,0,300,62,15,38,81,"QIANG_MI_DANG0_RED","ATK_DAO_BING","DIE_QIANG_BING");
         this._skins[this._skins.length] = new SkinVO(10211,0,300,67,13,35,83,"QIANG_QIANG_RUI0_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(10221,0,300,77,13,35,83,"QIANG_YA_DAN0_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(10231,0,300,70,13,35,83,"QIANG_YUE_JI0_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(10232,211,300,450,13,40,71,"QIANG_QIE_LIJI0_RED","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(10233,0,300,66,13,28,68,"NM_YANG_FENG_RED","ATK_DAO_BING","DIE_QIANG_BING");
         this._skins[this._skins.length] = new SkinVO(10234,0,300,60,13,28,68,"NM_MYC_RED","ATK_DAO_BING","DIE_QIANG_BING");
         this._skins[this._skins.length] = new SkinVO(10235,0,300,60,13,28,68,"NM_AHN_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(10236,223,400,400,12,28,79,"NM_DLDZ_RED","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(10237,0,300,73,13,28,71,"NM_JHSJ_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(10238,0,300,78,13,24,70,"NM_SDDW_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(10239,0,300,117,15,28,66,"NM_MY_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(10240,0,300,159,14,53,127,"NM_WTG_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(10250,0,300,125,11,43,107,"FS_YDZZ_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(10251,0,300,110,15,44,95,"FS_MLYJ_RED","ATK_QIANG_BING","DIE_JIAN_JIANG");
         this._skins[this._skins.length] = new SkinVO(10252,0,300,108,11,28,92,"FS_ZTXF_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(10253,0,300,161,13,70,131,"FS_DCJK_RED","ATK_QIANG_BING","DIE_JIAN_JIANG");
         this._skins[this._skins.length] = new SkinVO(10254,0,300,147,11,59,140,"FS_FCXJ_RED","ATK_QIANG_BING","DIE_JIAN_JIANG");
         this._skins[this._skins.length] = new SkinVO(10255,0,300,151,11,54,140,"FS_ZTXC_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(10256,0,300,141,10,50,142,"FS_SSQX_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(10257,0,300,160,11,52,150,"FS_WTXX_RED","ATK_QIANG_BING","DIE_JIAN_JIANG");
         this._skins[this._skins.length] = new SkinVO(10258,0,300,150,11,61,135,"YZ_SZJ2_RED","ATK_DAO_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(10259,0,300,150,16,67,130,"YZ_TSJ2_RED","ATK_MAN_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(10260,0,300,131,13,91,161,"YZ_YSJ2_RED","ATK_MAN_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(10261,0,300,131,13,91,161,"YZ_YSJ3_RED","ATK_MAN_BING","DIE_DAO_BING");
         this._skins[this._skins.length] = new SkinVO(11000,0,500,66,11,44,58,"CAO_CAO0_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11001,0,500,66,11,44,58,"CAO_CAO0_RED","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11010,0,500,83,11,24,50,"CAO_REN0_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11011,0,500,83,11,24,50,"CAO_REN0_RED","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11020,100,500,400,12,48,54,"GUO_JIA0_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(11021,100,500,400,12,48,54,"GUO_JIA0_RED","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(11030,0,500,101,12,44,56,"LIU_BEI0_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11031,0,500,101,12,44,56,"LIU_BEI0_RED","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11040,0,500,100,13,50,54,"LV_BU0_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11041,0,500,100,13,50,54,"LV_BU0_RED","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11050,0,500,97,13,50,56,"TAI_SHI_CI0_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11051,0,500,97,13,50,56,"TAI_SHI_CI0_RED","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11060,101,500,350,16,46,56,"XIA_HOU_YUAN0_BLUE","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11061,101,500,350,16,46,56,"XIA_HOU_YUAN0_RED","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11070,0,500,59,15,24,46,"LE_JIN0_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG");
         this._skins[this._skins.length] = new SkinVO(11071,0,500,59,15,24,46,"LE_JIN0_RED","ATK_DAO_BING","DIE_JIAN_JIANG");
         this._skins[this._skins.length] = new SkinVO(11080,108,500,400,15,28,54,"ZHANG_JIAO0_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(11081,108,500,400,15,28,54,"ZHANG_JIAO0_RED","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(11090,0,500,99,13,50,60,"ZHAO_YUN0_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11091,0,500,99,13,50,60,"ZHAO_YUN0_RED","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11100,106,500,350,13,50,60,"XIAO_QIAO0_BLUE","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(11101,106,500,350,13,50,60,"XIAO_QIAO0_RED","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(11110,0,500,99,13,50,60,"ZHANG_FEI0_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11111,0,500,99,13,50,60,"ZHANG_FEI0_RED","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11120,0,500,65,15,24,46,"SUN_JIAN0_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG");
         this._skins[this._skins.length] = new SkinVO(11121,0,500,65,15,24,46,"SUN_JIAN0_RED","ATK_DAO_BING","DIE_JIAN_JIANG");
         this._skins[this._skins.length] = new SkinVO(11130,0,500,110,15,24,59,"GUAN_YU0_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11131,0,500,110,15,24,59,"GUAN_YU0_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11140,0,500,110,15,24,59,"YU_JIN0_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(11141,0,500,110,15,24,59,"YU_JIN0_RED","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(11150,0,500,110,15,24,59,"ZHANG_HE0_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(11151,0,500,110,15,24,59,"ZHANG_HE0_RED","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(11160,117,500,110,15,24,59,"SIMA_HUI0_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(11161,116,500,110,15,24,59,"SIMA_HUI0_RED","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(11170,0,500,96,12,34,64,"WEI_YAN0_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11171,0,500,96,12,34,64,"WEI_YAN0_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11180,0,500,90,10,34,50,"DIAN_WEI0_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11181,0,500,90,10,34,50,"DIAN_WEI0_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11190,111,500,400,16,52,64,"HUANG_YUE_YIN0_BLUE","ATK_Shan_Jiang","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(11191,110,500,400,16,52,64,"HUANG_YUE_YIN0_RED","ATK_Shan_Jiang","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(11200,115,500,350,17,26,56,"HUA_TUO0_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(11201,114,500,350,17,26,56,"HUA_TUO0_RED","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(11210,117,500,350,16,50,62,"GUAN_FEN0_BLUE","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(11211,116,500,350,16,50,62,"GUAN_FEN0_RED","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(11220,0,500,90,15,30,58,"ZHANG_LIAO0_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11221,0,500,90,15,30,58,"ZHANG_LIAO0_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11230,0,500,88,12,30,66,"MA_CAO0_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(11231,0,500,88,12,30,66,"MA_CAO0_RED","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(11240,0,500,65,14,36,74,"LIU_CHAN0_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11241,0,500,65,14,36,74,"LIU_CHAN0_RED","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11250,0,500,70,13,46,62,"GAN_NING0_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11251,0,500,70,13,46,62,"GAN_NING0_RED","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11260,119,500,350,15,38,60,"HUANG_GAI0_BLUE","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11261,118,500,350,15,38,60,"HUANG_GAI0_RED","ATK_GONG_JIAN_BING","SDIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11270,0,500,73,12,40,66,"DONG_ZUO0_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11271,0,500,188,11,95,140,"DONG_ZUO0_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11280,120,500,400,12,40,66,"NAN_HUA0_BLUE","ATK_Shan_Jiang","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11281,120,500,400,11,40,66,"NAN_HUA0_RED","ATK_Shan_Jiang","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11290,0,500,90,13,42,64,"XU_SHENG0_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(11291,0,500,90,13,42,64,"XU_SHENG0_RED","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(11300,0,500,71,15,42,68,"ZHOU_TAI0_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11301,0,500,71,15,42,68,"ZHOU_TAI0_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11310,0,500,70,14,56,72,"GUO_HUAI0_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11311,0,500,70,14,56,72,"GUO_HUAI0_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11320,0,500,66,11,44,58,"CAO_CAO01_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11321,0,500,118,12,88,122,"CAO_CAO01_RED","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11330,0,500,90,10,34,50,"DIAN_WEI01_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11331,0,500,196,8,84,116,"DIAN_WEI01_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11340,0,500,87,13,58,72,"LU_KANG0_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11341,0,500,87,13,58,72,"LU_KANG0_RED","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11350,123,500,400,13,46,70,"XIN_XIANY0_BLUE","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(11351,122,500,400,13,46,70,"XIN_XIANY0_RED","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(11360,127,500,400,13,46,70,"ZUOCI0_BLUE","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(11361,126,500,400,13,46,70,"ZUOCI0_RED","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(11370,0,500,87,13,58,72,"WENYING0_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11371,0,500,87,13,58,72,"WENYING0_RED","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11380,0,500,83,11,28,66,"YUANSHAO0_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG");
         this._skins[this._skins.length] = new SkinVO(11381,0,500,83,11,28,66,"YUANSHAO0_RED","ATK_DAO_BING","DIE_JIAN_JIANG");
         this._skins[this._skins.length] = new SkinVO(11390,131,500,400,14,44,64,"LUSU0_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(11391,130,500,400,14,44,64,"LUSU0_RED","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(11400,135,500,350,9,46,70,"HUANGZHONG0_BLUE","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11401,134,500,350,9,46,70,"HUANGZHONG0_RED","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11410,0,500,115,11,32,62,"XU_CHU0_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11411,0,500,115,11,32,62,"XU_CHU0_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11420,0,500,64,12,28,60,"XU_HUANG0_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11421,0,500,64,12,28,60,"XU_HUANG0_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11480,0,500,70,13,40,50,"ZHOUYU0_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11481,0,500,84,9,42,50,"ZHOUYU0_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11490,141,500,350,15,28,46,"LV_MENG0_BLUE","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11491,140,500,350,12,30,52,"LV_MENG0_RED","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11500,0,500,90,13,50,68,"LI_DIAN0_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(11501,0,500,90,13,50,68,"LI_DIAN0_RED","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(11510,0,500,70,15,28,60,"DENG_AI0_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11511,0,500,70,15,28,60,"DENG_AI0_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11520,145,500,400,14,34,56,"XUN_YOU0_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(11521,144,500,400,14,34,56,"XUN_YOU0_RED","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(11530,0,500,60,14,44,48,"XIAHOU_DUN0_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11531,0,500,60,14,44,48,"XIAHOU_DUN0_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11540,0,500,92,11,24,60,"JIANG_WEI0_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(11541,0,500,92,11,24,60,"JIANG_WEI0_RED","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(11550,0,500,60,9,32,66,"DIAO_CHAN0_BLUE","ATK_Shan_Jiang","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(11551,0,500,60,9,32,66,"DIAO_CHAN0_RED","ATK_Shan_Jiang","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(11570,0,500,70,13,28,60,"SUN_CE0_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11571,0,500,70,13,28,60,"SUN_CE0_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11580,151,500,400,14,40,68,"ZHUGE_LIANG0_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(11581,150,500,400,14,40,68,"ZHUGE_LIANG0_RED","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(11590,157,500,350,11,32,64,"SUN_SX0_BLUE","ATK_QIANG_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(11591,156,500,350,11,32,64,"SUN_SX0_RED","ATK_QIANG_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(11600,0,500,82,13,34,70,"SUN_QUAN0_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11601,0,500,82,13,34,70,"SUN_QUAN0_RED","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11610,161,500,350,13,22,52,"DA_QIAO0_BLUE","ATK_Shan_Jiang","DIE_NV_BING");
         this._skins[this._skins.length] = new SkinVO(11611,160,500,350,13,22,52,"DA_QIAO0_RED","ATK_Shan_Jiang","DIE_NV_BING");
         this._skins[this._skins.length] = new SkinVO(11620,0,500,78,12,26,62,"GUAN_XIN0_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(11621,0,500,78,12,26,62,"GUAN_XIN0_RED","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(11680,166,500,350,14,46,72,"PANG_TONG0_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(11681,165,500,350,14,46,72,"PANG_TONG0_RED","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(11690,0,500,87,13,38,80,"CAO_PI0_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11691,0,500,87,13,38,80,"CAO_PI0_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11710,0,500,60,13,38,72,"PANG_DE0_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11711,0,500,60,13,38,72,"PANG_DE0_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11720,178,500,350,12,22,76,"XU_SHU0_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(11721,177,500,350,12,22,76,"XU_SHU0_RED","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(11730,182,500,350,15,34,70,"SI_MA_YI0_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(11731,181,500,350,15,34,70,"SI_MA_YI0_RED","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(11740,186,500,350,15,26,68,"ZHEN_JI0_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(11741,185,500,350,15,26,68,"ZHEN_JI0_RED","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(11750,0,500,65,13,26,63,"CHEN_JIN0_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11751,0,500,65,13,26,63,"CHEN_JIN0_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11760,0,500,66,12,32,60,"LIN_TONG0_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(11761,0,500,66,12,32,60,"LIN_TONG0_RED","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(11770,0,500,70,13,22,60,"LIN_LUXUN0_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11771,0,500,70,13,22,60,"LIN_LUXUN0_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11780,191,500,350,15,34,76,"LHUO_QUBING0_BLUE","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(11790,0,500,98,13,36,74,"WEI_QIN0_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(11810,195,800,550,13,32,68,"SIMA_HUI0_BLUE","ATK_GONG_JIAN_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(11820,0,500,128,12,35,94,"BAI_QI0_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12000,0,500,75,11,46,58,"CAO_CAO_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12001,0,500,75,11,46,58,"CAO_CAO_RED","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12010,0,500,115,11,28,48,"CAO_REN_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12011,0,500,115,11,28,48,"CAO_REN_RED","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12020,100,500,450,12,48,54,"GUO_JIA_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(12021,100,500,450,12,48,54,"GUO_JIA_RED","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(12030,0,500,101,12,44,56,"LIU_BEI_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12031,0,500,101,12,44,56,"LIU_BEI_RED","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12040,0,500,138,13,50,54,"LV_BU_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12041,0,500,138,13,50,54,"LV_BU_RED","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12050,0,500,97,13,50,56,"TAI_SHI_CI_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12051,0,500,97,13,50,56,"TAI_SHI_CI_RED","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12060,105,500,350,14,46,56,"XIA_HOU_YUAN_BLUE","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12061,105,500,350,14,46,56,"XIA_HOU_YUAN_RED","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12070,0,500,80,13,30,50,"LE_JIN_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG");
         this._skins[this._skins.length] = new SkinVO(12071,0,500,80,13,30,50,"LE_JIN_RED","ATK_DAO_BING","DIE_JIAN_JIANG");
         this._skins[this._skins.length] = new SkinVO(12080,109,500,450,11,28,54,"ZHANG_JIAO_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(12081,109,500,540,11,28,54,"ZHANG_JIAO_RED","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(12090,0,500,140,13,50,60,"ZHAO_YUN_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12091,0,500,140,13,50,60,"ZHAO_YUN_RED","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12100,107,500,350,13,50,60,"XIAO_QIAO_BLUE","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(12101,107,500,420,13,50,60,"XIAO_QIAO_RED","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(12110,0,500,129,13,50,60,"ZHANG_FEI_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12111,0,500,129,13,50,60,"ZHANG_FEI_RED","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12120,0,500,80,15,24,46,"SUN_JIAN_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG");
         this._skins[this._skins.length] = new SkinVO(12121,0,500,80,15,24,46,"SUN_JIAN_RED","ATK_DAO_BING","DIE_JIAN_JIANG");
         this._skins[this._skins.length] = new SkinVO(12130,0,500,110,15,24,59,"GUAN_YU_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12131,0,500,110,15,24,59,"GUAN_YU_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12140,0,500,110,15,24,59,"YU_JIN_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(12141,0,500,110,15,24,59,"YU_JIN_RED","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(12150,0,500,110,15,24,59,"ZHANG_HE_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(12151,0,500,110,15,24,59,"ZHANG_HE_RED","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(12160,117,500,110,15,24,59,"SIMA_HUI_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(12161,116,500,110,15,24,59,"SIMA_HUI_RED","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(12170,0,500,110,11,36,66,"WEI_YAN_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12171,0,500,110,11,36,66,"WEI_YAN_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12180,0,500,110,9,36,56,"DIAN_WEI_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12181,0,500,110,9,36,56,"DIAN_WEI_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12190,113,500,450,14,50,68,"HUANG_YUE_YIN_BLUE","ATK_Shan_Jiang","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(12191,112,500,540,14,50,68,"HUANG_YUE_YIN_RED","ATK_Shan_Jiang","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(12200,115,500,350,15,36,58,"HUA_TUO_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(12201,114,500,350,15,36,58,"HUA_TUO_RED","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(12210,117,500,420,14,56,68,"GUAN_FEN_BLUE","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(12211,116,500,420,14,56,68,"GUAN_FEN_RED","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(12220,0,500,130,11,38,64,"ZHANG_LIAO_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12221,0,500,130,11,38,64,"ZHANG_LIAO_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12230,0,500,130,14,32,62,"MA_CAO_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(12231,0,500,130,14,32,62,"MA_CAO_RED","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(12240,0,500,77,11,48,70,"LIU_CHAN_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12241,0,500,77,11,48,70,"LIU_CHAN_RED","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12250,0,500,100,11,49,70,"GAN_NING_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12251,0,500,100,11,49,70,"GAN_NING_RED","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12260,119,500,420,13,42,64,"HUANG_GAI_BLUE","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12261,118,500,420,13,42,64,"HUANG_GAI_RED","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12270,0,500,99,11,52,70,"DONG_ZUO_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12271,0,500,99,11,50,70,"DONG_ZUO_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12280,121,500,450,12,40,66,"NAN_HUA_BLUE","ATK_Shan_Jiang","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12281,121,500,450,11,95,140,"NAN_HUA_RED","ATK_Shan_Jiang","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12290,0,500,112,10,36,64,"XU_SHENG_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(12291,0,500,112,10,36,64,"XU_SHENG_RED","ATK_QIANG_BING","DIE_QIANG_JIANGS");
         this._skins[this._skins.length] = new SkinVO(12300,0,500,86,11,42,72,"ZHOU_TAI_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12301,0,500,86,11,42,72,"ZHOU_TAI_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12310,0,500,95,11,56,78,"GUO_HUAI_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12311,0,500,95,11,56,78,"GUO_HUAI_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12340,0,500,126,11,62,72,"LU_KANG_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12341,0,500,126,11,62,72,"LU_KANG_RED","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12350,125,500,420,11,50,70,"XIN_XIANY_BLUE","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(12351,124,500,420,11,50,70,"XIN_XIANY_RED","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(12360,129,500,400,13,46,70,"ZUOCI_BLUE","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(12361,128,500,400,13,46,70,"ZUOCI_RED","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(12370,0,500,87,13,58,72,"WENYING_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12371,0,500,87,13,58,72,"WENYING_RED","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12380,0,500,98,10,36,70,"YUANSHAO_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG");
         this._skins[this._skins.length] = new SkinVO(12381,0,500,98,10,36,70,"YUANSHAO_RED","ATK_DAO_BING","DIE_JIAN_JIANG");
         this._skins[this._skins.length] = new SkinVO(12390,133,500,400,12,44,68,"LUSU_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(12391,132,500,400,12,44,68,"LUSU_RED","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(12400,137,500,350,9,46,70,"HUANGZHONG_BLUE","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12401,136,500,350,9,46,70,"HUANGZHONG_RED","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12410,0,500,110,9,34,66,"XU_CHU_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12411,0,500,110,9,34,66,"XU_CHU_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12420,0,500,95,9,30,68,"XU_HUANG_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12421,0,500,95,9,30,68,"XU_HUANG_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12430,0,500,99,11,47,58,"SD_JIAN_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12440,0,500,97,11,47,58,"SD_DAO_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12450,0,500,115,10,47,58,"SD_QIANG_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12460,138,500,380,11,47,58,"SD_GONG_BLUE","ATK_GONG_JIAN_BING","SDIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12470,139,500,420,11,47,58,"SD_MOU_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(12480,0,500,70,13,40,50,"ZHOUYU_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12481,0,500,84,9,42,50,"ZHOUYU_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12490,143,500,350,15,28,46,"LV_MENG_BLUE","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12491,142,500,350,12,30,52,"LV_MENG_RED","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12500,0,500,116,10,50,74,"LI_DIAN_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(12501,0,500,116,10,50,74,"LI_DIAN_RED","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(12510,0,500,100,9,34,60,"DENG_AI_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12511,0,500,100,9,34,60,"DENG_AI_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12520,147,500,400,11,38,56,"XUN_YOU_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(12521,146,500,400,11,38,56,"XUN_YOU_RED","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(12530,0,500,52,11,38,58,"XIAHOU_DUN_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12531,0,500,52,11,38,58,"XIAHOU_DUN_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12540,0,500,100,10,30,68,"JIANG_WEI_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(12541,0,500,100,10,30,68,"JIANG_WEI_RED","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(12550,0,500,65,9,36,70,"DIAO_CHAN_BLUE","ATK_Shan_Jiang","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(12551,0,500,65,9,36,70,"DIAO_CHAN_RED","ATK_Shan_Jiang","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(12570,0,500,92,11,30,64,"SUN_CE_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12571,0,500,92,11,30,64,"SUN_CE_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12580,153,500,450,14,38,72,"ZHUGE_LIANG_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(12581,152,500,450,14,38,72,"ZHUGE_LIANG_RED","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(12590,159,500,400,9,36,68,"SUN_SX_BLUE","ATK_QIANG_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(12591,158,500,400,9,36,68,"SUN_SX_RED","ATK_QIANG_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(12600,0,500,92,9,36,74,"SUN_QUAN_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12601,0,500,92,9,36,74,"SUN_QUAN_RED","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12610,163,500,400,10,26,60,"DA_QIAO_BLUE","ATK_GONG_JIAN_BING","DIE_NV_BING");
         this._skins[this._skins.length] = new SkinVO(12611,162,500,400,10,26,60,"DA_QIAO_RED","ATK_GONG_JIAN_BING","DIE_NV_BING");
         this._skins[this._skins.length] = new SkinVO(12620,0,500,106,10,26,66,"GUAN_XIN_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(12621,0,500,106,10,26,66,"GUAN_XIN_RED","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(12630,0,500,99,11,47,58,"CJ_JIAN_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12640,0,500,97,11,47,58,"CJ_DAO_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12650,0,500,115,10,47,58,"CJ_QIANG_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12660,154,500,380,11,47,58,"CJ_GONG_BLUE","ATK_GONG_JIAN_BING","SDIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12670,155,500,420,11,47,58,"CJ_MOU_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(12680,168,500,350,12,43,72,"PANG_TONG_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(12681,167,500,350,12,43,72,"PANG_TONG_RED","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(12690,0,500,87,11,38,85,"CAO_PI_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12691,0,500,87,11,38,85,"CAO_PI_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12710,0,500,74,13,38,72,"PANG_DE_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12711,0,500,74,13,38,72,"PANG_DE_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12720,180,500,350,11,30,76,"XU_SHU_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(12721,179,500,350,11,30,76,"XU_SHU_RED","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(12730,184,500,350,12,40,84,"SI_MA_YI_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(12731,183,500,350,12,40,84,"SI_MA_YI_RED","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(12740,188,500,350,12,28,66,"ZHEN_JI_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(12741,187,500,350,12,28,66,"ZHEN_JI_RED","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(12750,0,500,74,11,26,68,"CHEN_JIN_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12751,0,500,74,11,26,68,"CHEN_JIN_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12760,0,500,66,12,32,60,"LIN_TONG_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(12761,0,500,66,12,32,60,"LIN_TONG_RED","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(12770,0,500,70,13,22,60,"LIN_LUXUN_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12771,0,500,70,13,22,60,"LIN_LUXUN_RED","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12780,192,400,350,11,34,76,"LHUO_QUBING_BLUE","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12790,0,500,139,10,37,79,"WEI_QIN_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(12810,0,800,600,13,32,68,"SIMA_HUI_BLUE","ATK_GONG_JIAN_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12811,0,800,600,13,32,68,"SIMA_HUI_RED","ATK_GONG_JIAN_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(12820,0,500,134,9,49,100,"BAI_QI_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(13000,0,500,64,10,36,66,"CAO_CAO2_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(13010,0,500,115,11,30,78,"CAO_REN2_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(13020,215,500,450,9,30,83,"GUO_JIA2_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(13030,0,500,101,12,44,56,"LIU_BEI2_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(13040,0,500,140,9,46,90,"LV_BU2_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(13050,0,500,94,10,32,64,"TAI_SHI_CI2_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(13080,109,500,450,11,28,76,"ZHANG_JIAO2_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(13090,0,500,145,11,54,76,"ZHAO_YUN2_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(13100,175,500,350,9,50,78,"XIAOQIAO2_BLUE","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(13110,0,500,129,13,40,138,"ZHANG_FEI2_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(13130,0,500,122,9,46,118,"GUAN_YU2_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(13170,0,500,139,10,36,72,"WEI_YAN2_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(13180,0,500,110,9,36,56,"DIAN_WEI2_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(13200,212,500,350,10,22,55,"HUA_TUO2_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(13230,0,500,120,10,31,79,"MA_CAO2_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(13270,0,500,92,10,37,71,"DONG_ZUO2_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(13280,164,500,500,8,34,76,"NAN_HUA2_BLUE","ATK_Shan_Jiang","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(13390,217,500,400,12,34,72,"LUSU2_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(13400,213,500,350,11,33,76,"HUANGZHONG2_BLUE","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(13410,0,500,90,9,26,66,"XU_CHU2_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(13550,0,500,350,8,30,80,"DIAO_CHAN2_BLUE","ATK_Shan_Jiang","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(13580,153,500,450,11,38,88,"ZHU_GE_LIANG2_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(13600,0,500,92,9,36,74,"SUN_QUAN2_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(12700,169,500,400,13,46,70,"ZUOCI2_BLUE","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(13730,197,500,450,8,40,84,"SI_MA_YI2_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(13770,0,500,100,10,33,70,"LIN_LUXUN2_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(13810,196,800,650,13,32,68,"SIMA_HUI2_BLUE","ATK_GONG_JIAN_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(14000,0,500,118,9,48,100,"CAO_CAO3_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(14010,0,500,162,9,24,65,"CAO_REN3_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(14020,216,500,450,9,37,88,"GUO_JIA3_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(14030,0,500,138,12,43,105,"LIU_BEI3_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(14040,0,500,140,7,60,124,"LV_BU3_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(14050,0,500,97,7,46,93,"TAI_SHI_CI3_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(14060,220,500,350,9,60,107,"XIA_HOU_YUAN3_BLUE","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(14070,0,500,92,7,36,82,"LE_JIN3_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG");
         this._skins[this._skins.length] = new SkinVO(14080,109,500,450,10,47,96,"ZHANG_JIAO3_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(14090,0,500,122,9,43,65,"ZHAO_YUN3_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(14100,175,500,350,9,42,85,"XIAOQIAO3_BLUE","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(14110,0,500,115,11,36,84,"ZHANG_FEI3_BLUE","ATK_QIANG_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(14120,0,500,124,10,38,99,"SUN_JIAN3_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG");
         this._skins[this._skins.length] = new SkinVO(14130,0,500,144,9,46,120,"GUAN_YU3_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(14140,0,500,145,8,37,93,"YU_JIN3_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(14150,0,500,110,8,46,93,"ZHANG_HE3_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(14170,0,500,183,9,46,108,"WEI_YAN3_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(14180,0,500,110,9,36,56,"DIAN_WEI3_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(14190,113,500,450,8,50,105,"HUANG_YUE_YIN3_BLUE","ATK_Shan_Jiang","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(14200,212,500,500,11,34,83,"HUA_TUO3_BLUE","ATK_Shan_Jiang","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(14210,221,500,420,11,51,105,"GUAN_FEN3_BLUE","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(14220,0,500,104,8,35,85,"ZHANG_LIAO3_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(14240,0,500,91,7,62,87,"LIU_CHAN3_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(14340,0,500,130,8,43,122,"LU_KANG3_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(14260,222,500,420,9,40,91,"HUANG_GAI3_BLUE","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(14270,0,500,122,10,52,102,"DONG_ZUO3_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(14280,164,500,500,9,53,98,"NAN_HUA3_BLUE","ATK_Shan_Jiang","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(14290,0,500,138,8,32,82,"XU_SHENG3_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(14300,0,500,90,8,32,87,"ZHOU_TAI3_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(14230,0,500,92,10,45,83,"MA_CAO3_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(14250,0,500,100,11,49,70,"GAN_NING3_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(14350,125,500,420,8,43,91,"XIN_XIANY3_BLUE","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(14370,0,500,103,8,58,100,"WENYING3_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(14380,0,500,125,7,36,75,"YUANSHAO3_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG");
         this._skins[this._skins.length] = new SkinVO(14390,218,500,400,12,68,105,"LUSU3_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(14400,214,500,350,12,46,90,"HUANGZHONG3_BLUE","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(14410,0,500,135,9,36,70,"XU_CHU3_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(14420,0,500,80,8,40,81,"XU_HUANG3_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(14480,0,500,76,10,43,94,"ZHOUYU3_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(14490,143,500,350,9,25,67,"LV_MENG3_BLUE","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(14500,0,500,136,6,52,105,"LI_DIAN3_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(14510,0,500,102,8,42,83,"DENG_AI3_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(14520,147,500,400,8,33,82,"XUN_YOU3_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(14530,0,500,111,9,45,123,"XIAHOU_DUN3_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(14540,0,500,116,8,33,79,"JIANG_WEI3_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(14550,0,500,350,8,42,104,"DIAO_CHAN3_BLUE","ATK_Shan_Jiang","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(14570,0,500,94,8,33,80,"SUN_CE3_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(14580,198,500,500,8,37,88,"ZHU_GE_LIANG3_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(14590,159,500,400,10,39,82,"SUN_SX3_BLUE","ATK_QIANG_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(14600,0,500,92,9,36,74,"SUN_QUAN3_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(14610,163,500,400,10,32,80,"DA_QIAO3_BLUE","ATK_GONG_JIAN_BING","DIE_NV_BING");
         this._skins[this._skins.length] = new SkinVO(14620,0,500,112,8,38,94,"GUAN_XIN3_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(14680,168,500,350,12,43,72,"PANG_TONG3_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(14690,0,500,110,9,39,85,"CAO_PI3_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(14700,169,500,400,9,61,98,"ZUOCI3_BLUE","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(14710,0,500,95,8,38,88,"PANG_DE3_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(14720,180,500,350,8,32,80,"XU_SHU3_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(14730,219,500,450,9,40,84,"SI_MA_YI3_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(14740,188,500,350,8,46,88,"ZHEN_JI3_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(14750,0,500,82,7,35,85,"CHEN_JIN3_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(14760,0,500,116,9,48,100,"LIN_TONG3_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(14770,0,500,120,8,41,92,"LIN_LUXUN3_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(14780,192,400,350,8,58,106,"LHUO_QUBING3_BLUE","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(14790,0,500,113,8,48,98,"WEI_QIN3_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(14810,199,800,650,8,36,90,"SIMA_HUI3_BLUE","ATK_GONG_JIAN_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(14820,0,500,128,12,35,94,"BAI_QI3_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(15000,0,500,130,8,49,136,"CAO_CAO4_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(15010,0,500,126,7,36,96,"CAO_REN4_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(15020,232,500,500,7,58,102,"GUO_JIA4_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(15030,0,500,157,9,46,100,"LIU_BEI4_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(15040,0,500,162,5,68,110,"LV_BU4_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(15050,0,500,120,9,65,124,"TAI_SHI_CI4_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(15060,230,500,450,8,57,125,"XIA_HOU_YUAN4_BLUE","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(15090,0,500,140,8,55,79,"ZHAO_YUN4_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(15100,234,500,400,9,69,109,"XIAOQIAO4_BLUE","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(15110,0,500,130,6,44,91,"ZHANG_FEI4_BLUE","ATK_QIANG_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(15130,0,500,161,8,72,133,"GUAN_YU4_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(15170,0,500,133,7,70,101,"WEI_YAN4_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(15180,0,500,131,6,41,81,"DIAN_WEI4_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(15200,227,500,550,9,45,125,"HUA_TUO4_BLUE","ATK_Shan_Jiang","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(15230,0,500,117,11,32,93,"MA_CAO4_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(15240,0,500,122,10,45,107,"LIU_CHAN4_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(15250,0,500,140,7,78,133,"GAN_NING4_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(15270,0,500,135,7,75,158,"DONG_ZUO4_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(15280,226,500,550,6,49,120,"NAN_HUA4_BLUE","ATK_Shan_Jiang","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(15390,229,500,450,12,63,117,"LU_SU4_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(15400,235,500,400,9,68,120,"HUANGZHONG4_BLUE","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(15490,238,500,350,9,25,67,"LV_MENG4_BLUE","ATK_GONG_JIAN_BING","DIE_GONG_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(15520,239,500,400,7,63,110,"XUN_YOU4_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(15530,0,500,130,9,52,124,"XIAHOU_DUN4_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(15540,0,500,130,7,50,98,"JIANG_WEI4_BLUE","ATK_QIANG_BING","DIE_QIANG_JIANG");
         this._skins[this._skins.length] = new SkinVO(15550,0,500,200,7,47,107,"DIAO_CHAN4_BLUE","ATK_Shan_Jiang","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(15580,198,500,500,6,39,90,"ZHU_GE_LIANG4_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(15590,198,500,450,8,48,102,"SUN_SX4_BLUE","ATK_QIANG_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(15600,0,500,126,8,49,126,"SUN_QUAN4_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(15610,233,500,450,7,47,121,"DA_QIAO4_BLUE","ATK_GONG_JIAN_BING","DIE_NV_BING");
         this._skins[this._skins.length] = new SkinVO(15700,228,500,450,7,45,101,"ZUOCI4_BLUE","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(15730,231,500,500,7,59,106,"SI_MA_YI4_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI");
         this._skins[this._skins.length] = new SkinVO(15770,0,500,135,9,62,98,"LIN_LUXUN4_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(15810,224,800,700,6,43,92,"SIMA_HUI4_BLUE","ATK_GONG_JIAN_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(15820,0,500,200,6,54,130,"BAI_QI4_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(16010,0,500,130,8,40,96,"CAO_REN5_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(16180,0,500,145,8,43,105,"DIAN_WEI5_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(16200,244,500,550,8,82,155,"HUA_TUO5_BLUE","ATK_Shan_Jiang","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(16250,0,500,150,7,82,134,"GAN_NING5_BLUE","ATK_DAO_BING","DIE_JIAN_JIANG_QI");
         this._skins[this._skins.length] = new SkinVO(16280,226,600,600,6,100,158,"NAN_HUA5_BLUE","ATK_Shan_Jiang","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(16040,0,500,230,7,81,160,"LV_BU5_BLUE","ATK_QIANG_BING","DIE_CHANG_QIANG_QI");
         this._skins[this._skins.length] = new SkinVO(16390,243,500,500,12,91,111,"LU_SU5_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(16580,242,550,550,6,50,109,"ZHU_GE_LIANG5_BLUE","ATK_Shan_Jiang","DIE_MOU_SHI_QI");
         this._skins[this._skins.length] = new SkinVO(16700,241,500,450,6,45,101,"ZUOCI5_BLUE","ATK_GONG_JIAN_BING","DIE_NV_QI_JIANG");
         this._skins[this._skins.length] = new SkinVO(16770,0,500,145,9,55,98,"LIN_LUXUN5_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(16810,224,800,700,6,74,109,"SIMA_HUI5_BLUE","ATK_GONG_JIAN_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(16820,0,500,300,6,34,134,"BAI_QI5_BLUE","ATK_DAO_BING","DIE_DAO_JIANG");
         this._skins[this._skins.length] = new SkinVO(8001,0,0,0,0,132,50,"SEQ_BIAO_CHE","","");
         this._roles[this._roles.length] = new HeroInfoVO(999,0,3,102,1,1,1,1019,11620,11621,"抗匈武将","GUAN_XING","抗匈武将");
      }
      
      public function convertSkin() : void
      {
         var arr:Array = null;
         var vo:SkinVO = null;
         var index:int = 0;
         var timer:SysTimer = null;
         var convert:Function = null;
         convert = function():void
         {
            if(index >= arr.length)
            {
               return;
            }
            AssetManager.findRoleSeq(arr[index].skinID);
            ++index;
         };
      }
      
      public function findSkin(param1:int) : SkinVO
      {
         var _loc2_:SkinVO = null;
         for each(_loc2_ in this._skins)
         {
            if(_loc2_.skinID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findInfo(param1:int) : RoleInfoVO
      {
         var _loc2_:RoleInfoVO = null;
         for each(_loc2_ in this._roles)
         {
            if(_loc2_.roleID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function newNpcList() : Array
      {
         var _loc2_:ConstNpcVO = null;
         var _loc3_:Class = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._npcs)
         {
            _loc3_ = NpcFactory.newNpcVO(_loc2_.npcID);
            _loc1_[_loc1_.length] = new _loc3_(_loc2_) as GameNpcVO;
         }
         return _loc1_;
      }
      
      public function get roles() : Vector.<RoleInfoVO>
      {
         return this._roles;
      }
   }
}

