package file
{
   import com.mogames.event.GameSignal;
   import com.mogames.event.SignalManager;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.boon.HYInfoVO;
   import mogames.gameData.boon.HYRewardVO;
   import mogames.gameData.flag.FlagProxy;
   
   public class HuoYueConfig
   {
      
      private static var _instance:HuoYueConfig;
      
      private var _list:Array;
      
      private var _rewards:Array;
      
      public function HuoYueConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : HuoYueConfig
      {
         if(!_instance)
         {
            _instance = new HuoYueConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = [];
         this._list[this._list.length] = new HYInfoVO(302,5,"酒馆中招募任意武将。");
         this._list[this._list.length] = new HYInfoVO(303,5,"提升酒馆任意武将好感度1次。");
         this._list[this._list.length] = new HYInfoVO(304,15,"每日签到。");
         this._list[this._list.length] = new HYInfoVO(305,5,"使用升级丹提升任意武将等级。");
         this._list[this._list.length] = new HYInfoVO(306,5,"赏赐任意武将1次。");
         this._list[this._list.length] = new HYInfoVO(307,20,"内政时对任意郡城征收1次。");
         this._list[this._list.length] = new HYInfoVO(308,10,"劝降地牢内任意武将1次。");
         this._list[this._list.length] = new HYInfoVO(309,20,"制作任意卷轴。");
         this._list[this._list.length] = new HYInfoVO(310,15,"搜刮百姓2次。");
         this._list[this._list.length] = new HYInfoVO(311,15,"补充粮草1次。");
         this._list[this._list.length] = new HYInfoVO(312,20,"通关任意关卡3次。");
         this._rewards = [];
         this._rewards[this._rewards.length] = new HYRewardVO(152,25,[new BaseRewardVO(10000,4000),new BaseRewardVO(11071,4)]);
         this._rewards[this._rewards.length] = new HYRewardVO(153,50,[new BaseRewardVO(10000,8000),new BaseRewardVO(10008,200),new BaseRewardVO(10530,2)]);
         this._rewards[this._rewards.length] = new HYRewardVO(154,75,[new BaseRewardVO(10000,12000),new BaseRewardVO(10008,200),new BaseRewardVO(10531,2)]);
         this._rewards[this._rewards.length] = new HYRewardVO(155,100,[new BaseRewardVO(10000,20000),new BaseRewardVO(10008,200),new BaseRewardVO(10532,2)]);
      }
      
      public function addHY(param1:int) : void
      {
         FlagProxy.instance().limitFlag.changeValue(param1);
         SignalManager.signalUI.dispatchEvent({
            "signal":GameSignal.REFRESH_UI_TIP,
            "type":"huoyue"
         });
      }
      
      public function get infos() : Array
      {
         return this._list;
      }
      
      public function get rewards() : Array
      {
         return this._rewards;
      }
      
      public function get total() : int
      {
         var _loc1_:int = 0;
         var _loc2_:HYInfoVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.isComplete)
            {
               _loc1_ += _loc2_.num;
            }
         }
         return _loc1_;
      }
      
      public function get tipGet() : Boolean
      {
         var _loc1_:HYRewardVO = null;
         for each(_loc1_ in this._rewards)
         {
            if(_loc1_.canGet)
            {
               return true;
            }
         }
         return false;
      }
   }
}

