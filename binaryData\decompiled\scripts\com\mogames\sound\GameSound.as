package com.mogames.sound
{
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.media.Sound;
   import flash.media.SoundChannel;
   import flash.media.SoundTransform;
   import flash.net.URLRequest;
   
   public class GameSound
   {
      
      public var soundVO:GameSoundVO;
      
      private var _sound:Sound;
      
      private var _sc:SoundChannel;
      
      private var _st:SoundTransform;
      
      private var _count:int;
      
      public function GameSound(param1:GameSoundVO)
      {
         super();
         this.soundVO = param1;
         if(this.soundVO.auto)
         {
            this.loadSound();
         }
         this._st = new SoundTransform(this.soundVO.volume);
      }
      
      public function play(param1:int = 0) : void
      {
         if(!this._sound)
         {
            this.loadSound();
         }
         this._sc = this._sound.play(0,param1);
         if(!this._sc)
         {
            return;
         }
         this._sc.soundTransform = this._st;
         this._sc.addEventListener(Event.SOUND_COMPLETE,this.onComplete);
      }
      
      public function playCount(param1:int = 2) : void
      {
         if(this._count >= param1)
         {
            return;
         }
         this.play();
         ++this._count;
      }
      
      public function stop() : void
      {
         if(!this._sc)
         {
            return;
         }
         this.onComplete(null);
         this._sc.stop();
      }
      
      private function onComplete(param1:Event) : void
      {
         --this._count;
         if(this._count <= 0)
         {
            this._count = 0;
         }
         if(this._sc)
         {
            this._sc.removeEventListener(Event.SOUND_COMPLETE,this.onComplete);
         }
      }
      
      private function loadSound() : void
      {
         this._sound = new Sound();
         this._sound.addEventListener(Event.COMPLETE,this.onLoaded);
         this._sound.addEventListener(IOErrorEvent.IO_ERROR,this.onError);
         this._sound.load(new URLRequest(this.soundVO.url));
      }
      
      private function onError(param1:IOErrorEvent) : void
      {
      }
      
      private function onLoaded(param1:Event) : void
      {
         this._sound.removeEventListener(Event.COMPLETE,this.onLoaded);
      }
   }
}

