package mogames.gameBuff.buff
{
   import mogames.gameBuff.base.TimeRoleBuff;
   import mogames.gameData.buff.ConstBuffVO;
   import mogames.gameEffect.EffectManager;
   
   public class AllCritBuff extends TimeRoleBuff
   {
      
      public function AllCritBuff(param1:ConstBuffVO)
      {
         super(param1);
      }
      
      override protected function createData() : void
      {
         _owner.allCrit = true;
         EffectManager.addHeadWord("持续暴击！",_owner.center.x,_owner.center.y);
      }
      
      override protected function onCleanRole() : void
      {
         _owner.allCrit = false;
      }
   }
}

