package mogames.gameData.base.func
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import file.GoodConfig;
   import mogames.gameData.base.UseProxy;
   import mogames.gameData.game.LackVO;
   import mogames.gameData.good.base.KacaoVO;
   import mogames.gameData.good.equip.GameEquipVO;
   
   public class PFJLVO
   {
      
      private var _oldID:Oint = new Oint();
      
      private var _newID:Oint = new Oint();
      
      private var _list:Array;
      
      public function PFJLVO(param1:int, param2:int, param3:Array)
      {
         super();
         MathUtil.saveINT(this._oldID,param1);
         MathUtil.saveINT(this._newID,param2);
         this._list = param3;
      }
      
      public function checkLack() : LackVO
      {
         return UseProxy.instance().checkLack(this._list);
      }
      
      public function newFashion(param1:GameEquipVO) : GameEquipVO
      {
         var _loc6_:KacaoVO = null;
         var _loc2_:GameEquipVO = GoodConfig.instance().newGameEquip(this.newID,param1.numHole);
         _loc2_.setForgeLevel(param1.level);
         var _loc3_:int = 0;
         var _loc4_:int = int(param1.kacaoList.length);
         var _loc5_:Vector.<KacaoVO> = _loc2_.kacaoList;
         while(_loc3_ < _loc4_)
         {
            _loc6_ = param1.kacaoList[_loc3_];
            if(_loc6_.attVO)
            {
               _loc5_[_loc3_].addCard(_loc6_.attVO.goodID);
            }
            _loc3_++;
         }
         return _loc2_;
      }
      
      public function get oldID() : int
      {
         return MathUtil.loadINT(this._oldID);
      }
      
      public function get needList() : Array
      {
         return this._list;
      }
      
      private function get newID() : int
      {
         return MathUtil.loadINT(this._newID);
      }
   }
}

