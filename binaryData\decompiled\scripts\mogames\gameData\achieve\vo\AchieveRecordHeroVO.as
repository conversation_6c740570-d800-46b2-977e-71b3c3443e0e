package mogames.gameData.achieve.vo
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.achieve.base.AchieveLockVO;
   import mogames.gameData.role.RecordProxy;
   
   public class AchieveRecordHeroVO extends AchieveLockVO
   {
      
      private var _num:Oint = new Oint();
      
      public function AchieveRecordHeroVO(param1:int, param2:int, param3:Array, param4:String, param5:String)
      {
         super(param1,param3,param4,param5);
         MathUtil.saveINT(this._num,param2);
      }
      
      override public function get isOpen() : Boolean
      {
         return RecordProxy.instance().recordNum(3) >= MathUtil.loadINT(this._num);
      }
      
      override public function get saveData() : String
      {
         return [id,MathUtil.loadINT(_open),MathUtil.loadINT(_get)].join("H");
      }
      
      override public function set loadData(param1:Array) : void
      {
         MathUtil.saveINT(_open,int(param1[1]));
         MathUtil.saveINT(_get,int(param1[2]));
      }
   }
}

