package com.mogames.display
{
   import com.mogames.event.UIEvent;
   import com.mogames.utils.MethodUtil;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import mogames.gameAsset.AssetManager;
   
   public class MovieClipUI extends EventSprite
   {
      
      protected var ui:Object;
      
      protected var _btnList:Array;
      
      public function MovieClipUI(param1:String = "", param2:Boolean = true)
      {
         super();
         if(param1 == "")
         {
            return;
         }
         if(param2)
         {
            this.ui = AssetManager.findUI(param1,this);
         }
         else
         {
            this.ui = MethodUtil.transMC(AssetManager.assetTemp.findAsset(param1) as MovieClip,this);
         }
      }
      
      protected function initBtns(param1:Array) : void
      {
         var _loc2_:SimpleButton = null;
         if(!param1)
         {
            return;
         }
         this._btnList = param1;
         for each(_loc2_ in param1)
         {
            _loc2_.addEventListener(MouseEvent.MOUSE_DOWN,this.onDown,false,0,true);
         }
      }
      
      protected function removeBtnListener(param1:Array) : void
      {
         var _loc2_:SimpleButton = null;
         if(!param1)
         {
            return;
         }
         for each(_loc2_ in param1)
         {
            _loc2_.removeEventListener(MouseEvent.MOUSE_DOWN,this.onDown);
         }
      }
      
      protected function onDown(param1:MouseEvent) : void
      {
         dispatchEvent(new UIEvent(UIEvent.PANEL_EVENT,param1.target.name));
      }
      
      public function get uiObject() : Object
      {
         return this.ui;
      }
      
      override public function destroy() : void
      {
         this.removeBtnListener(this._btnList);
         super.destroy();
         this._btnList = null;
         this.ui = null;
      }
   }
}

