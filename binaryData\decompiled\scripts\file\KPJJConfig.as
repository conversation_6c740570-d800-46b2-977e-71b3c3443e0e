package file
{
   import mogames.gameData.base.func.KPJJVO;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.base.vo.NeedVO;
   
   public class KPJJConfig
   {
      
      private static var _instance:KPJJConfig;
      
      private var _list:Vector.<KPJJVO>;
      
      public function KPJJConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : KPJJConfig
      {
         if(!_instance)
         {
            _instance = new KPJJConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = new Vector.<KPJJVO>();
         this._list[this._list.length] = new KPJJVO(new NeedVO(18501,3),new BaseRewardVO(18511,1),20000,30);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18502,3),new BaseRewardVO(18512,1),20000,30);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18511,3),new BaseRewardVO(18521,1),50000,50);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18512,3),new BaseRewardVO(18522,1),50000,50);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18521,3),new BaseRewardVO(18531,1),80000,80);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18522,3),new BaseRewardVO(18532,1),80000,80);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18531,3),new BaseRewardVO(18533,1),120000,120);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18533,3),new BaseRewardVO(18534,1),250000,300);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18532,3),new BaseRewardVO(18542,1),120000,110);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18541,3),new BaseRewardVO(18551,1),666666,300);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18542,3),new BaseRewardVO(18552,1),666666,300);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18534,3),new BaseRewardVO(18541,1),500000,300);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18553,3),new BaseRewardVO(18554,1),888888,2000);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18554,3),new BaseRewardVO(18555,1),999999,3000);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18601,3),new BaseRewardVO(18602,1),100000,100);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18611,3),new BaseRewardVO(18612,1),100000,100);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18621,3),new BaseRewardVO(18622,1),100000,100);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18631,3),new BaseRewardVO(18632,1),100000,100);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18602,5),new BaseRewardVO(18603,1),500000,200);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18612,5),new BaseRewardVO(18613,1),500000,200);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18622,5),new BaseRewardVO(18623,1),500000,200);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18632,5),new BaseRewardVO(18633,1),500000,200);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18603,5),new BaseRewardVO(18604,1),666666,300);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18613,5),new BaseRewardVO(18614,1),666666,300);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18623,5),new BaseRewardVO(18624,1),666666,300);
         this._list[this._list.length] = new KPJJVO(new NeedVO(18633,5),new BaseRewardVO(18634,1),666666,300);
      }
      
      public function findJJ(param1:int) : KPJJVO
      {
         var _loc2_:KPJJVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.source.needID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

