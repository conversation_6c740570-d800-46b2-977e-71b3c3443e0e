package file
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class WCZZConfig
   {
      
      private static var _instance:WCZZConfig;
      
      public var ccData:BossArgVO;
      
      public var fiveDatas:Array;
      
      public var waveData:WaveDataVO;
      
      public function WCZZConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : WCZZConfig
      {
         if(!_instance)
         {
            _instance = new WCZZConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         var _loc1_:OneWaveVO = null;
         this.ccData = new BossArgVO(312,160000,388,0,20,20,150,150,new BossSkillData1(50,{
            "hurt":1500,
            "killPer":20
         },3),0,0);
         this.fiveDatas = [];
         this.fiveDatas[this.fiveDatas.length] = new BossArgVO(210,25000,400,110,20,20,150,150,new BossSkillData1(100,{"hurt":1500},2),1006,0);
         this.fiveDatas[this.fiveDatas.length] = new BossArgVO(201,25000,400,110,20,20,150,150,new BossSkillData1(100,{"hurt":850},2),1018,0);
         this.fiveDatas[this.fiveDatas.length] = new BossArgVO(204,25000,400,110,20,20,150,150,new BossSkillData1(100,{
            "hurt":1500,
            "hurtCount":3
         },2),1019,0);
         this.fiveDatas[this.fiveDatas.length] = new BossArgVO(205,25000,400,110,20,20,150,150,new BossSkillData1(100,{"hurt":900},2),1011,0);
         this.fiveDatas[this.fiveDatas.length] = new BossArgVO(211,25000,400,110,20,20,150,150,new BossSkillData1(100,{"hurt":2000},2),1008,0);
         this.waveData = new WaveDataVO(0);
         this.waveData.limitBR = new WaveLimitVO(2500,1.2,1.3);
         this.waveData.zhuBoss = new BossArgVO(433,250000,900,120,20,20,150,150,new BossSkillData1(40,{
            "hurt":1550,
            "keepTime":10
         },10),1049,0);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(101,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(102,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(103,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(104,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(105,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(106,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(107,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(108,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(109,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(110,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(111,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(112,1500,120,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
      }
   }
}

