package com.mogames.utils
{
   import fl.motion.ColorMatrix;
   import flash.filters.BitmapFilter;
   import flash.filters.BlurFilter;
   import flash.filters.ColorMatrixFilter;
   import flash.filters.ConvolutionFilter;
   import flash.filters.GlowFilter;
   
   public class FilterUtil
   {
      
      public function FilterUtil()
      {
         super();
      }
      
      public static function getGrayFilter(param1:Number = 0.3) : BitmapFilter
      {
         var _loc2_:Array = [];
         _loc2_ = _loc2_.concat([param1,param1,param1,0,0]);
         _loc2_ = _loc2_.concat([param1,param1,param1,0,0]);
         _loc2_ = _loc2_.concat([param1,param1,param1,0,0]);
         _loc2_ = _loc2_.concat([0,0,0,1,0]);
         return new ColorMatrixFilter(_loc2_);
      }
      
      public static function getNegativeFilter() : BitmapFilter
      {
         var _loc1_:Array = [];
         _loc1_ = _loc1_.concat([-1,0,0,0,255]);
         _loc1_ = _loc1_.concat([0,-1,0,0,255]);
         _loc1_ = _loc1_.concat([0,0,-1,0,255]);
         _loc1_ = _loc1_.concat([0,0,0,1,0]);
         return new ColorMatrixFilter(_loc1_);
      }
      
      public static function getGlowFilter(param1:uint, param2:Number = 5, param3:Number = 5, param4:Number = 1) : BitmapFilter
      {
         return new GlowFilter(param1,1,param2,param3,param4);
      }
      
      public static function getBlurFilter() : BitmapFilter
      {
         return new BlurFilter();
      }
      
      public static function getBWFilter(param1:int = 64) : BitmapFilter
      {
         var _loc2_:Array = [];
         _loc2_ = _loc2_.concat([0.3086 * 256,0.6094 * 256,0.082 * 256,0,-256 * param1]);
         _loc2_ = _loc2_.concat([0.3086 * 256,0.6094 * 256,0.082 * 256,0,-256 * param1]);
         _loc2_ = _loc2_.concat([0.3086 * 256,0.6094 * 256,0.082 * 256,0,-256 * param1]);
         _loc2_ = _loc2_.concat([0,0,0,1,0]);
         return new ColorMatrixFilter(_loc2_);
      }
      
      public static function getHighLightFilter() : BitmapFilter
      {
         var _loc1_:Array = [];
         _loc1_ = _loc1_.concat([1,0,0,0,50]);
         _loc1_ = _loc1_.concat([0,1,0,0,50]);
         _loc1_ = _loc1_.concat([0,0,1,0,50]);
         _loc1_ = _loc1_.concat([0,0,0,1,0]);
         return new ColorMatrixFilter(_loc1_);
      }
      
      public static function getSharpenFilter() : BitmapFilter
      {
         return new ConvolutionFilter(3,3,[0,-1,0,-1,5,-1,0,-1,0]);
      }
      
      public static function getLightFilter(param1:int = 200) : BitmapFilter
      {
         var _loc2_:ColorMatrix = new ColorMatrix();
         var _loc3_:ColorMatrixFilter = new ColorMatrixFilter();
         _loc2_.SetContrastMatrix(param1);
         _loc3_.matrix = _loc2_.GetFlatArray();
         return _loc3_;
      }
      
      public static function getBrightFilter(param1:int = 50) : BitmapFilter
      {
         var _loc2_:ColorMatrix = new ColorMatrix();
         var _loc3_:ColorMatrixFilter = new ColorMatrixFilter();
         _loc2_.SetBrightnessMatrix(param1);
         _loc3_.matrix = _loc2_.GetFlatArray();
         return _loc3_;
      }
   }
}

