package file
{
   import com.mogames.utils.TxtUtil;
   import mogames.gameData.base.RewardProxy;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.base.vo.NeedVO;
   import mogames.gameData.fuben.union.FubenUnionVO;
   import mogames.gameUnion.data.UnionProxy;
   import mogames.gameUnion.data.base.UnionHeadVO;
   import mogames.gameUnion.data.base.UnionMoneyTaskVO;
   import mogames.gameUnion.data.base.UnionTaskVO;
   import mogames.gameUnion.data.skill.JianRenVO;
   import mogames.gameUnion.data.skill.XingYunVO;
   import mogames.gameUnion.data.skill.XiongShiVO;
   import mogames.gameUnion.data.skill.YongWuVO;
   
   public class UnionConfig
   {
      
      private static var _instance:UnionConfig;
      
      public static const UNION_CREATE_NEED:int = 300;
      
      public static const UNION_LEVEL_EXP:Array = [0,11300,23504,54240,85880,124300,169500,246600,"---"];
      
      public static const UNION_MAX_MEMBER:Array = [0,8,11,14,17,20,23,26,29,29];
      
      private var _heads:Array;
      
      private var _tasks:Array;
      
      private var _fubens:Array;
      
      public function UnionConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : UnionConfig
      {
         if(!_instance)
         {
            _instance = new UnionConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._heads = [];
         this._heads[this._heads.length] = new UnionHeadVO("学徒",678,0,[new BaseRewardVO(10000,10000),new BaseRewardVO(10271,1),new BaseRewardVO(10272,1),new BaseRewardVO(68002,1)]);
         this._heads[this._heads.length] = new UnionHeadVO("精英",2034,15,[new BaseRewardVO(10000,15000),new BaseRewardVO(10273,1),new BaseRewardVO(10274,1),new BaseRewardVO(68001,1)]);
         this._heads[this._heads.length] = new UnionHeadVO("堂主",3390,54,[new BaseRewardVO(10000,20000),new BaseRewardVO(10275,1),new BaseRewardVO(10276,1),new BaseRewardVO(68001,1),new BaseRewardVO(68002,1)]);
         this._heads[this._heads.length] = new UnionHeadVO("香主",4746,90,[new BaseRewardVO(10000,25000),new BaseRewardVO(10277,1),new BaseRewardVO(10278,1),new BaseRewardVO(68001,2),new BaseRewardVO(68002,2)]);
         this._heads[this._heads.length] = new UnionHeadVO("坛主",6102,126,[new BaseRewardVO(10000,30000),new BaseRewardVO(10279,1),new BaseRewardVO(11012,1),new BaseRewardVO(68001,3),new BaseRewardVO(68002,3)]);
         this._heads[this._heads.length] = new UnionHeadVO("护法",10000,188,[new BaseRewardVO(10000,35000),new BaseRewardVO(10280,1),new BaseRewardVO(11601,1),new BaseRewardVO(11012,2),new BaseRewardVO(68001,4),new BaseRewardVO(68002,4)]);
         this._tasks = [];
         this._tasks[this._tasks.length] = new UnionMoneyTaskVO(0,20,10,new NeedVO(10010,10));
         this._tasks[this._tasks.length] = new UnionTaskVO(81,12,3,new NeedVO(10007,50));
         this._tasks[this._tasks.length] = new UnionTaskVO(82,10,3,new NeedVO(10006,50));
         this._tasks[this._tasks.length] = new UnionTaskVO(83,8,3,new NeedVO(10000,50000));
         this._tasks[this._tasks.length] = new UnionTaskVO(84,6,3,new NeedVO(10005,50));
         this._tasks[this._tasks.length] = new UnionTaskVO(85,4,3,new NeedVO(10004,50));
         this._tasks[this._tasks.length] = new UnionTaskVO(86,2,3,new NeedVO(10003,50));
         this._fubens = [];
         this._fubens[this._fubens.length] = new FubenUnionVO(301,[new BaseRewardVO(10000,200000),new BaseRewardVO(50012,30),new BaseRewardVO(10501,10),new BaseRewardVO(18501,6)]);
         this._fubens[this._fubens.length] = new FubenUnionVO(302,[new BaseRewardVO(10000,300000),new BaseRewardVO(10306,10),new BaseRewardVO(10503,10),new BaseRewardVO(18502,6)]);
         this._fubens[this._fubens.length] = new FubenUnionVO(303,[new BaseRewardVO(10000,400000),new BaseRewardVO(10505,10),new BaseRewardVO(18511,2),new BaseRewardVO(18512,6)]);
         this._fubens[this._fubens.length] = new FubenUnionVO(304,[new BaseRewardVO(10000,500000),new BaseRewardVO(50021,30),new BaseRewardVO(10510,10),new BaseRewardVO(18521,6)]);
         this._fubens[this._fubens.length] = new FubenUnionVO(305,[new BaseRewardVO(10000,600000),new BaseRewardVO(50024,30),new BaseRewardVO(10539,10),new BaseRewardVO(18522,6)]);
         this._fubens[this._fubens.length] = new FubenUnionVO(306,[new BaseRewardVO(10000,700000),new BaseRewardVO(50033,30),new BaseRewardVO(10545,10),new BaseRewardVO(18531,6)]);
         this._fubens[this._fubens.length] = new FubenUnionVO(307,[new BaseRewardVO(10000,800000),new BaseRewardVO(50038,30),new BaseRewardVO(10553,10),new BaseRewardVO(18532,6)]);
      }
      
      private function defaultTask() : void
      {
         var _loc1_:UnionTaskVO = null;
         for each(_loc1_ in this._tasks)
         {
            _loc1_.curNum = _loc1_.taskNum;
         }
      }
      
      public function initTask(param1:Object) : void
      {
         var _loc2_:UnionTaskVO = null;
         var _loc3_:Object = null;
         this.defaultTask();
         for each(_loc3_ in param1.tasklist)
         {
            _loc2_ = this.findTasks(_loc3_.taskName);
            if(_loc2_)
            {
               _loc2_.curNum = Math.max(0,_loc2_.taskNum - int(_loc3_.value) / _loc2_.numGX);
            }
         }
         _loc2_ = this._tasks[0];
         if(param1.exchange)
         {
            _loc2_.curNum = Math.max(0,_loc2_.taskNum - int(param1.exchange) / _loc2_.numGX);
         }
      }
      
      public function findTasks(param1:int) : UnionTaskVO
      {
         var _loc2_:UnionTaskVO = null;
         for each(_loc2_ in this._tasks)
         {
            if(_loc2_.taskID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findHeads(param1:int) : UnionHeadVO
      {
         if(param1 == 0)
         {
            return null;
         }
         return this._heads[param1 - 1];
      }
      
      public function findFuben(param1:int) : FubenUnionVO
      {
         var _loc2_:FubenUnionVO = null;
         for each(_loc2_ in this._fubens)
         {
            if(_loc2_.fubenID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function newSkills() : Array
      {
         var _loc1_:Array = [];
         _loc1_[_loc1_.length] = new YongWuVO();
         _loc1_[_loc1_.length] = new JianRenVO();
         _loc1_[_loc1_.length] = new XiongShiVO();
         _loc1_[_loc1_.length] = new XingYunVO();
         return _loc1_;
      }
      
      public function get tasks() : Array
      {
         return this._tasks;
      }
      
      public function get headName() : String
      {
         var _loc1_:UnionHeadVO = this.findHeads(UnionProxy.instance().myHead);
         if(!_loc1_)
         {
            return "无";
         }
         return _loc1_.name;
      }
      
      public function get headRewardInfor() : String
      {
         var _loc2_:UnionHeadVO = null;
         var _loc1_:Array = ["拥有头衔后可领取每日福利<br>"];
         for each(_loc2_ in this._heads)
         {
            _loc1_[_loc1_.length] = _loc2_.name + "：<br>" + TxtUtil.setColor(RewardProxy.instance().parseName1(_loc2_.rewards,false),"ffff00");
         }
         return _loc1_.join("<br><br>");
      }
   }
}

