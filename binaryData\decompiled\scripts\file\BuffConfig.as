package file
{
   import mogames.gameBuff.base.RoleBuff;
   import mogames.gameBuff.buff.ATKBuff;
   import mogames.gameBuff.buff.ATKHPBuff;
   import mogames.gameBuff.buff.ATKKeepBuff;
   import mogames.gameBuff.buff.AllCritBuff;
   import mogames.gameBuff.buff.BeiBuff;
   import mogames.gameBuff.buff.CritBuff;
   import mogames.gameBuff.buff.DEFBuff;
   import mogames.gameBuff.buff.HLZTBuff;
   import mogames.gameBuff.buff.LoseHurtBuff;
   import mogames.gameBuff.buff.MISSBuff;
   import mogames.gameBuff.buff.MJZSBuff;
   import mogames.gameBuff.buff.MoveBuff;
   import mogames.gameBuff.buff.PDEFBuff;
   import mogames.gameBuff.buff.QXDBuff;
   import mogames.gameBuff.buff.ReboundBuff;
   import mogames.gameBuff.buff.WDBuff;
   import mogames.gameBuff.buff.WuWeiBuff;
   import mogames.gameBuff.buff.XXMFBuff;
   import mogames.gameBuff.buff.YinShenBuff;
   import mogames.gameBuff.debuff.ATKDebuff;
   import mogames.gameBuff.debuff.CritDebuff;
   import mogames.gameBuff.debuff.DEFDebuff;
   import mogames.gameBuff.debuff.HurtDebuff;
   import mogames.gameBuff.debuff.MissDebuff;
   import mogames.gameBuff.debuff.MoveDebuff;
   import mogames.gameBuff.debuff.NSJDebuff;
   import mogames.gameBuff.debuff.NoskillBuff;
   import mogames.gameBuff.debuff.RigorDebuff;
   import mogames.gameBuff.debuff.WITDebuff;
   import mogames.gameData.buff.ConstBuffVO;
   
   public class BuffConfig
   {
      
      private static var _instance:BuffConfig;
      
      private var _list:Vector.<ConstBuffVO>;
      
      public function BuffConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : BuffConfig
      {
         if(!_instance)
         {
            _instance = new BuffConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = new Vector.<ConstBuffVO>();
         this._list[this._list.length] = new ConstBuffVO(1001,true,{
            "name":"SEQ_ATK_BUFF_CLIP",
            "align":"TOP"
         });
         this._list[this._list.length] = new ConstBuffVO(1003,true,{
            "name":"BOOK_QI_XING_DENG_BUFF",
            "align":"TOP"
         });
         this._list[this._list.length] = new ConstBuffVO(1004,true);
         this._list[this._list.length] = new ConstBuffVO(1005,true,{
            "name":"BOOK_FAN_TAN_BUFF",
            "align":"CENTER"
         });
         this._list[this._list.length] = new ConstBuffVO(1006,true,{
            "name":"BUFF_DEF_CLIP",
            "align":"CENTER"
         });
         this._list[this._list.length] = new ConstBuffVO(1007,true,{
            "name":"SEQ_BUFF_SPEED_UP_CLIP",
            "align":"BTM"
         });
         this._list[this._list.length] = new ConstBuffVO(1008,true,{
            "name":"SEQ_YIN_SHEN_BUFF_CLIP",
            "align":"BTM"
         });
         this._list[this._list.length] = new ConstBuffVO(1009,true,{
            "name":"SEQ_WUDI_BUFF_CLIP",
            "align":"CENTER"
         });
         this._list[this._list.length] = new ConstBuffVO(1010,true,{
            "name":"SEQ_CRIT_BUFF_CLIP",
            "align":"CENTER"
         });
         this._list[this._list.length] = new ConstBuffVO(1011,true,{
            "name":"BUFF_LOSE_HURT_CLIP",
            "align":"CENTER"
         });
         this._list[this._list.length] = new ConstBuffVO(1012,true,{
            "name":"SKILL_XI_XING_MI_FA_BUFF",
            "align":"BTM"
         });
         this._list[this._list.length] = new ConstBuffVO(1013,true,{
            "name":"BUFF_MISS_CLIP",
            "align":"CENTER"
         });
         this._list[this._list.length] = new ConstBuffVO(1014,true,{
            "name":"SEQ_HLZT_BUFF_CLIP",
            "align":"CENTER"
         });
         this._list[this._list.length] = new ConstBuffVO(1015,true,{
            "name":"SEQ_CRIT_BUFF_CLIP",
            "align":"CENTER"
         });
         this._list[this._list.length] = new ConstBuffVO(1016,true,{
            "name":"SEQ_ATK_BUFF_CLIP",
            "align":"TOP"
         });
         this._list[this._list.length] = new ConstBuffVO(1017,true,{
            "name":"SEQ_BEI_BUFF_CLIP",
            "align":"CENTER"
         });
         this._list[this._list.length] = new ConstBuffVO(1018,true,{
            "name":"SKILL_XI_XING_MI_FA_BUFF",
            "align":"BTM"
         });
         this._list[this._list.length] = new ConstBuffVO(1019,true,{
            "name":"SKILL_WU_WEI_BUFF",
            "align":"BTM"
         });
         this._list[this._list.length] = new ConstBuffVO(1020,true,{
            "name":"SKILL_MJZS_BUFF",
            "align":"CENTER"
         });
         this._list[this._list.length] = new ConstBuffVO(2001,true,{
            "name":"SEQ_DEF_DEBUFF_CLIP",
            "align":"TOP"
         });
         this._list[this._list.length] = new ConstBuffVO(2002,true,{
            "name":"DEBUFF_NO_SKILL_CLIP",
            "align":"TOP"
         });
         this._list[this._list.length] = new ConstBuffVO(2003,true);
         this._list[this._list.length] = new ConstBuffVO(2004,true,{
            "name":"SEQ_POSION_DEBUFF_CLIP",
            "align":"CENTER"
         });
         this._list[this._list.length] = new ConstBuffVO(2005,true,{
            "name":"SEQ_MOVE_DEBUFF_CLIP",
            "align":"CENTER"
         });
         this._list[this._list.length] = new ConstBuffVO(2006,true,{
            "name":"SEQ_ATK_DEBUFF_CLIP",
            "align":"TOP"
         });
         this._list[this._list.length] = new ConstBuffVO(2007,true,{
            "name":"DEBUFF_MISS_CLIP",
            "align":"CENTER"
         });
         this._list[this._list.length] = new ConstBuffVO(2008,true,{
            "name":"SEQ_HLB_DEBUFF_CLIP",
            "align":"BTM"
         });
         this._list[this._list.length] = new ConstBuffVO(2009,true,{
            "name":"SEQ_CRIT_DEBUFF_CLIP",
            "align":"TOP"
         });
         this._list[this._list.length] = new ConstBuffVO(2100,false);
      }
      
      public function findConstBuff(param1:int) : ConstBuffVO
      {
         var _loc2_:ConstBuffVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.buffID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function newRoleBuff(param1:int) : RoleBuff
      {
         var _loc2_:ConstBuffVO = this.findConstBuff(param1);
         switch(param1)
         {
            case 1001:
               return new ATKBuff(_loc2_);
            case 1003:
               return new QXDBuff(_loc2_);
            case 1004:
               return new PDEFBuff(_loc2_);
            case 1005:
               return new ReboundBuff(_loc2_);
            case 1006:
               return new DEFBuff(_loc2_);
            case 1007:
               return new MoveBuff(_loc2_);
            case 1008:
               return new YinShenBuff(_loc2_);
            case 1009:
               return new WDBuff(_loc2_);
            case 1010:
               return new CritBuff(_loc2_);
            case 1011:
               return new LoseHurtBuff(_loc2_);
            case 1012:
               return new XXMFBuff(_loc2_);
            case 1013:
               return new MISSBuff(_loc2_);
            case 1014:
               return new HLZTBuff(_loc2_);
            case 1015:
               return new AllCritBuff(_loc2_);
            case 1016:
               return new ATKKeepBuff(_loc2_);
            case 1017:
               return new BeiBuff(_loc2_);
            case 1018:
               return new ATKHPBuff(_loc2_);
            case 1019:
               return new WuWeiBuff(_loc2_);
            case 1020:
               return new MJZSBuff(_loc2_);
            case 2001:
               return new DEFDebuff(_loc2_);
            case 2002:
               return new NoskillBuff(_loc2_);
            case 2003:
               return new RigorDebuff(_loc2_);
            case 2004:
               return new HurtDebuff(_loc2_);
            case 2005:
               return new MoveDebuff(_loc2_);
            case 2006:
               return new ATKDebuff(_loc2_);
            case 2007:
               return new MissDebuff(_loc2_);
            case 2008:
               return new WITDebuff(_loc2_);
            case 2009:
               return new CritDebuff(_loc2_);
            case 2100:
               return new NSJDebuff(_loc2_);
            default:
               return null;
         }
      }
   }
}

