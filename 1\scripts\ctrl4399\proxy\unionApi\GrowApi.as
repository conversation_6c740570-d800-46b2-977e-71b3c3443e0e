package ctrl4399.proxy.unionApi
{
   public interface GrowApi
   {
      
      function doTask(param1:<PERSON><PERSON><PERSON><PERSON><PERSON>, param2:String, param3:Function, param4:Function) : void;
      
      function getTaskValue(param1:<PERSON>piHeader, param2:Function, param3:Function) : void;
      
      function exchange(param1:<PERSON><PERSON><PERSON>eader, param2:int, param3:Function, param4:Function) : void;
      
      function test(param1:Function, param2:Function) : void;
   }
}

