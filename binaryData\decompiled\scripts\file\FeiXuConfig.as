package file
{
   import com.mogames.utils.MathUtil;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.ConstData;
   import mogames.gameData.base.func.MLRandWaveVO;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.fuben.vo.FubenVO;
   import mogames.gameData.good.GoodFactory;
   import mogames.gameData.good.bag.base.GameBagVO;
   import mogames.gameData.good.base.ConstBagVO;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class FeiXuConfig
   {
      
      private static var _instance:FeiXuConfig;
      
      private var _waves:Array;
      
      private var _armyID:int;
      
      private var _bossID:int;
      
      private var _paths:Array;
      
      private var _bags:Array;
      
      public var interval:Number;
      
      public function FeiXuConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : FeiXuConfig
      {
         if(!_instance)
         {
            _instance = new FeiXuConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._armyID = 222;
         this._bossID = 213;
         this._waves = [];
         this._waves[this._waves.length] = new MLRandWaveVO(15,[{
            "index":4,
            "num":1
         },{
            "index":7,
            "num":1
         },{
            "index":14,
            "num":1
         }],[20,15,20,15,10,20,15,20,10,20,15,20,15,20,15],[20,20,20,20,20,25,20,20,25,20,25,20,20,25,30],[0,1,2,3,0,1,2,2,3,1,1,2,2,3,1]);
         this._waves[this._waves.length] = new MLRandWaveVO(14,[{
            "index":3,
            "num":1
         },{
            "index":8,
            "num":1
         },{
            "index":11,
            "num":1
         }],[20,20,15,10,15,20,15,15,10,15,10,20,15,20],[20,20,20,25,20,20,20,25,20,25,20,20,20,30],[1,2,3,0,1,2,3,0,1,2,3,0,1,2]);
         this._waves[this._waves.length] = new MLRandWaveVO(13,[{
            "index":5,
            "num":1
         },{
            "index":6,
            "num":1
         },{
            "index":10,
            "num":1
         }],[20,20,20,15,20,15,20,15,20,15,10,15,15],[20,20,25,25,20,20,20,25,20,25,20,20,30],[2,3,0,1,2,3,0,1,2,0,0,1,2]);
         this._waves[this._waves.length] = new MLRandWaveVO(12,[{
            "index":2,
            "num":1
         },{
            "index":5,
            "num":1
         },{
            "index":9,
            "num":1
         }],[20,20,15,20,15,20,20,15,20,20,15,20],[20,20,20,25,20,25,20,25,20,25,20,30],[2,3,0,1,2,3,0,1,2,0,1,2]);
         this._waves[this._waves.length] = new MLRandWaveVO(11,[{
            "index":6,
            "num":1
         },{
            "index":10,
            "num":1
         }],[20,15,20,10,20,15,20,15,10,20,15],[20,25,20,20,25,20,25,20,25,20,30],[3,0,1,2,3,0,1,2,3,0,1]);
         this._waves[this._waves.length] = new MLRandWaveVO(10,[{
            "index":3,
            "num":1
         },{
            "index":9,
            "num":1
         }],[20,20,15,20,10,20,15,20,10,15],[20,25,20,20,25,20,20,25,20,30],[3,0,1,2,3,0,1,2,3,0]);
         this._waves[this._waves.length] = new MLRandWaveVO(9,[{
            "index":3,
            "num":1
         },{
            "index":6,
            "num":1
         }],[20,10,20,15,20,10,20,15,15],[25,20,30,20,25,20,20,25,20],[2,2,3,0,1,3,3,0,2,3]);
         this._bags = [];
         this._bags[this._bags.length] = {
            "rate":100,
            "bagID":11002
         };
         this._bags[this._bags.length] = {
            "rate":130,
            "bagID":11154
         };
         this._bags[this._bags.length] = {
            "rate":80,
            "bagID":11157
         };
         this._bags[this._bags.length] = {
            "rate":50,
            "bagID":11008
         };
         this._bags[this._bags.length] = {
            "rate":120,
            "bagID":11201
         };
         this._bags[this._bags.length] = {
            "rate":30,
            "bagID":11071
         };
         this._bags[this._bags.length] = {
            "rate":10,
            "bagID":11072
         };
         this._bags[this._bags.length] = {
            "rate":80,
            "bagID":11204
         };
         this._bags[this._bags.length] = {
            "rate":50,
            "bagID":11011
         };
         this._bags[this._bags.length] = {
            "rate":270,
            "bagID":11301
         };
         this._bags[this._bags.length] = {
            "rate":80,
            "bagID":11302
         };
         this.interval = 4;
      }
      
      private function newSkills() : Array
      {
         var _loc1_:Array = [];
         _loc1_[_loc1_.length] = {
            "sid":1001,
            "arg":{
               "hurt":this.countHurt(72),
               "keepTime":3,
               "hurtCount":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1002,
            "arg":{
               "hurt":this.countHurt(80),
               "hurtCount":5
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1003,
            "arg":{
               "hurt":this.countHurt(80),
               "hurtCount":5
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1004,
            "arg":{
               "hurt":this.countHurt(80),
               "roleNum":4
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1005,
            "arg":{
               "hurt":this.countHurt(80),
               "roleNum":10
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1006,
            "arg":{"hurt":this.countHurt(90)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1007,
            "arg":{
               "hurt":this.countHurt(80),
               "keepTime":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1008,
            "arg":{"hurt":this.countHurt(90)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1009,
            "arg":{"hurt":this.countHurt(90)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1010,
            "arg":{"hurt":this.countHurt(90)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1011,
            "arg":{"hurt":this.countHurt(75)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1012,
            "arg":{"hurt":this.countHurt(90)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1013,
            "arg":{"hurt":this.countHurt(80)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1014,
            "arg":{
               "hurt":this.countHurt(80),
               "keepTime":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1015,
            "arg":{
               "hurt":this.countHurt(73),
               "keepTime":8,
               "hurtCount":5
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1016,
            "arg":{
               "hurt":this.countHurt(80),
               "roleNum":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1017,
            "arg":{
               "hurt":this.countHurt(90),
               "hurtCount":5
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1018,
            "arg":{"hurt":this.countHurt(90)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1019,
            "arg":{
               "hurt":this.countHurt(90),
               "hurtCount":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1020,
            "arg":{
               "hurt":this.countHurt(90),
               "keepTime":3
            }
         };
         return _loc1_;
      }
      
      private function countHurt(param1:int) : int
      {
         return param1;
      }
      
      public function newMiLinWave() : Object
      {
         var _loc6_:OneWaveVO = null;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc1_:Array = this.newSkills();
         var _loc2_:MLRandWaveVO = this._waves[int(Math.random() * this._waves.length)];
         var _loc3_:WaveDataVO = new WaveDataVO();
         _loc3_.limitBR = new WaveLimitVO(0,1,1);
         _loc3_.zhuBoss = this.newMLBoss(_loc1_[int(Math.random() * _loc1_.length)]);
         var _loc4_:int = 0;
         var _loc5_:int = _loc2_.total;
         while(_loc4_ < _loc5_)
         {
            _loc6_ = new OneWaveVO(_loc2_.findTime(_loc4_));
            _loc6_.addEnemy(new WaveEnemyVO(_loc2_.findEmemies(_loc4_),this.newEnemyArg));
            _loc7_ = _loc2_.findFu(_loc4_);
            _loc8_ = 0;
            while(_loc8_ < _loc7_)
            {
               _loc6_.addFu(this.newMLBoss(_loc1_[int(Math.random() * _loc1_.length)]));
               _loc8_++;
            }
            _loc3_.addWave(_loc6_);
            _loc4_++;
         }
         _loc1_.length = 0;
         _loc1_ = null;
         return {
            "wave":_loc3_,
            "location":_loc2_.location
         };
      }
      
      private function get newEnemyArg() : RoleArgVO
      {
         return new RoleArgVO(this._armyID,240,35,1,20,50,170,40,null);
      }
      
      private function newMLBoss(param1:Object) : BossArgVO
      {
         return new BossArgVO(this._bossID,8000,45,1,40,80,220,60,new BossSkillData0(600,param1.arg,2),param1.sid,0);
      }
      
      public function enemyPaths(param1:int) : Array
      {
         if(!this._paths)
         {
            this._paths = [];
            this._paths[0] = [new Point(-43,327),new Rectangle(2,278,85,100)];
            this._paths[1] = [new Point(1000,327),new Rectangle(875,282,85,100)];
            this._paths[2] = [new Point(708,634),new Rectangle(655,515,100,85)];
            this._paths[3] = [new Point(240,634),new Rectangle(190,515,100,85)];
         }
         var _loc2_:Array = this._paths[param1];
         var _loc3_:Point = new Point();
         _loc3_.x = _loc2_[1].x + Math.random() * _loc2_[1].width;
         _loc3_.y = _loc2_[1].y + Math.random() * _loc2_[1].height;
         return [_loc2_[0],_loc3_];
      }
      
      public function countWinReward(param1:Number) : Array
      {
         var _loc6_:int = 0;
         var _loc2_:Array = [];
         _loc2_[0] = new BaseRewardVO(10000,50000 * param1);
         var _loc3_:int = ConstData.INT1.v;
         if(MathUtil.checkOdds(700))
         {
            _loc3_ = ConstData.INT2.v;
         }
         var _loc4_:FubenVO = FubenConfig.instance().findFuben(103);
         var _loc5_:Array = _loc4_.drops.slice(1);
         var _loc7_:int = 0;
         while(_loc7_ < _loc3_)
         {
            _loc6_ = Math.random() * _loc5_.length;
            _loc2_[_loc2_.length] = _loc5_[_loc6_];
            _loc5_.splice(_loc6_,1);
            _loc7_++;
         }
         return _loc2_;
      }
      
      public function countLoseReward() : Array
      {
         var _loc1_:int = MasterProxy.instance().masterVO.level * MathUtil.randomNum(50,250);
         return [new BaseRewardVO(10000,_loc1_)];
      }
      
      public function get randBagVO() : GameBagVO
      {
         var _loc2_:Object = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._bags)
         {
            if(MathUtil.checkOdds(_loc2_.rate))
            {
               _loc1_[_loc1_.length] = _loc2_;
            }
         }
         if(_loc1_.length <= 0)
         {
            _loc1_ = this._bags.slice();
         }
         var _loc3_:Object = _loc1_[int(Math.random() * _loc1_.length)];
         return GoodFactory.newBagVO(GoodConfig.instance().findConstGood(_loc3_.bagID) as ConstBagVO);
      }
   }
}

