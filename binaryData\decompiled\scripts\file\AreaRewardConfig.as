package file
{
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.base.vo.EquipRewardVO;
   import mogames.gameData.mission.reward.AreaRewardVO;
   import mogames.gameData.mission.reward.StarRewardVO;
   
   public class AreaRewardConfig
   {
      
      private static var _instance:AreaRewardConfig;
      
      private var _list:Vector.<AreaRewardVO>;
      
      public function AreaRewardConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : AreaRewardConfig
      {
         if(!_instance)
         {
            _instance = new AreaRewardConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = new Vector.<AreaRewardVO>();
         this._list[this._list.length] = new AreaRewardVO(201,[new StarRewardVO(201,111,13,[new BaseRewardVO(10000,10000),new BaseRewardVO(10250,3),new BaseRewardVO(10801,1),new BaseRewardVO(11071,10)]),new StarRewardVO(201,112,26,[new BaseRewardVO(10000,20000),new BaseRewardVO(10801,2),new BaseRewardVO(10250,5),new BaseRewardVO(50001,5)]),new StarRewardVO(201,113,39,[new BaseRewardVO(10000,30000),new BaseRewardVO(11073,3),new BaseRewardVO(50001,20)])]);
         this._list[this._list.length] = new AreaRewardVO(202,[new StarRewardVO(202,114,13,[new BaseRewardVO(10000,10000),new BaseRewardVO(10301,3),new BaseRewardVO(10806,1)]),new StarRewardVO(202,115,32,[new BaseRewardVO(10000,20000),new BaseRewardVO(10250,5),new BaseRewardVO(11104,5),new BaseRewardVO(11005,5)]),new StarRewardVO(202,116,54,[new BaseRewardVO(10000,30000),new BaseRewardVO(11073,3),new BaseRewardVO(10806,2),new BaseRewardVO(10007,50)])]);
         this._list[this._list.length] = new AreaRewardVO(203,[new StarRewardVO(203,117,13,[new BaseRewardVO(10000,20000),new BaseRewardVO(10301,3),new BaseRewardVO(10811,1)]),new StarRewardVO(203,118,35,[new BaseRewardVO(10000,30000),new BaseRewardVO(11204,3),new BaseRewardVO(10811,1)]),new StarRewardVO(203,119,60,[new BaseRewardVO(10000,50000),new BaseRewardVO(11157,3),new BaseRewardVO(10811,1)])]);
         this._list[this._list.length] = new AreaRewardVO(204,[new StarRewardVO(204,120,13,[new BaseRewardVO(10000,20000),new BaseRewardVO(10402,5),new BaseRewardVO(11005,5)]),new StarRewardVO(204,121,35,[new BaseRewardVO(10000,30000),new BaseRewardVO(10403,5),new BaseRewardVO(10811,1)]),new StarRewardVO(204,122,60,[new BaseRewardVO(10000,50000),new BaseRewardVO(11003,8),new BaseRewardVO(10811,2)])]);
         this._list[this._list.length] = new AreaRewardVO(205,[new StarRewardVO(205,123,13,[new BaseRewardVO(10000,20000),new BaseRewardVO(68001,3),new BaseRewardVO(68002,3)]),new StarRewardVO(205,124,35,[new BaseRewardVO(10000,30000),new BaseRewardVO(68002,6),new BaseRewardVO(10801,1)]),new StarRewardVO(205,125,60,[new BaseRewardVO(10000,50000),new BaseRewardVO(68001,6),new BaseRewardVO(10801,2)])]);
         this._list[this._list.length] = new AreaRewardVO(206,[new StarRewardVO(206,126,13,[new BaseRewardVO(10000,20000),new BaseRewardVO(68001,3),new BaseRewardVO(68002,3)]),new StarRewardVO(206,127,35,[new BaseRewardVO(10000,30000),new BaseRewardVO(68002,5),new BaseRewardVO(10980,10)]),new StarRewardVO(206,128,60,[new BaseRewardVO(10000,50000),new BaseRewardVO(68001,5),new BaseRewardVO(50011,30)])]);
         this._list[this._list.length] = new AreaRewardVO(207,[new StarRewardVO(207,129,13,[new BaseRewardVO(10000,30000),new BaseRewardVO(10202,3),new BaseRewardVO(10811,1)]),new StarRewardVO(207,130,35,[new BaseRewardVO(10000,40000),new BaseRewardVO(10103,1),new BaseRewardVO(10811,1)]),new StarRewardVO(207,131,60,[new BaseRewardVO(10000,70000),new BaseRewardVO(10851,2),new BaseRewardVO(10811,2)])]);
         this._list[this._list.length] = new AreaRewardVO(208,[new StarRewardVO(208,132,13,[new BaseRewardVO(10000,30000),new BaseRewardVO(11204,3),new BaseRewardVO(10806,1)]),new StarRewardVO(208,133,35,[new BaseRewardVO(10000,40000),new BaseRewardVO(68001,5),new BaseRewardVO(10806,2)]),new StarRewardVO(208,134,60,[new BaseRewardVO(10000,70000),new BaseRewardVO(68002,5),new BaseRewardVO(10103,2)])]);
         this._list[this._list.length] = new AreaRewardVO(209,[new StarRewardVO(209,135,13,[new BaseRewardVO(10000,30000),new BaseRewardVO(11204,3),new BaseRewardVO(10980,3)]),new StarRewardVO(209,136,35,[new BaseRewardVO(10000,40000),new BaseRewardVO(68001,5),new BaseRewardVO(11005,10)]),new StarRewardVO(209,137,60,[new BaseRewardVO(10000,70000),new BaseRewardVO(68002,5),new BaseRewardVO(50022,30)])]);
         this._list[this._list.length] = new AreaRewardVO(210,[new StarRewardVO(210,138,13,[new BaseRewardVO(10000,30000),new BaseRewardVO(11204,3),new BaseRewardVO(11501,10)]),new StarRewardVO(210,139,35,[new BaseRewardVO(10000,40000),new BaseRewardVO(10981,1),new BaseRewardVO(11104,10)]),new StarRewardVO(210,140,60,[new BaseRewardVO(10000,70000),new BaseRewardVO(10982,1),new BaseRewardVO(11151,10)])]);
         this._list[this._list.length] = new AreaRewardVO(211,[new StarRewardVO(211,141,13,[new BaseRewardVO(10000,30000),new BaseRewardVO(11205,5),new BaseRewardVO(10851,3)]),new StarRewardVO(211,142,35,[new BaseRewardVO(10000,40000),new BaseRewardVO(10302,5),new BaseRewardVO(10852,3)]),new StarRewardVO(211,143,60,[new BaseRewardVO(10000,70000),new BaseRewardVO(11104,5),new BaseRewardVO(50026,30)])]);
         this._list[this._list.length] = new AreaRewardVO(212,[new StarRewardVO(212,144,13,[new BaseRewardVO(10000,30000),new BaseRewardVO(11158,5),new BaseRewardVO(11151,5)]),new StarRewardVO(212,145,35,[new BaseRewardVO(10000,40000),new BaseRewardVO(10302,5),new BaseRewardVO(11151,8)]),new StarRewardVO(212,146,60,[new BaseRewardVO(10000,70000),new BaseRewardVO(11005,20),new BaseRewardVO(50029,30)])]);
         this._list[this._list.length] = new AreaRewardVO(213,[new StarRewardVO(213,147,13,[new BaseRewardVO(10000,30000),new BaseRewardVO(68001,10),new BaseRewardVO(11011,15)]),new StarRewardVO(213,148,35,[new BaseRewardVO(10000,40000),new BaseRewardVO(68002,10),new BaseRewardVO(11158,8)]),new StarRewardVO(213,149,60,[new BaseRewardVO(10000,70000),new BaseRewardVO(11005,20),new BaseRewardVO(50035,30)])]);
         this._list[this._list.length] = new AreaRewardVO(214,[new StarRewardVO(214,1001,13,[new BaseRewardVO(10000,40000),new BaseRewardVO(68001,10),new BaseRewardVO(11012,10)]),new StarRewardVO(214,1002,32,[new BaseRewardVO(10000,50000),new BaseRewardVO(68002,10),new BaseRewardVO(11601,10)]),new StarRewardVO(214,1003,57,[new BaseRewardVO(10000,80000),new BaseRewardVO(11104,20),new BaseRewardVO(50037,30)])]);
         this._list[this._list.length] = new AreaRewardVO(215,[new StarRewardVO(215,1004,13,[new BaseRewardVO(10000,40000),new BaseRewardVO(68001,10),new BaseRewardVO(11158,15)]),new StarRewardVO(215,1005,32,[new BaseRewardVO(10000,50000),new BaseRewardVO(68002,10),new BaseRewardVO(11601,10)]),new StarRewardVO(215,1006,54,[new BaseRewardVO(10000,80000),new BaseRewardVO(11151,20),new EquipRewardVO(30017,3)])]);
         this._list[this._list.length] = new AreaRewardVO(216,[new StarRewardVO(216,1007,13,[new BaseRewardVO(10000,40000),new BaseRewardVO(68001,15),new BaseRewardVO(10303,5)]),new StarRewardVO(216,1008,32,[new BaseRewardVO(10000,50000),new BaseRewardVO(68002,15),new BaseRewardVO(11005,30)]),new StarRewardVO(216,1009,57,[new BaseRewardVO(10000,80000),new BaseRewardVO(11205,20),new BaseRewardVO(10562,10)])]);
         this._list[this._list.length] = new AreaRewardVO(217,[new StarRewardVO(217,10101,13,[new BaseRewardVO(10000,40000),new BaseRewardVO(68001,15),new BaseRewardVO(11005,20)]),new StarRewardVO(217,10111,32,[new BaseRewardVO(10000,50000),new BaseRewardVO(68002,15),new BaseRewardVO(11104,10)]),new StarRewardVO(217,1012,54,[new BaseRewardVO(10000,80000),new BaseRewardVO(11002,25),new BaseRewardVO(50042,30)])]);
         this._list[this._list.length] = new AreaRewardVO(218,[new StarRewardVO(218,1013,13,[new BaseRewardVO(10000,40000),new BaseRewardVO(68001,15),new BaseRewardVO(10851,6)]),new StarRewardVO(218,1014,32,[new BaseRewardVO(10000,50000),new BaseRewardVO(68002,15),new BaseRewardVO(10852,6)]),new StarRewardVO(218,1015,57,[new BaseRewardVO(10000,80000),new BaseRewardVO(11104,20),new EquipRewardVO(42521,3),new BaseRewardVO(50045,30)])]);
         this._list[this._list.length] = new AreaRewardVO(219,[new StarRewardVO(219,1016,13,[new BaseRewardVO(10000,40000),new BaseRewardVO(68001,15),new BaseRewardVO(50043,10)]),new StarRewardVO(219,1017,32,[new BaseRewardVO(10000,50000),new BaseRewardVO(68002,15),new BaseRewardVO(50043,10)]),new StarRewardVO(219,1018,54,[new BaseRewardVO(10000,80000),new BaseRewardVO(11104,20),new BaseRewardVO(50043,20)])]);
         this._list[this._list.length] = new AreaRewardVO(220,[new StarRewardVO(220,1019,13,[new BaseRewardVO(10000,40000),new BaseRewardVO(68001,15),new BaseRewardVO(11005,20)]),new StarRewardVO(220,10201,32,[new BaseRewardVO(10000,50000),new BaseRewardVO(68002,15),new BaseRewardVO(11104,10)]),new StarRewardVO(220,10211,57,[new BaseRewardVO(10000,80000),new BaseRewardVO(18840,2),new BaseRewardVO(18830,2),new BaseRewardVO(50007,10)])]);
         this._list[this._list.length] = new AreaRewardVO(221,[new StarRewardVO(221,1022,13,[new BaseRewardVO(10000,40000),new BaseRewardVO(68001,15),new BaseRewardVO(10851,6)]),new StarRewardVO(221,1023,32,[new BaseRewardVO(10000,50000),new BaseRewardVO(68002,15),new BaseRewardVO(10852,6)]),new StarRewardVO(221,1024,54,[new BaseRewardVO(10000,80000),new BaseRewardVO(11002,25),new BaseRewardVO(10320,20)])]);
         this._list[this._list.length] = new AreaRewardVO(222,[new StarRewardVO(222,1025,13,[new BaseRewardVO(10000,40000),new BaseRewardVO(68001,15),new BaseRewardVO(11005,20)]),new StarRewardVO(222,1026,32,[new BaseRewardVO(10000,50000),new BaseRewardVO(68002,15),new BaseRewardVO(11104,10)]),new StarRewardVO(222,1027,57,[new BaseRewardVO(10000,80000),new BaseRewardVO(11205,25),new BaseRewardVO(10320,20)])]);
         this._list[this._list.length] = new AreaRewardVO(223,[new StarRewardVO(223,1028,13,[new BaseRewardVO(10000,40000),new BaseRewardVO(68001,15),new BaseRewardVO(10851,6)]),new StarRewardVO(223,1029,32,[new BaseRewardVO(10000,50000),new BaseRewardVO(68002,15),new BaseRewardVO(10852,6)]),new StarRewardVO(223,10301,54,[new BaseRewardVO(10000,80000),new BaseRewardVO(11104,15),new BaseRewardVO(10320,20)])]);
         this._list[this._list.length] = new AreaRewardVO(224,[new StarRewardVO(224,10311,13,[new BaseRewardVO(10000,40000),new BaseRewardVO(68001,15),new BaseRewardVO(10851,6)]),new StarRewardVO(224,1032,32,[new BaseRewardVO(10000,50000),new BaseRewardVO(68002,15),new BaseRewardVO(10852,6)]),new StarRewardVO(224,1033,57,[new BaseRewardVO(10000,80000),new BaseRewardVO(11002,25),new BaseRewardVO(10320,20)])]);
         this._list[this._list.length] = new AreaRewardVO(225,[new StarRewardVO(225,1034,13,[new BaseRewardVO(10000,40000),new BaseRewardVO(68001,15),new BaseRewardVO(11005,20)]),new StarRewardVO(225,1035,32,[new BaseRewardVO(10000,50000),new BaseRewardVO(68002,15),new BaseRewardVO(11104,10)]),new StarRewardVO(225,1036,54,[new BaseRewardVO(10000,80000),new BaseRewardVO(11205,25),new BaseRewardVO(10320,10)])]);
         this._list[this._list.length] = new AreaRewardVO(226,[new StarRewardVO(226,1037,15,[new BaseRewardVO(10000,40000),new BaseRewardVO(68001,15),new BaseRewardVO(10851,6)]),new StarRewardVO(226,1038,30,[new BaseRewardVO(10000,50000),new BaseRewardVO(68002,15),new BaseRewardVO(10852,6)]),new StarRewardVO(226,1039,45,[new BaseRewardVO(10000,80000),new BaseRewardVO(11205,25),new BaseRewardVO(10320,10)])]);
         this._list[this._list.length] = new AreaRewardVO(227,[new StarRewardVO(227,10401,15,[new BaseRewardVO(10000,40000),new BaseRewardVO(68001,15),new BaseRewardVO(10851,6)]),new StarRewardVO(227,10411,30,[new BaseRewardVO(10000,50000),new BaseRewardVO(68002,15),new BaseRewardVO(10852,6)]),new StarRewardVO(227,1042,45,[new BaseRewardVO(10000,80000),new BaseRewardVO(18541,2),new BaseRewardVO(18542,2)])]);
         this._list[this._list.length] = new AreaRewardVO(228,[new StarRewardVO(228,1043,15,[new BaseRewardVO(10000,40000),new BaseRewardVO(68001,15),new BaseRewardVO(10851,6)]),new StarRewardVO(228,1044,30,[new BaseRewardVO(10000,50000),new BaseRewardVO(68002,15),new BaseRewardVO(10852,6)]),new StarRewardVO(228,1045,45,[new BaseRewardVO(10000,80000),new BaseRewardVO(18541,2),new BaseRewardVO(18542,2)])]);
         this._list[this._list.length] = new AreaRewardVO(229,[new StarRewardVO(229,1046,15,[new BaseRewardVO(10000,40000),new BaseRewardVO(68001,15),new BaseRewardVO(11005,20)]),new StarRewardVO(229,1047,30,[new BaseRewardVO(10000,50000),new BaseRewardVO(68002,15),new BaseRewardVO(11104,10)]),new StarRewardVO(229,1048,45,[new BaseRewardVO(10000,80000),new BaseRewardVO(18541,2),new BaseRewardVO(18542,2),new BaseRewardVO(10320,10)])]);
         this._list[this._list.length] = new AreaRewardVO(230,[new StarRewardVO(230,1049,15,[new BaseRewardVO(10000,40000),new BaseRewardVO(18531,3),new BaseRewardVO(11005,20)]),new StarRewardVO(230,10501,30,[new BaseRewardVO(10000,50000),new BaseRewardVO(18532,3),new BaseRewardVO(11104,10)]),new StarRewardVO(230,10511,45,[new BaseRewardVO(10000,80000),new BaseRewardVO(18541,2),new BaseRewardVO(18542,2),new BaseRewardVO(10320,10)])]);
         this._list[this._list.length] = new AreaRewardVO(231,[new StarRewardVO(231,1052,15,[new BaseRewardVO(10000,40000),new BaseRewardVO(18531,3),new BaseRewardVO(11005,20)]),new StarRewardVO(231,1053,30,[new BaseRewardVO(10000,50000),new BaseRewardVO(18532,3),new BaseRewardVO(11104,10)]),new StarRewardVO(231,1054,45,[new BaseRewardVO(10000,80000),new BaseRewardVO(18541,2),new BaseRewardVO(18542,2),new BaseRewardVO(10320,10)])]);
         this._list[this._list.length] = new AreaRewardVO(232,[new StarRewardVO(232,1055,15,[new BaseRewardVO(10000,40000),new BaseRewardVO(18531,3),new BaseRewardVO(11005,20)]),new StarRewardVO(232,1056,30,[new BaseRewardVO(10000,50000),new BaseRewardVO(18532,3),new BaseRewardVO(11104,10)]),new StarRewardVO(232,1057,45,[new BaseRewardVO(10000,80000),new BaseRewardVO(18541,3),new BaseRewardVO(18542,3),new BaseRewardVO(10320,10)])]);
         this._list[this._list.length] = new AreaRewardVO(233,[new StarRewardVO(233,1058,15,[new BaseRewardVO(10000,100000),new BaseRewardVO(18531,3),new BaseRewardVO(11005,20)]),new StarRewardVO(233,1059,30,[new BaseRewardVO(10000,200000),new BaseRewardVO(18532,3),new BaseRewardVO(11104,10)]),new StarRewardVO(233,1062,45,[new BaseRewardVO(10000,300000),new BaseRewardVO(18541,3),new BaseRewardVO(18542,3),new BaseRewardVO(10959,1)])]);
         this._list[this._list.length] = new AreaRewardVO(234,[new StarRewardVO(234,1063,15,[new BaseRewardVO(10000,100000),new BaseRewardVO(18531,3),new BaseRewardVO(11005,20)]),new StarRewardVO(234,1064,30,[new BaseRewardVO(10000,200000),new BaseRewardVO(18532,3),new BaseRewardVO(11104,10)]),new StarRewardVO(234,1065,45,[new BaseRewardVO(10000,300000),new BaseRewardVO(18541,3),new BaseRewardVO(18542,3),new BaseRewardVO(10959,1)])]);
         this._list[this._list.length] = new AreaRewardVO(235,[new StarRewardVO(235,1066,15,[new BaseRewardVO(10000,100000),new BaseRewardVO(18531,3),new BaseRewardVO(11005,20)]),new StarRewardVO(235,1067,30,[new BaseRewardVO(10000,200000),new BaseRewardVO(18532,3),new BaseRewardVO(11104,10)]),new StarRewardVO(235,1068,45,[new BaseRewardVO(10000,300000),new BaseRewardVO(18541,3),new BaseRewardVO(18542,3),new BaseRewardVO(10959,1)])]);
         this._list[this._list.length] = new AreaRewardVO(236,[new StarRewardVO(236,1069,15,[new BaseRewardVO(10000,100000),new BaseRewardVO(18531,3),new BaseRewardVO(11005,20)]),new StarRewardVO(236,1070,30,[new BaseRewardVO(10000,200000),new BaseRewardVO(18532,3),new BaseRewardVO(11104,10)]),new StarRewardVO(236,1071,45,[new BaseRewardVO(10000,300000),new BaseRewardVO(18541,3),new BaseRewardVO(18542,3),new BaseRewardVO(10959,1)])]);
         this._list[this._list.length] = new AreaRewardVO(237,[new StarRewardVO(237,1072,15,[new BaseRewardVO(10000,100000),new BaseRewardVO(18531,3),new BaseRewardVO(11005,20)]),new StarRewardVO(237,1073,30,[new BaseRewardVO(10000,200000),new BaseRewardVO(18532,3),new BaseRewardVO(11104,10)]),new StarRewardVO(237,1074,45,[new BaseRewardVO(10000,300000),new BaseRewardVO(18541,5),new BaseRewardVO(18542,5),new BaseRewardVO(10959,1)])]);
         this._list[this._list.length] = new AreaRewardVO(238,[new StarRewardVO(238,1075,15,[new BaseRewardVO(10000,100000),new BaseRewardVO(18531,3),new BaseRewardVO(11005,20)]),new StarRewardVO(238,1076,30,[new BaseRewardVO(10000,200000),new BaseRewardVO(18532,3),new BaseRewardVO(11104,10)]),new StarRewardVO(238,1077,45,[new BaseRewardVO(10000,300000),new BaseRewardVO(18541,5),new BaseRewardVO(18542,5),new BaseRewardVO(10959,1)])]);
         this._list[this._list.length] = new AreaRewardVO(239,[new StarRewardVO(239,1078,15,[new BaseRewardVO(10000,100000),new BaseRewardVO(18531,3),new BaseRewardVO(11005,20)]),new StarRewardVO(239,1079,30,[new BaseRewardVO(10000,200000),new BaseRewardVO(18532,3),new BaseRewardVO(11104,10)]),new StarRewardVO(239,1080,45,[new BaseRewardVO(10000,300000),new BaseRewardVO(18541,5),new BaseRewardVO(18542,5),new BaseRewardVO(10959,1)])]);
         this._list[this._list.length] = new AreaRewardVO(240,[new StarRewardVO(240,1081,15,[new BaseRewardVO(10000,100000),new BaseRewardVO(18531,3),new BaseRewardVO(11005,20)]),new StarRewardVO(240,1082,30,[new BaseRewardVO(10000,200000),new BaseRewardVO(18532,3),new BaseRewardVO(11104,10)]),new StarRewardVO(240,1083,45,[new BaseRewardVO(10000,300000),new BaseRewardVO(18541,5),new BaseRewardVO(18542,5),new BaseRewardVO(10959,1)])]);
         this._list[this._list.length] = new AreaRewardVO(241,[new StarRewardVO(241,1084,12,[new BaseRewardVO(10000,100000),new BaseRewardVO(18531,3),new BaseRewardVO(11005,20)]),new StarRewardVO(241,1085,24,[new BaseRewardVO(10000,200000),new BaseRewardVO(18532,3),new BaseRewardVO(11104,10)]),new StarRewardVO(241,1086,36,[new BaseRewardVO(10000,300000),new BaseRewardVO(18541,5),new BaseRewardVO(18542,5),new BaseRewardVO(10960,1)])]);
         this._list[this._list.length] = new AreaRewardVO(242,[new StarRewardVO(242,1087,12,[new BaseRewardVO(10000,100000),new BaseRewardVO(18531,3),new BaseRewardVO(11005,20)]),new StarRewardVO(242,1088,24,[new BaseRewardVO(10000,200000),new BaseRewardVO(18532,3),new BaseRewardVO(11104,10)]),new StarRewardVO(242,1089,36,[new BaseRewardVO(10000,300000),new BaseRewardVO(18541,5),new BaseRewardVO(18542,5),new BaseRewardVO(10960,1)])]);
         this._list[this._list.length] = new AreaRewardVO(243,[new StarRewardVO(243,1090,12,[new BaseRewardVO(10000,100000),new BaseRewardVO(18531,3),new BaseRewardVO(11005,20)]),new StarRewardVO(243,1091,24,[new BaseRewardVO(10000,200000),new BaseRewardVO(18532,3),new BaseRewardVO(11104,10)]),new StarRewardVO(243,1092,36,[new BaseRewardVO(10000,300000),new BaseRewardVO(18541,5),new BaseRewardVO(18542,5),new BaseRewardVO(10960,1)])]);
         this._list[this._list.length] = new AreaRewardVO(244,[new StarRewardVO(244,1093,12,[new BaseRewardVO(10000,100000),new BaseRewardVO(18531,3),new BaseRewardVO(11005,20)]),new StarRewardVO(244,1094,24,[new BaseRewardVO(10000,200000),new BaseRewardVO(18532,3),new BaseRewardVO(11104,10)]),new StarRewardVO(244,1095,36,[new BaseRewardVO(10000,300000),new BaseRewardVO(18541,5),new BaseRewardVO(18542,5),new BaseRewardVO(10960,1)])]);
         this._list[this._list.length] = new AreaRewardVO(245,[new StarRewardVO(245,1096,12,[new BaseRewardVO(10000,100000),new BaseRewardVO(18531,3),new BaseRewardVO(11005,20)]),new StarRewardVO(245,1097,24,[new BaseRewardVO(10000,200000),new BaseRewardVO(18532,3),new BaseRewardVO(11104,10)]),new StarRewardVO(245,1098,36,[new BaseRewardVO(10000,300000),new BaseRewardVO(18541,5),new BaseRewardVO(18542,5),new BaseRewardVO(10960,1)])]);
         this._list[this._list.length] = new AreaRewardVO(246,[new StarRewardVO(246,1099,12,[new BaseRewardVO(10000,100000),new BaseRewardVO(18531,3),new BaseRewardVO(11005,20)]),new StarRewardVO(246,1100,24,[new BaseRewardVO(10000,200000),new BaseRewardVO(18532,3),new BaseRewardVO(11104,10)]),new StarRewardVO(246,1101,36,[new BaseRewardVO(10000,300000),new BaseRewardVO(18541,5),new BaseRewardVO(18542,5),new BaseRewardVO(10960,1)])]);
      }
      
      public function findVO(param1:int) : AreaRewardVO
      {
         var _loc2_:AreaRewardVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.areaID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

