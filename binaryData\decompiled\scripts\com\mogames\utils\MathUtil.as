package com.mogames.utils
{
   import com.mogames.data.Oint;
   import com.mogames.data.Onum;
   import flash.geom.Point;
   import mogames.gameData.ServerProxy;
   import mogames.gameNet.Secret;
   
   public class MathUtil
   {
      
      public function MathUtil()
      {
         super();
      }
      
      public static function addToArr(param1:*, param2:Array) : void
      {
         if(param2.indexOf(param1) != -1)
         {
            return;
         }
         param2[param2.length] = param1;
      }
      
      public static function toFixed(param1:Number, param2:int = 1) : Number
      {
         return Number(param1.toFixed(param2));
      }
      
      public static function randValue(... rest) : Object
      {
         return rest[int(Math.random() * rest.length)];
      }
      
      public static function randArr(param1:Array) : Array
      {
         var _loc4_:int = 0;
         var _loc2_:Array = [];
         var _loc3_:int = int(param1.length);
         var _loc5_:int = 0;
         while(_loc5_ < _loc3_)
         {
            _loc4_ = Math.random() * param1.length;
            _loc2_[_loc5_] = param1[_loc4_];
            param1.splice(_loc4_,1);
            _loc5_++;
         }
         return _loc2_;
      }
      
      public static function cloneArr(param1:Array) : Array
      {
         var _loc2_:Array = [];
         var _loc3_:int = 0;
         var _loc4_:int = int(param1.length);
         while(_loc3_ < _loc4_)
         {
            _loc2_[_loc3_] = param1[_loc3_];
            _loc3_++;
         }
         return _loc2_;
      }
      
      public static function arrStringToNum(param1:Array) : Array
      {
         var _loc2_:int = 0;
         var _loc3_:int = int(param1.length);
         while(_loc2_ < _loc3_)
         {
            param1[_loc2_] = parseInt(param1[_loc2_]);
            _loc2_++;
         }
         return param1;
      }
      
      public static function checkOdds(param1:Number) : Boolean
      {
         return int(Math.random() * 1000 + 1) <= param1;
      }
      
      public static function randomNum(param1:int, param2:int) : int
      {
         param2 += 1;
         return Math.random() * (param2 - param1) + param1;
      }
      
      public static function checkInRate(param1:int, param2:int, param3:int) : Boolean
      {
         return param1 >= param2 && param1 <= param3;
      }
      
      public static function convertWAN(param1:int) : String
      {
         if(param1 > 10000)
         {
            return int(param1 / 10000) + "万";
         }
         return param1 + "";
      }
      
      public static function convertPER(param1:int) : String
      {
         return toFixed(param1 * 0.1) + "%";
      }
      
      public static function convertBEI(param1:int) : String
      {
         return toFixed(param1 * 0.01,2) + "倍";
      }
      
      public static function inCircle(param1:Point, param2:Point, param3:int) : Boolean
      {
         return Point.distance(param1,param2) <= param3;
      }
      
      public static function inRect(param1:Point, param2:Point, param3:int) : Boolean
      {
         return Math.abs(param1.y - param2.y) <= param3 / 2 && Math.abs(param1.x - param2.x) <= param3;
      }
      
      public static function inEllipse(param1:Point, param2:Point, param3:int) : Boolean
      {
         return 4 * (param1.x - param2.x) * (param1.x - param2.x) + 16 * (param1.y - param2.y) * (param1.y - param2.y) <= param3 * param3;
      }
      
      public static function inPosX(param1:int, param2:int, param3:int) : Boolean
      {
         return Math.abs(param1 - param2) <= param3;
      }
      
      public static function checkWD(param1:int, param2:Array) : int
      {
         var _loc3_:int = 0;
         var _loc4_:int = int(param2.length);
         while(_loc3_ < _loc4_)
         {
            if(param1 <= param2[_loc3_])
            {
               return _loc3_ + 1;
            }
            _loc3_++;
         }
         return 1;
      }
      
      public static function saveNUM(param1:Onum, param2:Number) : void
      {
         Secret.hideNum(param1,param2);
      }
      
      public static function loadNUM(param1:Onum) : Number
      {
         if(!param1.hash)
         {
            return 0;
         }
         if(param1.hash != String(Number(param1.real) + param1.salt))
         {
            ServerProxy.instance().checkCode = 101;
            return -1;
         }
         return (Number(param1.real) - 4) * 0.5;
      }
      
      public static function saveINT(param1:Oint, param2:int) : void
      {
         Secret.hideInt(param1,param2);
      }
      
      public static function loadINT(param1:Oint) : int
      {
         if(!param1.hash)
         {
            return 0;
         }
         if(param1.hash != String(int(param1.real) + param1.salt))
         {
            ServerProxy.instance().checkCode = 102;
            return -1;
         }
         return (int(param1.real) - 4) * 0.5;
      }
   }
}

