package file
{
   import com.mogames.utils.MathUtil;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.ConstData;
   import mogames.gameData.base.func.MLRandWaveVO;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.fuben.vo.FubenVO;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class HuangMoConfig
   {
      
      private static var _instance:HuangMoConfig;
      
      private var _waves:Array;
      
      private var _armyID:int;
      
      private var _bossID:int;
      
      private var _paths:Array;
      
      public var argVO:RoleArgVO;
      
      public function HuangMoConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : HuangMoConfig
      {
         if(!_instance)
         {
            _instance = new HuangMoConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._armyID = 247;
         this._bossID = 246;
         this.argVO = new RoleArgVO(166,600,2000,99999,30,10,250,130,null);
         this._waves = [];
         this._waves[this._waves.length] = new MLRandWaveVO(15,[{
            "index":4,
            "num":1
         },{
            "index":7,
            "num":1
         },{
            "index":14,
            "num":1
         }],[5,20,20,15,20,20,15,20,15,25,20,15,20,20,25],[20,15,20,15,15,15,20,10,15,20,15,20,15,20,20],[0,1,2,3,0,1,2,2,3,1,1,2,2,3,1]);
         this._waves[this._waves.length] = new MLRandWaveVO(14,[{
            "index":3,
            "num":1
         },{
            "index":8,
            "num":1
         },{
            "index":11,
            "num":1
         }],[5,20,15,20,20,20,15,20,15,25,20,15,20,20],[15,20,15,15,15,20,15,15,20,15,15,15,20,30],[1,2,3,0,1,2,3,0,1,2,3,0,1,2]);
         this._waves[this._waves.length] = new MLRandWaveVO(13,[{
            "index":5,
            "num":1
         },{
            "index":6,
            "num":1
         },{
            "index":10,
            "num":1
         }],[5,15,20,20,20,15,15,20,20,25,20,15,20],[20,15,15,15,20,15,20,15,20,25,15,15,30],[2,3,0,1,2,3,0,1,2,0,0,1,2]);
         this._waves[this._waves.length] = new MLRandWaveVO(12,[{
            "index":2,
            "num":1
         },{
            "index":5,
            "num":1
         },{
            "index":9,
            "num":1
         }],[5,20,20,15,20,20,15,20,20,25,15,20],[20,15,15,15,20,15,20,15,15,25,15,30],[2,3,0,1,2,3,0,1,2,0,1,2]);
         this._waves[this._waves.length] = new MLRandWaveVO(11,[{
            "index":6,
            "num":1
         },{
            "index":10,
            "num":1
         }],[5,20,20,15,20,20,15,20,20,20,20],[20,15,20,15,15,20,15,15,15,20,30],[3,0,1,2,3,0,1,2,3,0,1]);
         this._waves[this._waves.length] = new MLRandWaveVO(10,[{
            "index":3,
            "num":1
         },{
            "index":9,
            "num":1
         }],[5,20,20,20,20,20,15,20,20,25],[15,25,15,20,15,20,15,15,15,30],[3,0,1,2,3,0,1,2,3,0]);
         this._waves[this._waves.length] = new MLRandWaveVO(9,[{
            "index":3,
            "num":1
         },{
            "index":6,
            "num":1
         }],[5,20,20,15,20,20,15,15,20],[15,20,15,20,15,15,20,15,30],[2,2,3,0,1,3,3,0,2,3]);
      }
      
      private function newSkills() : Array
      {
         var _loc1_:Array = [];
         _loc1_[_loc1_.length] = {
            "sid":1001,
            "arg":{
               "hurt":this.countHurt(58),
               "keepTime":3,
               "hurtCount":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1002,
            "arg":{
               "hurt":this.countHurt(58),
               "hurtCount":5
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1003,
            "arg":{
               "hurt":this.countHurt(56),
               "hurtCount":5
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1004,
            "arg":{
               "hurt":this.countHurt(74),
               "roleNum":4
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1005,
            "arg":{
               "hurt":this.countHurt(60),
               "roleNum":10
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1006,
            "arg":{"hurt":this.countHurt(116)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1007,
            "arg":{
               "hurt":this.countHurt(58),
               "keepTime":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1008,
            "arg":{"hurt":this.countHurt(52)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1009,
            "arg":{"hurt":this.countHurt(103)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1010,
            "arg":{"hurt":this.countHurt(43)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1011,
            "arg":{"hurt":this.countHurt(46)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1012,
            "arg":{"hurt":this.countHurt(134)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1013,
            "arg":{"hurt":this.countHurt(61)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1014,
            "arg":{
               "hurt":this.countHurt(46),
               "keepTime":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1015,
            "arg":{
               "hurt":this.countHurt(31),
               "keepTime":8,
               "hurtCount":5
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1016,
            "arg":{
               "hurt":this.countHurt(64),
               "roleNum":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1017,
            "arg":{
               "hurt":this.countHurt(46),
               "hurtCount":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1018,
            "arg":{"hurt":this.countHurt(40)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1019,
            "arg":{
               "hurt":this.countHurt(40),
               "keepTime":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1020,
            "arg":{"hurt":this.countHurt(39)}
         };
         return _loc1_;
      }
      
      private function countHurt(param1:int) : int
      {
         return param1 * int(MasterProxy.instance().masterVO.level * 0.55 + 1);
      }
      
      public function newMiLinWave() : Object
      {
         var _loc6_:OneWaveVO = null;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc1_:Array = this.newSkills();
         var _loc2_:MLRandWaveVO = this._waves[int(Math.random() * this._waves.length)];
         var _loc3_:WaveDataVO = new WaveDataVO();
         _loc3_.limitBR = new WaveLimitVO(0,1,1);
         _loc3_.zhuBoss = this.newMLBoss(_loc1_[int(Math.random() * _loc1_.length)]);
         var _loc4_:int = 0;
         var _loc5_:int = _loc2_.total;
         while(_loc4_ < _loc5_)
         {
            _loc6_ = new OneWaveVO(_loc2_.findTime(_loc4_));
            _loc6_.addEnemy(new WaveEnemyVO(_loc2_.findEmemies(_loc4_),this.newEnemyArg));
            _loc7_ = _loc2_.findFu(_loc4_);
            _loc8_ = 0;
            while(_loc8_ < _loc7_)
            {
               _loc6_.addFu(this.newMLBoss(_loc1_[int(Math.random() * _loc1_.length)]));
               _loc8_++;
            }
            _loc3_.addWave(_loc6_);
            _loc4_++;
         }
         _loc1_.length = 0;
         _loc1_ = null;
         return {
            "wave":_loc3_,
            "location":_loc2_.location
         };
      }
      
      private function get newEnemyArg() : RoleArgVO
      {
         var _loc1_:int = MasterProxy.instance().masterVO.level * 150;
         var _loc2_:int = MasterProxy.instance().masterVO.level * 9;
         var _loc3_:int = MasterProxy.instance().masterVO.level * 2;
         return new RoleArgVO(this._armyID,_loc1_,_loc2_,_loc3_,20,50,170,130,null);
      }
      
      private function newMLBoss(param1:Object) : BossArgVO
      {
         var _loc2_:int = MasterProxy.instance().masterVO.level * 600;
         var _loc3_:int = MasterProxy.instance().masterVO.level * 15;
         var _loc4_:int = MasterProxy.instance().masterVO.level * 3;
         return new BossArgVO(this._bossID,_loc2_,_loc3_,_loc4_,40,80,220,160,new BossSkillData0(150,param1.arg,2),param1.sid,0);
      }
      
      public function enemyPaths(param1:int) : Array
      {
         if(!this._paths)
         {
            this._paths = [];
            this._paths[0] = [new Point(65,168),new Rectangle(78,186,84,174)];
            this._paths[1] = [new Point(982,-48),new Rectangle(922,58,110,76)];
            this._paths[2] = [new Point(974,630),new Rectangle(922,460,92,76)];
            this._paths[3] = [new Point(1962,288),new Rectangle(1745,232,138,136)];
         }
         var _loc2_:Array = this._paths[param1];
         var _loc3_:Point = new Point();
         _loc3_.x = _loc2_[1].x + Math.random() * _loc2_[1].width;
         _loc3_.y = _loc2_[1].y + Math.random() * _loc2_[1].height;
         return [_loc2_[0],_loc3_];
      }
      
      public function newReward(param1:Boolean) : Array
      {
         if(!param1)
         {
            return this.loseReward;
         }
         return this.winReward;
      }
      
      private function get winReward() : Array
      {
         var _loc6_:int = 0;
         var _loc1_:Array = [];
         var _loc2_:int = MasterProxy.instance().masterVO.level * MathUtil.randomNum(350,550);
         _loc1_[0] = new BaseRewardVO(10000,_loc2_);
         var _loc3_:int = ConstData.INT1.v;
         if(MathUtil.checkOdds(100))
         {
            _loc3_ = ConstData.INT2.v;
         }
         var _loc4_:FubenVO = FubenConfig.instance().findFuben(601);
         var _loc5_:Array = _loc4_.drops.slice(1);
         var _loc7_:int = 0;
         while(_loc7_ < _loc3_)
         {
            _loc6_ = Math.random() * _loc5_.length;
            _loc1_[_loc1_.length] = _loc5_[_loc6_];
            _loc5_.splice(_loc6_,1);
            _loc7_++;
         }
         return _loc1_;
      }
      
      private function get loseReward() : Array
      {
         var _loc1_:int = MasterProxy.instance().masterVO.level * MathUtil.randomNum(200,300);
         return [new BaseRewardVO(10000,_loc1_)];
      }
   }
}

