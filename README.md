# 🏛️ 三国存档编辑器

一个功能强大的三国题材游戏存档编辑器，支持可视化编辑游戏资源、英雄、装备等数据。

## ✨ 功能特色

### 🎯 核心功能
- 📁 **文件管理** - 打开、保存、备份存档文件
- 💰 **资源编辑** - 修改银币、威望、体力、元宝等游戏资源
- ⚔️ **英雄管理** - 编辑英雄等级、星级、经验值
- 🎒 **物品管理** - 查看和管理背包物品
- 📊 **进度管理** - 查看副本进度和任务状态
- 🔄 **数据验证** - 自动验证数据完整性和合法性

### 🛡️ 安全特性
- 🔒 **自动备份** - 编辑前自动创建原始存档备份
- ✅ **数据验证** - 防止无效数据破坏存档
- 🔄 **撤销功能** - 支持重置到原始状态
- 📋 **修改统计** - 显示修改内容统计

## 🚀 快速开始

### 方案一：Web版本（推荐新手）

1. **直接使用**
   ```bash
   # 下载文件
   wget save-editor.html
   
   # 在浏览器中打开
   open save-editor.html
   ```

2. **功能说明**
   - 纯前端实现，无需安装
   - 支持拖拽上传存档文件
   - 实时预览修改效果
   - 一键导出修改后的存档

### 方案二：Python桌面版（功能最全）

1. **环境要求**
   ```bash
   Python 3.7+
   tkinter (通常随Python安装)
   ```

2. **安装运行**
   ```bash
   # 下载文件
   git clone <repository>
   cd save-editor
   
   # 运行程序
   python save_editor.py
   ```

3. **功能特色**
   - 原生桌面应用体验
   - 完整的文件管理功能
   - 高级数据编辑选项
   - 批量操作支持

### 方案三：JavaScript模块版（开发者）

1. **集成使用**
   ```javascript
   // 引入核心模块
   const SaveEditor = require('./save-editor-core.js');
   
   // 创建编辑器实例
   const editor = new SaveEditor();
   
   // 加载存档
   const result = editor.loadSave(jsonData);
   
   // 修改资源
   editor.updateResources({
       silver: 99999999,
       prestige: 99999999
   });
   
   // 导出存档
   const modifiedSave = editor.exportSave();
   ```

## 📖 使用指南

### 🔧 基础操作

1. **打开存档**
   - 点击"打开存档"按钮
   - 选择游戏存档JSON文件
   - 等待加载完成

2. **编辑资源**
   - 切换到"资源管理"标签
   - 修改银币、威望等数值
   - 点击"更新资源"保存

3. **管理英雄**
   - 在"英雄管理"中查看所有英雄
   - 选择英雄后编辑等级、星级、经验
   - 支持批量升级操作

4. **保存存档**
   - 点击"保存存档"覆盖原文件
   - 或使用"另存为"创建新文件

### ⚠️ 注意事项

1. **备份重要**
   - 编辑前务必备份原始存档
   - 程序会自动创建备份文件

2. **数值限制**
   - 银币/威望：最大999,999,999
   - 体力：最大999
   - 英雄等级：最大200
   - 英雄星级：最大10

3. **兼容性**
   - 支持标准JSON格式存档
   - 兼容大部分三国题材游戏

## 🏗️ 项目结构

```
save-editor/
├── save-editor.html          # Web版本主文件
├── save-editor-core.js       # JavaScript核心模块
├── save_editor.py            # Python桌面版
├── README.md                 # 项目说明文档
├── docs/                     # 详细文档
│   ├── api.md               # API文档
│   ├── development.md       # 开发指南
│   └── troubleshooting.md   # 故障排除
└── examples/                 # 示例文件
    ├── sample_save.json     # 示例存档
    └── config_templates/    # 配置模板
```

## 🔧 开发指南

### 添加新英雄

```javascript
// 在hero_db中添加新英雄数据
const newHero = {
    id: 999,
    name: "新英雄",
    faction: "蜀",
    type: "武将", 
    rarity: 5
};
```

### 添加新物品

```javascript
// 在item_db中添加新物品数据
const newItem = {
    id: 99999,
    name: "新物品",
    type: "装备",
    rarity: 5
};
```

### 扩展功能模块

```python
class CustomModule:
    def __init__(self, editor):
        self.editor = editor
    
    def custom_function(self):
        # 自定义功能实现
        pass
```

## 🐛 故障排除

### 常见问题

1. **存档加载失败**
   - 检查文件格式是否为有效JSON
   - 确认文件编码为UTF-8
   - 验证文件是否损坏

2. **数据修改无效**
   - 确认已点击"更新"按钮
   - 检查数值是否在有效范围内
   - 验证存档结构是否完整

3. **程序崩溃**
   - 查看错误日志
   - 确认Python版本兼容性
   - 检查依赖库是否完整

### 获取帮助

- 📧 **邮件支持**: <EMAIL>
- 💬 **在线讨论**: [GitHub Issues](https://github.com/example/save-editor/issues)
- 📚 **详细文档**: [Wiki页面](https://github.com/example/save-editor/wiki)

## 🤝 贡献指南

欢迎贡献代码和建议！

1. **Fork项目**
2. **创建功能分支** (`git checkout -b feature/AmazingFeature`)
3. **提交更改** (`git commit -m 'Add some AmazingFeature'`)
4. **推送分支** (`git push origin feature/AmazingFeature`)
5. **创建Pull Request**

### 开发规范

- 遵循PEP 8代码风格（Python）
- 使用ESLint规范（JavaScript）
- 添加适当的注释和文档
- 编写单元测试

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- 感谢所有贡献者的努力
- 特别感谢三国游戏社区的支持
- 参考了多个开源项目的优秀设计

## 📊 项目状态

![GitHub stars](https://img.shields.io/github/stars/example/save-editor)
![GitHub forks](https://img.shields.io/github/forks/example/save-editor)
![GitHub issues](https://img.shields.io/github/issues/example/save-editor)
![GitHub license](https://img.shields.io/github/license/example/save-editor)

---

## 🎮 支持的游戏

目前支持以下三国题材游戏的存档编辑：

- ✅ 三国志系列
- ✅ 三国群英传系列  
- ✅ 手机三国游戏
- 🔄 更多游戏支持开发中...

## 🔮 未来计划

- [ ] 支持更多游戏格式
- [ ] 添加装备编辑功能
- [ ] 实现技能树编辑
- [ ] 开发云端同步功能
- [ ] 添加多语言支持
- [ ] 创建移动端版本

---

**⭐ 如果这个项目对您有帮助，请给我们一个Star！**
