package mogames.gameData.bag
{
   import mogames.gameData.bag.base.GoodBagVO;
   
   public class BagGoodProxy
   {
      
      private static var _instance:BagGoodProxy;
      
      private var _goodBag:Array;
      
      public function BagGoodProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
      }
      
      public static function instance() : BagGoodProxy
      {
         if(!_instance)
         {
            _instance = new BagGoodProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this._goodBag = [];
         this._goodBag[this._goodBag.length] = new GoodBagVO(11);
         this._goodBag[this._goodBag.length] = new GoodBagVO(12);
         this._goodBag[this._goodBag.length] = new GoodBagVO(13);
         this._goodBag[this._goodBag.length] = new GoodBagVO(14);
         this._goodBag[this._goodBag.length] = new GoodBagVO(15);
         this._goodBag[this._goodBag.length] = new GoodBagVO(16);
         this._goodBag[this._goodBag.length] = new GoodBagVO(17);
      }
      
      public function set loadData(param1:Array) : void
      {
         var _loc2_:Array = null;
         var _loc3_:GoodBagVO = null;
         var _loc4_:String = null;
         this.startNew();
         if(!param1)
         {
            return;
         }
         for each(_loc4_ in param1)
         {
            _loc2_ = _loc4_.split("H");
            _loc3_ = this.findGoodBag(int(_loc2_[0]));
            if(_loc3_)
            {
               _loc3_.setGoodID(int(_loc2_[1]));
            }
         }
      }
      
      public function get saveData() : Array
      {
         var _loc2_:GoodBagVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._goodBag)
         {
            _loc1_[_loc1_.length] = _loc2_.bagID + "H" + _loc2_.goodID;
         }
         return _loc1_;
      }
      
      public function findGoodBag(param1:int) : GoodBagVO
      {
         var _loc2_:GoodBagVO = null;
         for each(_loc2_ in this._goodBag)
         {
            if(_loc2_.bagID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function hasGood(param1:int) : Boolean
      {
         var _loc2_:GoodBagVO = null;
         for each(_loc2_ in this._goodBag)
         {
            if(_loc2_.goodID == param1)
            {
               return true;
            }
         }
         return false;
      }
      
      public function get goodBag() : Array
      {
         return this._goodBag;
      }
   }
}

