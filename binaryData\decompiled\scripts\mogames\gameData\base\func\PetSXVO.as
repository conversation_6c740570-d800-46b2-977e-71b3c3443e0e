package mogames.gameData.base.func
{
   import com.mogames.data.IntList;
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.vip.VipProxy;
   
   public class PetSXVO
   {
      
      private var _petID:Oint = new Oint();
      
      private var _rate:IntList;
      
      private var _list:Array;
      
      public function PetSXVO(param1:int, param2:Array, param3:Array)
      {
         super();
         MathUtil.saveINT(this._petID,param1);
         this._rate = new IntList(param2);
         this._list = param3;
      }
      
      public function get petID() : int
      {
         return MathUtil.loadINT(this._petID);
      }
      
      public function get needList() : Array
      {
         return this._list;
      }
      
      public function findList(param1:int) : Array
      {
         return this._list[param1];
      }
      
      public function findRate(param1:int) : int
      {
         if(VipProxy.instance().hasFunc(123))
         {
            return 1000;
         }
         return this._rate.findValue(param1);
      }
   }
}

