package com.mogames.comt
{
   import com.mogames.sound.SoundManager;
   import com.mogames.utils.FontUtil;
   import com.mogames.utils.MethodUtil;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   
   public class NumPage
   {
      
      private var _skin:Object;
      
      private var _pageFunc:Function;
      
      private var _pageStart:int;
      
      private var _max:int;
      
      public function NumPage(param1:Object, param2:Function, param3:int = 90)
      {
         super();
         this._skin = param1;
         this._pageFunc = param2;
         this._max = param3;
         this._skin.btnPrev.addEventListener(MouseEvent.MOUSE_DOWN,this.onPrev);
         this._skin.btnNext.addEventListener(MouseEvent.MOUSE_DOWN,this.onNext);
         this.initNum();
         this.resetPage();
      }
      
      public function resetPage(param1:Boolean = false) : void
      {
         this.defaultNum();
         this.setStartNum(1);
         this.updateBtn();
         if(param1)
         {
            this._skin.mcNum0.dispatchEvent(new MouseEvent(MouseEvent.MOUSE_DOWN));
         }
      }
      
      public function setStartNum(param1:int) : void
      {
         this._pageStart = param1;
         var _loc2_:int = 0;
         while(_loc2_ < 10)
         {
            FontUtil.setText(this._skin["mcNum" + _loc2_].txt,_loc2_ + param1);
            _loc2_++;
         }
      }
      
      private function defaultNum() : void
      {
         var _loc1_:int = 0;
         while(_loc1_ < 10)
         {
            this._skin["mcNum" + _loc1_].gotoAndStop(1);
            _loc1_++;
         }
      }
      
      private function initNum() : void
      {
         var _loc1_:int = 0;
         while(_loc1_ < 10)
         {
            this._skin["mcNum" + _loc1_].buttonMode = true;
            this._skin["mcNum" + _loc1_].mouseChildren = false;
            this._skin["mcNum" + _loc1_].addEventListener(MouseEvent.MOUSE_DOWN,this.onSelect);
            _loc1_++;
         }
      }
      
      private function onSelect(param1:MouseEvent) : void
      {
         var _loc2_:MovieClip = param1.currentTarget as MovieClip;
         if(_loc2_.currentFrame == 2)
         {
            return;
         }
         this.defaultNum();
         _loc2_.gotoAndStop(2);
         if(this._pageFunc != null)
         {
            this._pageFunc(int(_loc2_.txt.text));
         }
         SoundManager.instance().playAudio("AUDIO_CLICK");
      }
      
      private function onPrev(param1:MouseEvent) : void
      {
         if(this._pageStart < 10)
         {
            return;
         }
         this.setStartNum(this._pageStart - 10);
         this.updateBtn();
         this.defaultNum();
         this._skin.mcNum0.dispatchEvent(new MouseEvent(MouseEvent.MOUSE_DOWN));
      }
      
      private function onNext(param1:MouseEvent) : void
      {
         if(this._pageStart > this._max)
         {
            return;
         }
         this.setStartNum(this._pageStart + 10);
         this.updateBtn();
         this.defaultNum();
         this._skin.mcNum0.dispatchEvent(new MouseEvent(MouseEvent.MOUSE_DOWN));
      }
      
      private function updateBtn() : void
      {
         MethodUtil.enableBtn(this._skin.btnPrev,this._pageStart > 10);
         MethodUtil.enableBtn(this._skin.btnNext,this._pageStart < this._max);
      }
      
      private function checkIndex(param1:String) : int
      {
         return int(param1.slice(5));
      }
      
      public function destroy() : void
      {
         var _loc1_:int = 0;
         while(_loc1_ < 10)
         {
            this._skin["mcNum" + _loc1_].mouseChildren = false;
            this._skin["mcNum" + _loc1_].removeEventListener(MouseEvent.MOUSE_DOWN,this.onSelect);
            _loc1_++;
         }
         this._skin.btnPrev.removeEventListener(MouseEvent.MOUSE_DOWN,this.onPrev);
         this._skin.btnNext.removeEventListener(MouseEvent.MOUSE_DOWN,this.onNext);
         this._skin = null;
      }
   }
}

