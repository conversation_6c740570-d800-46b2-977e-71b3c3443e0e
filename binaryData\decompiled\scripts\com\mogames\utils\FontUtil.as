package com.mogames.utils
{
   import flash.text.Font;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class FontUtil
   {
      
      private static var fontList:Array;
      
      private static var txtFormat:TextFormat;
      
      public function FontUtil()
      {
         super();
      }
      
      public static function registerFont() : void
      {
         Font.registerFont(GAME_FONT as Class);
         var _loc1_:Array = Font.enumerateFonts(false);
         txtFormat = new TextFormat();
         txtFormat.font = _loc1_[0].fontName;
         txtFormat.kerning = true;
      }
      
      public static function formatText(param1:TextField) : void
      {
         param1.embedFonts = true;
         var _loc2_:Array = Font.enumerateFonts(false);
         var _loc3_:TextFormat = new TextFormat();
         _loc3_.font = _loc2_[0].fontName;
         _loc3_.kerning = true;
         param1.defaultTextFormat = _loc3_;
      }
      
      public static function appendText(param1:TextField, param2:String, param3:int = 0, param4:int = 0) : void
      {
         param1.embedFonts = true;
         param1.appendText(param2);
         txtFormat.indent = param4;
         txtFormat.letterSpacing = param3;
         param1.setTextFormat(txtFormat);
      }
      
      public static function setText(param1:TextField, param2:*, param3:int = 0, param4:int = 0) : void
      {
         param1.embedFonts = true;
         param1.text = param2 + "";
         txtFormat.indent = param4;
         txtFormat.letterSpacing = param3;
         param1.setTextFormat(txtFormat);
      }
      
      public static function setHtml(param1:TextField, param2:String, param3:int = 0, param4:int = 0) : void
      {
         param1.embedFonts = true;
         param1.htmlText = param2;
         txtFormat.indent = param4;
         txtFormat.letterSpacing = param3;
         param1.setTextFormat(txtFormat);
      }
      
      public static function initText(param1:TextField, param2:uint, param3:int, param4:int, param5:int, param6:String = "center", param7:int = 0, param8:int = 3, param9:uint = 0, param10:Boolean = true) : void
      {
         var _loc11_:TextFormat = new TextFormat(null,param3,param2);
         _loc11_.align = param6;
         _loc11_.leading = param7;
         param1.selectable = false;
         param1.width = param4;
         param1.height = param5;
         param1.defaultTextFormat = _loc11_;
         param1.multiline = true;
         param1.wordWrap = true;
         param1.mouseEnabled = false;
         if(param10)
         {
            param1.filters = [FilterUtil.getGlowFilter(param9,param8,param8,5)];
         }
      }
   }
}

