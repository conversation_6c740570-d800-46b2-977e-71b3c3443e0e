package com.mogames.utils
{
   import flash.text.TextField;
   
   public class TxtUtil
   {
      
      private static var chinaNum:Array = ["零","一","二","三","四","五","六","七","八","九","十","十一","十二","十三","十四","十五","十六","十七","十八","十九","二十","二十一","二十二"];
      
      public function TxtUtil()
      {
         super();
      }
      
      public static function setParam(param1:String, param2:String, ... rest) : String
      {
         var _loc4_:int = 0;
         var _loc5_:int = int(rest.length);
         while(_loc4_ < _loc5_)
         {
            param1 = param1.replace("@param" + _loc4_,TxtUtil.setColor(rest[_loc4_],param2));
            _loc4_++;
         }
         return param1;
      }
      
      public static function setColor(param1:*, param2:String = "ff0000") : String
      {
         return "<font color=" + "\"#" + param2 + "\">" + String(param1) + "</font>";
      }
      
      public static function formatMoney(param1:int) : String
      {
         var _loc2_:int = String(param1).length;
         if(_loc2_ < 5)
         {
            return param1 + "";
         }
         return int(param1 * 0.0001) + "万";
      }
      
      public static function initLinkWord(param1:*, param2:String) : String
      {
         var _loc3_:* = "\'" + "event:" + param2 + "\'";
         return "<a href=" + _loc3_ + ">" + param1 + "</a>";
      }
      
      public static function trim(param1:String) : String
      {
         return param1.replace(/([ 　]{1})/g,"");
      }
      
      public static function initTimeFormat(param1:int) : String
      {
         var _loc2_:int = int(param1 / 60);
         var _loc3_:int = int(param1 % 60);
         return (_loc2_ < 10 ? "0" + _loc2_ : _loc2_) + ":" + (_loc3_ < 10 ? "0" + _loc3_ : _loc3_);
      }
      
      public static function formatTime(param1:int) : String
      {
         var _loc2_:int = int(param1 / 60);
         var _loc3_:int = int(param1 % 60);
         return (_loc2_ < 10 ? "0" + _loc2_ : _loc2_) + "分" + (_loc3_ < 10 ? "0" + _loc3_ : _loc3_) + "秒";
      }
      
      public static function removeHtml(param1:String) : String
      {
         var _loc2_:TextField = new TextField();
         _loc2_.htmlText = param1;
         return _loc2_.text;
      }
      
      public static function convertStamp(param1:int) : String
      {
         var _loc2_:Date = new Date(param1 * 1000);
         return [_loc2_.fullYear,_loc2_.month + 1,_loc2_.date].join("-") + " " + [_loc2_.hours,_loc2_.minutes,_loc2_.seconds].join(":");
      }
      
      public static function convertChinaNum(param1:int) : String
      {
         return chinaNum[param1];
      }
      
      public static function numCharAt(param1:String, param2:int) : int
      {
         return int(param1.charAt(param2));
      }
      
      public static function addKuoHao(param1:String) : String
      {
         return "（" + param1 + "）";
      }
      
      public static function newCode(param1:int = 10) : String
      {
         var _loc2_:String = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
         var _loc3_:int = 52;
         var _loc4_:Array = _loc2_.split("");
         var _loc5_:String = "";
         var _loc6_:int = 0;
         var _loc7_:int = 0;
         while(_loc7_ < param1)
         {
            _loc6_ = int(Math.random() * _loc3_);
            _loc5_ += _loc4_[_loc6_];
            _loc4_.splice(_loc6_,1);
            _loc7_++;
         }
         return _loc5_;
      }
      
      public static function hash(param1:String, param2:String) : String
      {
         var _loc3_:String = "";
         var _loc4_:int = 0;
         var _loc5_:int = param1.length;
         while(_loc4_ < _loc5_)
         {
            _loc3_ += param2.charAt(int(param1.charAt(_loc4_)));
            _loc4_++;
         }
         return _loc3_;
      }
   }
}

