package file
{
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.zhuan.ZQZPVO;
   
   public class ZQZPConfig
   {
      
      private static var _instance:ZQZPConfig;
      
      public var buyReward:BaseRewardVO;
      
      public function ZQZPConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.buyReward = new BaseRewardVO(11401,1);
      }
      
      public static function instance() : ZQZPConfig
      {
         if(!_instance)
         {
            _instance = new ZQZPConfig();
         }
         return _instance;
      }
      
      public function newList() : Array
      {
         var _loc1_:Array = [];
         _loc1_[_loc1_.length] = new ZQZPVO(101,new BaseRewardVO(10303,6));
         _loc1_[_loc1_.length] = new ZQZPVO(102,new BaseRewardVO(18531,10));
         _loc1_[_loc1_.length] = new ZQZPVO(103,new BaseRewardVO(10982,1));
         _loc1_[_loc1_.length] = new ZQZPVO(204,new BaseRewardVO(10287,30));
         _loc1_[_loc1_.length] = new ZQZPVO(205,new BaseRewardVO(10288,30));
         _loc1_[_loc1_.length] = new ZQZPVO(206,new BaseRewardVO(10289,30));
         _loc1_[_loc1_.length] = new ZQZPVO(307,new BaseRewardVO(10290,40));
         _loc1_[_loc1_.length] = new ZQZPVO(308,new BaseRewardVO(10854,66));
         return _loc1_;
      }
   }
}

