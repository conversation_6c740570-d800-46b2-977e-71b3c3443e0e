package file
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class XPZZConfig
   {
      
      private static var _instance:XPZZConfig;
      
      public var waveData:WaveDataVO;
      
      public var burnTime:int;
      
      public var burnKeepTime:int;
      
      public var liangTime:int;
      
      public var yuanbings:Array;
      
      public function XPZZConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : XPZZConfig
      {
         if(!_instance)
         {
            _instance = new XPZZConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         var _loc1_:OneWaveVO = null;
         this.burnTime = 3;
         this.burnKeepTime = 18;
         this.liangTime = 8;
         this.yuanbings = [];
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(2,new RoleArgVO(209,25000,600,0,30,25,150,100,null));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(2,new RoleArgVO(210,25000,600,0,30,25,150,100,null));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(2,new RoleArgVO(211,25000,600,0,30,25,150,100,null));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(2,new RoleArgVO(212,25000,600,0,30,25,150,100,null));
         this.waveData = new WaveDataVO(0);
         this.waveData.limitBR = new WaveLimitVO(3000,1.2,1.3);
         this.waveData.zhuBoss = new BossArgVO(379,300000,700,50,50,50,150,150,new BossSkillData1(50,{
            "hurt":2050,
            "beiRate":300
         },10),0,10101,0);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(101,1000,100,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(102,1000,100,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(103,1000,100,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(104,1000,100,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(105,1000,100,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(106,1000,100,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(107,1000,100,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(108,1000,100,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(109,1000,100,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(110,1000,100,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(111,1000,100,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(112,1000,100,0,30,25,150,100,null)));
         this.waveData.addWave(_loc1_);
      }
   }
}

