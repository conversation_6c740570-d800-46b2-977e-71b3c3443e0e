package file
{
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.base.vo.EquipRewardVO;
   import mogames.gameData.boon.PayRewardVO;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameNet.MoneyProxy;
   
   public class PayRewardConfig
   {
      
      private static var _instance:PayRewardConfig;
      
      private var _rewards:Array;
      
      public function PayRewardConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : PayRewardConfig
      {
         if(!_instance)
         {
            _instance = new PayRewardConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._rewards = [];
         this._rewards[this._rewards.length] = new PayRewardVO(8011,100,[new BaseRewardVO(50031,30),new BaseRewardVO(10302,5),new BaseRewardVO(10981,1)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8012,350,[new BaseRewardVO(50040,30),new BaseRewardVO(10302,10),new BaseRewardVO(10309,10)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8013,750,[new BaseRewardVO(50019,30),new BaseRewardVO(10303,10),new BaseRewardVO(11602,50)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8014,1750,[new BaseRewardVO(50030,30),new BaseRewardVO(11007,50),new BaseRewardVO(10558,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8015,3250,[new BaseRewardVO(50044,30),new BaseRewardVO(11106,50),new BaseRewardVO(10550,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8016,5000,[new BaseRewardVO(50014,30),new BaseRewardVO(11153,50),new BaseRewardVO(10984,30)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8017,7000,[new BaseRewardVO(50047,30),new EquipRewardVO(42555,3),new BaseRewardVO(10915,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8018,10000,[new BaseRewardVO(50048,30),new EquipRewardVO(42564,3),new BaseRewardVO(18829,50)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8019,15000,[new BaseRewardVO(18553,5),new BaseRewardVO(18951,1),new BaseRewardVO(18880,1)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8046,17500,[new BaseRewardVO(10316,99),new BaseRewardVO(10316,99),new BaseRewardVO(10316,99)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8020,20000,[new BaseRewardVO(18553,5),new BaseRewardVO(18952,1),new BaseRewardVO(18828,1)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8049,22500,[new BaseRewardVO(18553,5),new BaseRewardVO(10281,30),new BaseRewardVO(10282,30)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8040,25000,[new BaseRewardVO(18553,5),new BaseRewardVO(10303,20),new BaseRewardVO(10857,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8050,27500,[new BaseRewardVO(18553,5),new BaseRewardVO(10283,30),new BaseRewardVO(10284,30)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8021,30000,[new BaseRewardVO(18553,10),new BaseRewardVO(18953,1),new BaseRewardVO(10863,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8051,32500,[new BaseRewardVO(18553,5),new BaseRewardVO(10285,30),new BaseRewardVO(10986,3)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8041,35000,[new BaseRewardVO(18553,10),new BaseRewardVO(10303,20),new BaseRewardVO(10858,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8056,37500,[new BaseRewardVO(10281,30),new BaseRewardVO(10282,30),new BaseRewardVO(10283,30)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8022,40000,[new BaseRewardVO(18553,10),new BaseRewardVO(18954,1),new BaseRewardVO(10861,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8057,42500,[new BaseRewardVO(10284,30),new BaseRewardVO(10285,30),new BaseRewardVO(10286,30)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8042,45000,[new BaseRewardVO(18553,10),new BaseRewardVO(10303,20),new BaseRewardVO(10859,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8023,50000,[new BaseRewardVO(18553,10),new BaseRewardVO(10916,10),new BaseRewardVO(10862,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8043,55000,[new BaseRewardVO(18553,10),new BaseRewardVO(10303,20),new BaseRewardVO(10860,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8024,60000,[new BaseRewardVO(18553,10),new BaseRewardVO(10916,10),new BaseRewardVO(10864,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8052,65000,[new BaseRewardVO(18553,10),new BaseRewardVO(10303,20),new BaseRewardVO(10791,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8025,70000,[new BaseRewardVO(10984,66),new BaseRewardVO(10552,20),new BaseRewardVO(10865,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8053,75000,[new BaseRewardVO(18553,10),new BaseRewardVO(10303,20),new BaseRewardVO(10792,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8026,80000,[new BaseRewardVO(18553,10),new BaseRewardVO(10916,10),new BaseRewardVO(10867,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8054,85000,[new BaseRewardVO(18553,10),new BaseRewardVO(10303,20),new BaseRewardVO(10793,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8027,90000,[new BaseRewardVO(18553,10),new BaseRewardVO(10916,10),new BaseRewardVO(10866,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8055,95000,[new BaseRewardVO(18553,10),new BaseRewardVO(10303,20),new BaseRewardVO(10794,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8028,100000,[new BaseRewardVO(18553,15),new BaseRewardVO(10975,10),new BaseRewardVO(10868,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8058,105000,[new BaseRewardVO(18553,10),new BaseRewardVO(10303,20),new BaseRewardVO(10466,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8029,110000,[new BaseRewardVO(18553,15),new BaseRewardVO(10975,10),new BaseRewardVO(10869,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8059,115000,[new BaseRewardVO(18553,10),new BaseRewardVO(10303,20),new BaseRewardVO(10467,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8030,120000,[new BaseRewardVO(18553,15),new BaseRewardVO(10975,10),new BaseRewardVO(10870,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8060,125000,[new BaseRewardVO(18553,10),new BaseRewardVO(10303,20),new BaseRewardVO(10468,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8031,130000,[new BaseRewardVO(18554,6),new BaseRewardVO(10975,10),new BaseRewardVO(10871,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8061,135000,[new BaseRewardVO(18553,10),new BaseRewardVO(10303,20),new BaseRewardVO(10469,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8032,140000,[new BaseRewardVO(18554,6),new BaseRewardVO(10969,10),new BaseRewardVO(10872,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8062,145000,[new BaseRewardVO(18554,6),new BaseRewardVO(10303,20),new BaseRewardVO(10796,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8033,150000,[new BaseRewardVO(18554,6),new BaseRewardVO(10969,10),new BaseRewardVO(10873,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8063,155000,[new BaseRewardVO(18554,6),new BaseRewardVO(10303,20),new BaseRewardVO(10797,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8034,160000,[new BaseRewardVO(18554,6),new BaseRewardVO(10969,10),new BaseRewardVO(10874,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8064,165000,[new BaseRewardVO(18554,6),new BaseRewardVO(10303,20),new BaseRewardVO(10798,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8035,170000,[new BaseRewardVO(18554,6),new BaseRewardVO(10969,10),new BaseRewardVO(10875,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8065,175000,[new BaseRewardVO(18554,6),new BaseRewardVO(10303,20),new BaseRewardVO(10799,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8036,180000,[new BaseRewardVO(18554,6),new BaseRewardVO(10970,10),new BaseRewardVO(10876,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8066,185000,[new BaseRewardVO(18554,6),new BaseRewardVO(10303,20),new BaseRewardVO(10671,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8037,190000,[new BaseRewardVO(18554,6),new BaseRewardVO(10970,10),new BaseRewardVO(10877,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8067,195000,[new BaseRewardVO(18554,6),new BaseRewardVO(10303,20),new BaseRewardVO(10672,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8038,200000,[new BaseRewardVO(18554,6),new BaseRewardVO(10970,10),new BaseRewardVO(10878,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8068,205000,[new BaseRewardVO(18554,6),new BaseRewardVO(10303,20),new BaseRewardVO(10673,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8039,210000,[new BaseRewardVO(18554,6),new BaseRewardVO(10970,10),new BaseRewardVO(10879,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8069,215000,[new BaseRewardVO(18554,6),new BaseRewardVO(10303,66),new BaseRewardVO(10674,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8044,220000,[new BaseRewardVO(18554,6),new BaseRewardVO(10950,10),new BaseRewardVO(10880,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8070,225000,[new BaseRewardVO(18554,6),new BaseRewardVO(10303,88),new BaseRewardVO(10675,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8045,230000,[new BaseRewardVO(18554,6),new BaseRewardVO(10950,10),new BaseRewardVO(10881,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8071,235000,[new BaseRewardVO(18554,6),new BaseRewardVO(10303,88),new BaseRewardVO(10676,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8047,240000,[new BaseRewardVO(18554,6),new BaseRewardVO(10950,10),new BaseRewardVO(10882,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8072,245000,[new BaseRewardVO(18554,6),new BaseRewardVO(10303,88),new BaseRewardVO(10677,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8048,250000,[new BaseRewardVO(18554,6),new BaseRewardVO(10950,10),new BaseRewardVO(10883,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8073,255000,[new BaseRewardVO(18554,6),new BaseRewardVO(10303,88),new BaseRewardVO(10678,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8074,260000,[new BaseRewardVO(18555,5),new BaseRewardVO(10949,10),new BaseRewardVO(10884,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8075,265000,[new BaseRewardVO(18555,5),new BaseRewardVO(10680,20),new BaseRewardVO(10885,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8076,270000,[new BaseRewardVO(18555,5),new BaseRewardVO(10949,10),new BaseRewardVO(10886,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8077,275000,[new BaseRewardVO(18555,5),new BaseRewardVO(10681,20),new BaseRewardVO(10887,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8078,280000,[new BaseRewardVO(18555,5),new BaseRewardVO(10949,10),new BaseRewardVO(13006,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8079,285000,[new BaseRewardVO(18555,5),new BaseRewardVO(10682,20),new BaseRewardVO(13007,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8080,290000,[new BaseRewardVO(18555,5),new BaseRewardVO(10949,10),new BaseRewardVO(13008,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8081,295000,[new BaseRewardVO(18555,5),new BaseRewardVO(10683,20),new BaseRewardVO(13009,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8082,300000,[new BaseRewardVO(18555,5),new BaseRewardVO(10949,10),new BaseRewardVO(13010,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8083,305000,[new BaseRewardVO(18555,5),new BaseRewardVO(13001,20),new BaseRewardVO(13011,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8084,310000,[new BaseRewardVO(18555,5),new BaseRewardVO(10949,10),new BaseRewardVO(13012,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8085,315000,[new BaseRewardVO(18555,5),new BaseRewardVO(13002,20),new BaseRewardVO(13013,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8086,320000,[new BaseRewardVO(18555,5),new BaseRewardVO(13003,20),new BaseRewardVO(13014,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8087,325000,[new BaseRewardVO(18555,5),new BaseRewardVO(13004,20),new BaseRewardVO(13015,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8088,330000,[new BaseRewardVO(18555,5),new BaseRewardVO(13021,20),new BaseRewardVO(13016,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8089,335000,[new BaseRewardVO(18555,5),new BaseRewardVO(13022,20),new BaseRewardVO(13017,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8090,340000,[new BaseRewardVO(18555,5),new BaseRewardVO(13023,20),new BaseRewardVO(13026,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8091,345000,[new BaseRewardVO(18555,5),new BaseRewardVO(13024,20),new BaseRewardVO(13027,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8092,350000,[new BaseRewardVO(18555,5),new BaseRewardVO(13031,20),new BaseRewardVO(13028,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8093,355000,[new BaseRewardVO(18555,5),new BaseRewardVO(13032,20),new BaseRewardVO(13029,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8094,360000,[new BaseRewardVO(18555,5),new BaseRewardVO(13033,20),new BaseRewardVO(13035,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8095,365000,[new BaseRewardVO(18555,5),new BaseRewardVO(13034,20),new BaseRewardVO(13036,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8096,370000,[new BaseRewardVO(18555,5),new BaseRewardVO(13041,20),new BaseRewardVO(13037,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8097,375000,[new BaseRewardVO(18555,5),new BaseRewardVO(13042,20),new BaseRewardVO(13038,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8098,380000,[new BaseRewardVO(18555,5),new BaseRewardVO(13043,20),new BaseRewardVO(13045,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8099,385000,[new BaseRewardVO(18555,5),new BaseRewardVO(13044,20),new BaseRewardVO(13046,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8100,390000,[new BaseRewardVO(18555,5),new BaseRewardVO(13051,20),new BaseRewardVO(13047,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8101,395000,[new BaseRewardVO(18555,5),new BaseRewardVO(13052,20),new BaseRewardVO(13048,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8102,400000,[new BaseRewardVO(18555,5),new BaseRewardVO(13053,20),new BaseRewardVO(13055,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8103,405000,[new BaseRewardVO(18555,5),new BaseRewardVO(13054,20),new BaseRewardVO(13056,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8104,410000,[new BaseRewardVO(18555,5),new BaseRewardVO(13061,20),new BaseRewardVO(13057,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8105,415000,[new BaseRewardVO(18555,5),new BaseRewardVO(13062,20),new BaseRewardVO(13058,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8106,420000,[new BaseRewardVO(18555,5),new BaseRewardVO(13063,20),new BaseRewardVO(13065,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8107,425000,[new BaseRewardVO(18555,5),new BaseRewardVO(13064,20),new BaseRewardVO(13066,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8108,430000,[new BaseRewardVO(18555,5),new BaseRewardVO(13071,20),new BaseRewardVO(13067,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8109,435000,[new BaseRewardVO(18555,5),new BaseRewardVO(13072,20),new BaseRewardVO(13068,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8110,440000,[new BaseRewardVO(18555,5),new BaseRewardVO(13073,20),new BaseRewardVO(13075,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8111,445000,[new BaseRewardVO(18555,5),new BaseRewardVO(13074,20),new BaseRewardVO(13076,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8112,450000,[new BaseRewardVO(18555,5),new BaseRewardVO(13081,20),new BaseRewardVO(13077,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8113,455000,[new BaseRewardVO(18555,5),new BaseRewardVO(13082,20),new BaseRewardVO(13078,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8114,460000,[new BaseRewardVO(18555,5),new BaseRewardVO(13083,20),new BaseRewardVO(13085,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8115,465000,[new BaseRewardVO(18555,5),new BaseRewardVO(13084,20),new BaseRewardVO(13086,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8116,470000,[new BaseRewardVO(18555,5),new BaseRewardVO(13091,20),new BaseRewardVO(13087,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8117,475000,[new BaseRewardVO(18555,5),new BaseRewardVO(13092,20),new BaseRewardVO(13088,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8118,480000,[new BaseRewardVO(18555,5),new BaseRewardVO(13093,20),new BaseRewardVO(13095,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8119,485000,[new BaseRewardVO(18555,5),new BaseRewardVO(13094,20),new BaseRewardVO(13096,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8120,490000,[new BaseRewardVO(18555,5),new BaseRewardVO(13101,20),new BaseRewardVO(13097,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8121,495000,[new BaseRewardVO(18555,5),new BaseRewardVO(13102,20),new BaseRewardVO(13098,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8122,500000,[new BaseRewardVO(18555,5),new BaseRewardVO(13103,20),new BaseRewardVO(13105,20)]);
         this._rewards[this._rewards.length] = new PayRewardVO(8123,505000,[new BaseRewardVO(18555,5),new BaseRewardVO(13104,20),new BaseRewardVO(13106,20)]);
      }
      
      public function get rewards() : Array
      {
         return this._rewards;
      }
      
      public function get tipGet() : Boolean
      {
         var _loc1_:PayRewardVO = null;
         for each(_loc1_ in this._rewards)
         {
            if(_loc1_.canGet && !_loc1_.hasGet)
            {
               return true;
            }
         }
         return false;
      }
      
      public function get firstGet() : Boolean
      {
         return MoneyProxy.instance().money >= 100 && !FlagProxy.instance().openFlag.isComplete(201);
      }
   }
}

