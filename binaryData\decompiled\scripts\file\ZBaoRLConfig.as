package file
{
   import mogames.gameData.base.func.ZBaoRLVO;
   import mogames.gameData.base.vo.NeedVO;
   import mogames.gameData.base.vo.ZhiBaoNeedVO;
   
   public class ZBaoRLConfig
   {
      
      private static var _instance:ZBaoRLConfig;
      
      private var _list:Array;
      
      public var stoneNeed:NeedVO;
      
      public function ZBaoRLConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : ZBaoRLConfig
      {
         if(!_instance)
         {
            _instance = new ZBaoRLConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.stoneNeed = new NeedVO(10985,1);
         this._list = [];
         this._list[this._list.length] = new ZBaoRLVO(32121,999999,9999,1000,[new ZhiBaoNeedVO(31521),new ZhiBaoNeedVO(31522),new ZhiBaoNeedVO(31523),new ZhiBaoNeedVO(31524)]);
         this._list[this._list.length] = new ZBaoRLVO(32111,999999,9999,1000,[new ZhiBaoNeedVO(31351),new ZhiBaoNeedVO(31352),new ZhiBaoNeedVO(31353),new ZhiBaoNeedVO(31354)]);
         this._list[this._list.length] = new ZBaoRLVO(32101,999999,9999,1000,[new ZhiBaoNeedVO(31242),new ZhiBaoNeedVO(31273),new ZhiBaoNeedVO(31276),new ZhiBaoNeedVO(31279)]);
         this._list[this._list.length] = new ZBaoRLVO(31194,999999,9999,1000,[new ZhiBaoNeedVO(31190),new ZhiBaoNeedVO(31191),new ZhiBaoNeedVO(31197),new ZhiBaoNeedVO(31199)]);
         this._list[this._list.length] = new ZBaoRLVO(31093,999999,9999,1000,[new ZhiBaoNeedVO(31132),new ZhiBaoNeedVO(31090),new ZhiBaoNeedVO(31091),new ZhiBaoNeedVO(31092)]);
         this._list[this._list.length] = new ZBaoRLVO(34001,999999,9999,1000,[new ZhiBaoNeedVO(31665),new ZhiBaoNeedVO(31666),new ZhiBaoNeedVO(31667),new ZhiBaoNeedVO(31668)]);
         this._list[this._list.length] = new ZBaoRLVO(31564,999999,9999,1000,[new ZhiBaoNeedVO(31655),new ZhiBaoNeedVO(31656),new ZhiBaoNeedVO(31657),new ZhiBaoNeedVO(31658)]);
         this._list[this._list.length] = new ZBaoRLVO(31563,999999,9999,1000,[new ZhiBaoNeedVO(31645),new ZhiBaoNeedVO(31646),new ZhiBaoNeedVO(31647),new ZhiBaoNeedVO(31648)]);
         this._list[this._list.length] = new ZBaoRLVO(31562,999999,9999,1000,[new ZhiBaoNeedVO(31635),new ZhiBaoNeedVO(31636),new ZhiBaoNeedVO(31637),new ZhiBaoNeedVO(31638)]);
         this._list[this._list.length] = new ZBaoRLVO(31561,999999,9999,1000,[new ZhiBaoNeedVO(31625),new ZhiBaoNeedVO(31626),new ZhiBaoNeedVO(31627),new ZhiBaoNeedVO(31628)]);
         this._list[this._list.length] = new ZBaoRLVO(31807,888888,5888,1000,[new ZhiBaoNeedVO(31661),new ZhiBaoNeedVO(31662),new ZhiBaoNeedVO(31663),new ZhiBaoNeedVO(31664)]);
         this._list[this._list.length] = new ZBaoRLVO(31806,888888,5888,1000,[new ZhiBaoNeedVO(31651),new ZhiBaoNeedVO(31652),new ZhiBaoNeedVO(31653),new ZhiBaoNeedVO(31654)]);
         this._list[this._list.length] = new ZBaoRLVO(31805,888888,5888,1000,[new ZhiBaoNeedVO(31641),new ZhiBaoNeedVO(31642),new ZhiBaoNeedVO(31643),new ZhiBaoNeedVO(31644)]);
         this._list[this._list.length] = new ZBaoRLVO(31804,888888,5888,1000,[new ZhiBaoNeedVO(31631),new ZhiBaoNeedVO(31632),new ZhiBaoNeedVO(31633),new ZhiBaoNeedVO(31634)]);
         this._list[this._list.length] = new ZBaoRLVO(31803,888888,5888,1000,[new ZhiBaoNeedVO(31621),new ZhiBaoNeedVO(31622),new ZhiBaoNeedVO(31623),new ZhiBaoNeedVO(31624)]);
         this._list[this._list.length] = new ZBaoRLVO(31802,888888,5888,1000,[new ZhiBaoNeedVO(31611),new ZhiBaoNeedVO(31612),new ZhiBaoNeedVO(31613),new ZhiBaoNeedVO(31614)]);
         this._list[this._list.length] = new ZBaoRLVO(31801,888888,5888,1000,[new ZhiBaoNeedVO(31601),new ZhiBaoNeedVO(31602),new ZhiBaoNeedVO(31603),new ZhiBaoNeedVO(31604)]);
         this._list[this._list.length] = new ZBaoRLVO(31534,999999,9999,1000,[new ZhiBaoNeedVO(31615),new ZhiBaoNeedVO(31616),new ZhiBaoNeedVO(31617),new ZhiBaoNeedVO(31618)]);
         this._list[this._list.length] = new ZBaoRLVO(31533,999999,9999,1000,[new ZhiBaoNeedVO(31605),new ZhiBaoNeedVO(31606),new ZhiBaoNeedVO(31607),new ZhiBaoNeedVO(31608)]);
         this._list[this._list.length] = new ZBaoRLVO(31532,999999,9999,1000,[new ZhiBaoNeedVO(31495),new ZhiBaoNeedVO(31496),new ZhiBaoNeedVO(31497),new ZhiBaoNeedVO(31498)]);
         this._list[this._list.length] = new ZBaoRLVO(31531,999999,9999,1000,[new ZhiBaoNeedVO(31485),new ZhiBaoNeedVO(31486),new ZhiBaoNeedVO(31487),new ZhiBaoNeedVO(31488)]);
         this._list[this._list.length] = new ZBaoRLVO(31504,999999,9999,1000,[new ZhiBaoNeedVO(31475),new ZhiBaoNeedVO(31476),new ZhiBaoNeedVO(31477),new ZhiBaoNeedVO(31478)]);
         this._list[this._list.length] = new ZBaoRLVO(31503,999999,9999,1000,[new ZhiBaoNeedVO(31465),new ZhiBaoNeedVO(31466),new ZhiBaoNeedVO(31467),new ZhiBaoNeedVO(31468)]);
         this._list[this._list.length] = new ZBaoRLVO(31502,999999,9999,1000,[new ZhiBaoNeedVO(31455),new ZhiBaoNeedVO(31456),new ZhiBaoNeedVO(31457),new ZhiBaoNeedVO(31458)]);
         this._list[this._list.length] = new ZBaoRLVO(31501,999999,9999,1000,[new ZhiBaoNeedVO(31395),new ZhiBaoNeedVO(31396),new ZhiBaoNeedVO(31397),new ZhiBaoNeedVO(31398)]);
         this._list[this._list.length] = new ZBaoRLVO(31334,999999,9999,1000,[new ZhiBaoNeedVO(31385),new ZhiBaoNeedVO(31386),new ZhiBaoNeedVO(31387),new ZhiBaoNeedVO(31388)]);
         this._list[this._list.length] = new ZBaoRLVO(31333,999999,9999,1000,[new ZhiBaoNeedVO(31375),new ZhiBaoNeedVO(31376),new ZhiBaoNeedVO(31377),new ZhiBaoNeedVO(31378)]);
         this._list[this._list.length] = new ZBaoRLVO(31332,999999,9999,1000,[new ZhiBaoNeedVO(31365),new ZhiBaoNeedVO(31366),new ZhiBaoNeedVO(31367),new ZhiBaoNeedVO(31368)]);
         this._list[this._list.length] = new ZBaoRLVO(31331,999999,9999,1000,[new ZhiBaoNeedVO(31296),new ZhiBaoNeedVO(31297),new ZhiBaoNeedVO(31298),new ZhiBaoNeedVO(31299)]);
         this._list[this._list.length] = new ZBaoRLVO(31277,999999,9999,1000,[new ZhiBaoNeedVO(31286),new ZhiBaoNeedVO(31287),new ZhiBaoNeedVO(31288),new ZhiBaoNeedVO(31289)]);
         this._list[this._list.length] = new ZBaoRLVO(31274,999999,9999,1000,[new ZhiBaoNeedVO(31266),new ZhiBaoNeedVO(31267),new ZhiBaoNeedVO(31268),new ZhiBaoNeedVO(31269)]);
         this._list[this._list.length] = new ZBaoRLVO(31271,999999,9999,1000,[new ZhiBaoNeedVO(31256),new ZhiBaoNeedVO(31257),new ZhiBaoNeedVO(31258),new ZhiBaoNeedVO(31259)]);
         this._list[this._list.length] = new ZBaoRLVO(32002,999999,9999,1000,[new ZhiBaoNeedVO(31321),new ZhiBaoNeedVO(31322),new ZhiBaoNeedVO(31323),new ZhiBaoNeedVO(31324)]);
         this._list[this._list.length] = new ZBaoRLVO(32001,999999,9999,1000,[new ZhiBaoNeedVO(31182),new ZhiBaoNeedVO(31183),new ZhiBaoNeedVO(31186),new ZhiBaoNeedVO(31187)]);
         this._list[this._list.length] = new ZBaoRLVO(31007,999999,9999,1000,[new ZhiBaoNeedVO(30084),new ZhiBaoNeedVO(30089),new ZhiBaoNeedVO(31005),new ZhiBaoNeedVO(31006)]);
         this._list[this._list.length] = new ZBaoRLVO(31240,999999,9999,1000,[new ZhiBaoNeedVO(31235),new ZhiBaoNeedVO(31236),new ZhiBaoNeedVO(31237),new ZhiBaoNeedVO(31238)]);
         this._list[this._list.length] = new ZBaoRLVO(31193,999999,9999,1000,[new ZhiBaoNeedVO(31225),new ZhiBaoNeedVO(31226),new ZhiBaoNeedVO(31227),new ZhiBaoNeedVO(31228)]);
         this._list[this._list.length] = new ZBaoRLVO(31192,999999,9999,1000,[new ZhiBaoNeedVO(31215),new ZhiBaoNeedVO(31216),new ZhiBaoNeedVO(31217),new ZhiBaoNeedVO(31218)]);
         this._list[this._list.length] = new ZBaoRLVO(31099,999999,9999,1000,[new ZhiBaoNeedVO(30367),new ZhiBaoNeedVO(30370),new ZhiBaoNeedVO(31098),new ZhiBaoNeedVO(31097)]);
         this._list[this._list.length] = new ZBaoRLVO(31191,999999,9999,1000,[new ZhiBaoNeedVO(31211),new ZhiBaoNeedVO(31212),new ZhiBaoNeedVO(31213),new ZhiBaoNeedVO(31214)]);
         this._list[this._list.length] = new ZBaoRLVO(31190,999999,9999,1000,[new ZhiBaoNeedVO(31076),new ZhiBaoNeedVO(31077),new ZhiBaoNeedVO(31078),new ZhiBaoNeedVO(31079)]);
         this._list[this._list.length] = new ZBaoRLVO(31092,999999,9999,1000,[new ZhiBaoNeedVO(31071),new ZhiBaoNeedVO(31072),new ZhiBaoNeedVO(31073),new ZhiBaoNeedVO(31074)]);
         this._list[this._list.length] = new ZBaoRLVO(31091,999999,9999,1000,[new ZhiBaoNeedVO(31045),new ZhiBaoNeedVO(31046),new ZhiBaoNeedVO(31047),new ZhiBaoNeedVO(31048)]);
         this._list[this._list.length] = new ZBaoRLVO(31030,999999,9999,1000,[new ZhiBaoNeedVO(31026),new ZhiBaoNeedVO(31027),new ZhiBaoNeedVO(31028),new ZhiBaoNeedVO(31029)]);
         this._list[this._list.length] = new ZBaoRLVO(31090,999999,9999,1000,[new ZhiBaoNeedVO(31041),new ZhiBaoNeedVO(31042),new ZhiBaoNeedVO(31043),new ZhiBaoNeedVO(31044)]);
         this._list[this._list.length] = new ZBaoRLVO(31704,888888,5888,1000,[new ZhiBaoNeedVO(31491),new ZhiBaoNeedVO(31492),new ZhiBaoNeedVO(31493),new ZhiBaoNeedVO(31494)]);
         this._list[this._list.length] = new ZBaoRLVO(31703,888888,5888,1000,[new ZhiBaoNeedVO(31481),new ZhiBaoNeedVO(31482),new ZhiBaoNeedVO(31483),new ZhiBaoNeedVO(31484)]);
         this._list[this._list.length] = new ZBaoRLVO(31702,888888,5888,1000,[new ZhiBaoNeedVO(31471),new ZhiBaoNeedVO(31472),new ZhiBaoNeedVO(31473),new ZhiBaoNeedVO(31474)]);
         this._list[this._list.length] = new ZBaoRLVO(31701,888888,5888,1000,[new ZhiBaoNeedVO(31461),new ZhiBaoNeedVO(31462),new ZhiBaoNeedVO(31463),new ZhiBaoNeedVO(31464)]);
         this._list[this._list.length] = new ZBaoRLVO(31404,888888,5888,1000,[new ZhiBaoNeedVO(31451),new ZhiBaoNeedVO(31452),new ZhiBaoNeedVO(31453),new ZhiBaoNeedVO(31454)]);
         this._list[this._list.length] = new ZBaoRLVO(31403,888888,5888,1000,[new ZhiBaoNeedVO(31391),new ZhiBaoNeedVO(31392),new ZhiBaoNeedVO(31393),new ZhiBaoNeedVO(31394)]);
         this._list[this._list.length] = new ZBaoRLVO(31402,888888,5888,1000,[new ZhiBaoNeedVO(31381),new ZhiBaoNeedVO(31382),new ZhiBaoNeedVO(31383),new ZhiBaoNeedVO(31384)]);
         this._list[this._list.length] = new ZBaoRLVO(31401,888888,5888,1000,[new ZhiBaoNeedVO(31371),new ZhiBaoNeedVO(31372),new ZhiBaoNeedVO(31373),new ZhiBaoNeedVO(31374)]);
         this._list[this._list.length] = new ZBaoRLVO(31304,888888,5888,1000,[new ZhiBaoNeedVO(31361),new ZhiBaoNeedVO(31362),new ZhiBaoNeedVO(31363),new ZhiBaoNeedVO(31364)]);
         this._list[this._list.length] = new ZBaoRLVO(31303,888888,5888,1000,[new ZhiBaoNeedVO(31291),new ZhiBaoNeedVO(31292),new ZhiBaoNeedVO(31293),new ZhiBaoNeedVO(31294)]);
         this._list[this._list.length] = new ZBaoRLVO(31302,888888,5888,1000,[new ZhiBaoNeedVO(31281),new ZhiBaoNeedVO(31282),new ZhiBaoNeedVO(31283),new ZhiBaoNeedVO(31284)]);
         this._list[this._list.length] = new ZBaoRLVO(31301,888888,5888,1000,[new ZhiBaoNeedVO(31261),new ZhiBaoNeedVO(31262),new ZhiBaoNeedVO(31263),new ZhiBaoNeedVO(31264)]);
         this._list[this._list.length] = new ZBaoRLVO(30376,999999,9999,100,[new ZhiBaoNeedVO(30375),new ZhiBaoNeedVO(30369),new ZhiBaoNeedVO(30368),new ZhiBaoNeedVO(30353)]);
         this._list[this._list.length] = new ZBaoRLVO(31006,999999,9999,1000,[new ZhiBaoNeedVO(31121),new ZhiBaoNeedVO(31122),new ZhiBaoNeedVO(31123),new ZhiBaoNeedVO(31124)]);
         this._list[this._list.length] = new ZBaoRLVO(31005,999999,9999,1000,[new ZhiBaoNeedVO(31101),new ZhiBaoNeedVO(31102),new ZhiBaoNeedVO(31103),new ZhiBaoNeedVO(31104)]);
         this._list[this._list.length] = new ZBaoRLVO(31287,999999,6666,1000,[new NeedVO(13106,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31286,999999,6666,1000,[new NeedVO(13105,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31269,999999,6666,1000,[new NeedVO(13098,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31268,999999,6666,1000,[new NeedVO(13097,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31267,999999,6666,1000,[new NeedVO(13096,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31266,999999,6666,1000,[new NeedVO(13095,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31259,999999,6666,1000,[new NeedVO(13088,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31258,999999,6666,1000,[new NeedVO(13087,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31257,999999,6666,1000,[new NeedVO(13086,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31256,999999,6666,1000,[new NeedVO(13085,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31238,999999,6666,1000,[new NeedVO(13078,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31237,999999,6666,1000,[new NeedVO(13077,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31236,999999,6666,1000,[new NeedVO(13076,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31235,999999,6666,1000,[new NeedVO(13075,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31094,999999,9999,1000,[new NeedVO(10950,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30089,999999,9999,1000,[new ZhiBaoNeedVO(30385),new ZhiBaoNeedVO(30386),new ZhiBaoNeedVO(30387),new ZhiBaoNeedVO(30388)]);
         this._list[this._list.length] = new ZBaoRLVO(31025,999999,9999,1000,[new ZhiBaoNeedVO(31055),new ZhiBaoNeedVO(31060),new ZhiBaoNeedVO(31180),new ZhiBaoNeedVO(31181)]);
         this._list[this._list.length] = new ZBaoRLVO(31020,999999,9999,1000,[new ZhiBaoNeedVO(31014),new ZhiBaoNeedVO(31019),new ZhiBaoNeedVO(31035),new ZhiBaoNeedVO(31040)]);
         this._list[this._list.length] = new ZBaoRLVO(30084,999999,9999,1000,[new ZhiBaoNeedVO(30394),new ZhiBaoNeedVO(30395),new ZhiBaoNeedVO(30396),new ZhiBaoNeedVO(30397)]);
         this._list[this._list.length] = new ZBaoRLVO(30083,999999,9999,1000,[new ZhiBaoNeedVO(30390),new ZhiBaoNeedVO(30391),new ZhiBaoNeedVO(30392),new ZhiBaoNeedVO(30393)]);
         this._list[this._list.length] = new ZBaoRLVO(31096,999999,5000,1000,[new NeedVO(10970,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30375,999999,5000,100,[new NeedVO(10940,40),new NeedVO(10833,30),new ZhiBaoNeedVO(30363),new ZhiBaoNeedVO(30472)]);
         this._list[this._list.length] = new ZBaoRLVO(30082,999999,9999,1000,[new ZhiBaoNeedVO(30075),new ZhiBaoNeedVO(30076),new ZhiBaoNeedVO(30077),new ZhiBaoNeedVO(30078)]);
         this._list[this._list.length] = new ZBaoRLVO(30098,999999,5000,1000,[new NeedVO(10969,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30070,999999,5000,1000,[new NeedVO(10975,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31185,888888,5888,1000,[new ZhiBaoNeedVO(31251),new ZhiBaoNeedVO(31252),new ZhiBaoNeedVO(31253),new ZhiBaoNeedVO(31254)]);
         this._list[this._list.length] = new ZBaoRLVO(31184,888888,5888,1000,[new ZhiBaoNeedVO(31231),new ZhiBaoNeedVO(31232),new ZhiBaoNeedVO(31233),new ZhiBaoNeedVO(31234)]);
         this._list[this._list.length] = new ZBaoRLVO(31183,888888,5888,1000,[new ZhiBaoNeedVO(31221),new ZhiBaoNeedVO(31222),new ZhiBaoNeedVO(31223),new ZhiBaoNeedVO(31224)]);
         this._list[this._list.length] = new ZBaoRLVO(31182,888888,5888,1000,[new ZhiBaoNeedVO(31205),new ZhiBaoNeedVO(31206),new ZhiBaoNeedVO(31207),new ZhiBaoNeedVO(31208)]);
         this._list[this._list.length] = new ZBaoRLVO(31181,888888,5888,1000,[new ZhiBaoNeedVO(31201),new ZhiBaoNeedVO(31202),new ZhiBaoNeedVO(31203),new ZhiBaoNeedVO(31204)]);
         this._list[this._list.length] = new ZBaoRLVO(31180,888888,5888,1000,[new ZhiBaoNeedVO(31066),new ZhiBaoNeedVO(31067),new ZhiBaoNeedVO(31068),new ZhiBaoNeedVO(31069)]);
         this._list[this._list.length] = new ZBaoRLVO(31060,888888,5888,1000,[new ZhiBaoNeedVO(31056),new ZhiBaoNeedVO(31057),new ZhiBaoNeedVO(31058),new ZhiBaoNeedVO(31059)]);
         this._list[this._list.length] = new ZBaoRLVO(31055,777777,4888,1000,[new ZhiBaoNeedVO(31051),new ZhiBaoNeedVO(31052),new ZhiBaoNeedVO(31053),new ZhiBaoNeedVO(31054)]);
         this._list[this._list.length] = new ZBaoRLVO(31374,999999,6666,1000,[new NeedVO(13104,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31373,999999,6666,1000,[new NeedVO(13103,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31372,999999,6666,1000,[new NeedVO(13102,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31371,999999,6666,1000,[new NeedVO(13101,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31364,999999,6666,1000,[new NeedVO(13094,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31363,999999,6666,1000,[new NeedVO(13093,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31362,999999,6666,1000,[new NeedVO(13092,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31361,999999,6666,1000,[new NeedVO(13091,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31294,999999,6666,1000,[new NeedVO(13084,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31293,999999,6666,1000,[new NeedVO(13083,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31292,999999,6666,1000,[new NeedVO(13082,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31291,999999,6666,1000,[new NeedVO(13081,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31284,999999,6666,1000,[new NeedVO(13074,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31283,999999,6666,1000,[new NeedVO(13073,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31282,999999,6666,1000,[new NeedVO(13072,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31281,999999,6666,1000,[new NeedVO(13071,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31264,999999,6666,1000,[new NeedVO(13064,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31263,999999,6666,1000,[new NeedVO(13063,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31262,999999,6666,1000,[new NeedVO(13062,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31261,999999,6666,1000,[new NeedVO(13061,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31254,999999,6666,1000,[new NeedVO(13054,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31253,999999,6666,1000,[new NeedVO(13053,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31252,999999,6666,1000,[new NeedVO(13052,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31251,999999,6666,1000,[new NeedVO(13051,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31228,999999,6666,1000,[new NeedVO(13068,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31227,999999,6666,1000,[new NeedVO(13067,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31226,999999,6666,1000,[new NeedVO(13066,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31225,999999,6666,1000,[new NeedVO(13065,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31218,999999,6666,1000,[new NeedVO(13058,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31217,999999,6666,1000,[new NeedVO(13057,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31216,999999,6666,1000,[new NeedVO(13056,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31215,999999,6666,1000,[new NeedVO(13055,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31214,999999,6666,1000,[new NeedVO(13048,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31213,999999,6666,1000,[new NeedVO(13047,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31212,999999,6666,1000,[new NeedVO(13046,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31211,999999,6666,1000,[new NeedVO(13045,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31079,999999,6666,1000,[new NeedVO(13038,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31078,999999,6666,1000,[new NeedVO(13037,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31077,999999,6666,1000,[new NeedVO(13036,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31076,999999,6666,1000,[new NeedVO(13035,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31029,999999,6666,1000,[new NeedVO(13029,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31028,999999,6666,1000,[new NeedVO(13028,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31027,999999,6666,1000,[new NeedVO(13027,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31026,999999,6666,1000,[new NeedVO(13026,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31074,999999,6666,1000,[new NeedVO(13017,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31073,999999,6666,1000,[new NeedVO(13016,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31072,999999,6666,1000,[new NeedVO(13015,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31071,999999,6666,1000,[new NeedVO(13014,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31048,999999,6666,1000,[new NeedVO(13013,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31047,999999,6666,1000,[new NeedVO(13012,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31046,999999,6666,1000,[new NeedVO(13011,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31045,999999,6666,1000,[new NeedVO(13010,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30249,999999,6666,250,[new ZhiBaoNeedVO(31165),new ZhiBaoNeedVO(31166),new ZhiBaoNeedVO(31167),new ZhiBaoNeedVO(31168)]);
         this._list[this._list.length] = new ZBaoRLVO(31009,666666,5888,500,[new ZhiBaoNeedVO(30079),new ZhiBaoNeedVO(30099),new ZhiBaoNeedVO(30368),new ZhiBaoNeedVO(30058)]);
         this._list[this._list.length] = new ZBaoRLVO(31040,666666,3888,1000,[new ZhiBaoNeedVO(31036),new ZhiBaoNeedVO(31037),new ZhiBaoNeedVO(31038),new ZhiBaoNeedVO(31039)]);
         this._list[this._list.length] = new ZBaoRLVO(31035,666666,3888,1000,[new ZhiBaoNeedVO(31031),new ZhiBaoNeedVO(31032),new ZhiBaoNeedVO(31033),new ZhiBaoNeedVO(31034)]);
         this._list[this._list.length] = new ZBaoRLVO(31019,666666,2888,1000,[new ZhiBaoNeedVO(31015),new ZhiBaoNeedVO(31016),new ZhiBaoNeedVO(31017),new ZhiBaoNeedVO(31018)]);
         this._list[this._list.length] = new ZBaoRLVO(31014,666666,2888,1000,[new ZhiBaoNeedVO(31010),new ZhiBaoNeedVO(31011),new ZhiBaoNeedVO(31012),new ZhiBaoNeedVO(31013)]);
         this._list[this._list.length] = new ZBaoRLVO(31224,999999,6666,1000,[new NeedVO(13034,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31223,999999,6666,1000,[new NeedVO(13033,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31222,999999,6666,1000,[new NeedVO(13032,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31221,999999,6666,1000,[new NeedVO(13031,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31234,999999,6666,1000,[new NeedVO(13044,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31233,999999,6666,1000,[new NeedVO(13043,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31232,999999,6666,1000,[new NeedVO(13042,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31231,999999,6666,1000,[new NeedVO(13041,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31208,999999,6666,1000,[new NeedVO(13024,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31207,999999,6666,1000,[new NeedVO(13023,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31206,999999,6666,1000,[new NeedVO(13022,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31205,999999,6666,1000,[new NeedVO(13021,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31044,999999,6666,1000,[new NeedVO(13009,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31043,999999,6666,1000,[new NeedVO(13008,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31042,999999,6666,1000,[new NeedVO(13007,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31041,999999,6666,1000,[new NeedVO(13006,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31204,999999,6666,1000,[new NeedVO(13004,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31203,999999,6666,1000,[new NeedVO(13003,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31202,999999,6666,1000,[new NeedVO(13002,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31201,999999,6666,1000,[new NeedVO(13001,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30081,999999,8888,1000,[new ZhiBaoNeedVO(30063),new ZhiBaoNeedVO(30065),new ZhiBaoNeedVO(30066),new ZhiBaoNeedVO(30031)]);
         this._list[this._list.length] = new ZBaoRLVO(30080,999999,6666,1000,[new ZhiBaoNeedVO(30058),new ZhiBaoNeedVO(30059),new ZhiBaoNeedVO(30060),new ZhiBaoNeedVO(30061)]);
         this._list[this._list.length] = new ZBaoRLVO(30069,999999,5000,1000,[new NeedVO(10930,20),new NeedVO(10833,20),new ZhiBaoNeedVO(30471),new ZhiBaoNeedVO(30464)]);
         this._list[this._list.length] = new ZBaoRLVO(30067,999999,5000,1000,[new NeedVO(10916,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30068,999999,5000,1000,[new NeedVO(10914,20),new NeedVO(10833,20),new ZhiBaoNeedVO(30055),new ZhiBaoNeedVO(30214)]);
         this._list[this._list.length] = new ZBaoRLVO(30099,999999,6000,250,[new ZhiBaoNeedVO(30242),new ZhiBaoNeedVO(30243),new ZhiBaoNeedVO(30244),new ZhiBaoNeedVO(30248)]);
         this._list[this._list.length] = new ZBaoRLVO(30079,999999,5000,300,[new ZhiBaoNeedVO(30238),new ZhiBaoNeedVO(30239),new ZhiBaoNeedVO(30240),new ZhiBaoNeedVO(30241)]);
         this._list[this._list.length] = new ZBaoRLVO(31024,588888,8888,1000,[new NeedVO(10887,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31023,588888,8888,1000,[new NeedVO(10886,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31022,588888,8888,1000,[new NeedVO(10885,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31021,588888,8888,1000,[new NeedVO(10884,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31069,588888,8888,1000,[new NeedVO(10683,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31068,588888,8888,1000,[new NeedVO(10682,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31067,588888,8888,1000,[new NeedVO(10681,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31066,588888,8888,1000,[new NeedVO(10680,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31059,388888,8888,1000,[new NeedVO(10678,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31058,388888,8888,1000,[new NeedVO(10677,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31057,488888,8888,1000,[new NeedVO(10676,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31056,488888,8888,1000,[new NeedVO(10675,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31054,388888,8888,1000,[new NeedVO(10674,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31053,388888,8888,1000,[new NeedVO(10673,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31052,388888,8888,1000,[new NeedVO(10672,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31051,388888,8888,1000,[new NeedVO(10671,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31039,388888,8888,1000,[new NeedVO(10799,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31038,388888,8888,1000,[new NeedVO(10798,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31037,388888,8888,1000,[new NeedVO(10797,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31036,388888,8888,1000,[new NeedVO(10796,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31034,388888,8888,1000,[new NeedVO(10469,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31033,388888,8888,1000,[new NeedVO(10468,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31032,388888,8888,1000,[new NeedVO(10467,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31031,388888,8888,1000,[new NeedVO(10466,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31018,388888,8888,1000,[new NeedVO(10794,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31017,388888,8888,1000,[new NeedVO(10793,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31016,388888,8888,1000,[new NeedVO(10792,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31015,388888,8888,1000,[new NeedVO(10791,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31004,388888,7888,1000,[new NeedVO(10883,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31003,388888,7888,1000,[new NeedVO(10882,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31002,388888,7888,1000,[new NeedVO(10881,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31001,388888,7888,1000,[new NeedVO(10880,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30088,388888,6888,1000,[new NeedVO(10879,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30087,388888,6888,1000,[new NeedVO(10878,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30086,388888,6888,1000,[new NeedVO(10877,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30085,388888,6888,1000,[new NeedVO(10876,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30097,388888,5888,1000,[new NeedVO(10875,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30096,388888,5888,1000,[new NeedVO(10874,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30095,388888,5888,1000,[new NeedVO(10873,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30094,388888,5888,1000,[new NeedVO(10872,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30093,188888,4888,1000,[new NeedVO(10871,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30092,188888,4888,1000,[new NeedVO(10870,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30091,188888,4888,1000,[new NeedVO(10869,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30090,188888,4888,1000,[new NeedVO(10868,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30077,188888,2888,1000,[new NeedVO(10866,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30076,188888,2888,1000,[new NeedVO(10865,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30075,188888,2888,1000,[new NeedVO(10867,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30078,188888,2888,1000,[new NeedVO(10864,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30065,88888,888,1000,[new NeedVO(10863,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30031,88888,888,1000,[new NeedVO(10861,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30066,88888,888,1000,[new NeedVO(10862,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30062,88888,888,1000,[new NeedVO(10658,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30056,888888,4000,1000,[new ZhiBaoNeedVO(30203),new ZhiBaoNeedVO(30202),new ZhiBaoNeedVO(30204),new ZhiBaoNeedVO(30217)]);
         this._list[this._list.length] = new ZBaoRLVO(30054,88888,888,1000,[new NeedVO(10659,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30100,666666,8000,200,[new ZhiBaoNeedVO(30357),new ZhiBaoNeedVO(30237),new ZhiBaoNeedVO(30236),new NeedVO(10832,50)]);
         this._list[this._list.length] = new ZBaoRLVO(30058,88888,5000,300,[new ZhiBaoNeedVO(30357),new ZhiBaoNeedVO(30211),new ZhiBaoNeedVO(30025),new NeedVO(10589,5)]);
         this._list[this._list.length] = new ZBaoRLVO(30061,88888,5000,300,[new ZhiBaoNeedVO(30236),new ZhiBaoNeedVO(30228),new ZhiBaoNeedVO(30237),new ZhiBaoNeedVO(30224)]);
         this._list[this._list.length] = new ZBaoRLVO(30059,88888,5000,300,[new ZhiBaoNeedVO(30451),new ZhiBaoNeedVO(30471),new ZhiBaoNeedVO(30072),new ZhiBaoNeedVO(30030)]);
         this._list[this._list.length] = new ZBaoRLVO(30057,888888,5000,300,[new ZhiBaoNeedVO(30218),new ZhiBaoNeedVO(30205),new ZhiBaoNeedVO(30213),new ZhiBaoNeedVO(30051)]);
         this._list[this._list.length] = new ZBaoRLVO(30060,888888,4000,300,[new ZhiBaoNeedVO(30052),new ZhiBaoNeedVO(30055),new ZhiBaoNeedVO(30072),new ZhiBaoNeedVO(30014)]);
         this._list[this._list.length] = new ZBaoRLVO(30053,999999,6000,1000,[new NeedVO(10575,20),new ZhiBaoNeedVO(30212),new ZhiBaoNeedVO(30216),new ZhiBaoNeedVO(30013)]);
         this._list[this._list.length] = new ZBaoRLVO(30063,888888,4000,300,[new ZhiBaoNeedVO(30128),new ZhiBaoNeedVO(30030),new ZhiBaoNeedVO(30064)]);
         this._list[this._list.length] = new ZBaoRLVO(31064,777777,5000,200,[new NeedVO(10891,20),new NeedVO(10892,20),new NeedVO(10893,20),new NeedVO(10894,6)]);
         this._list[this._list.length] = new ZBaoRLVO(31063,777777,5000,200,[new NeedVO(10845,20),new NeedVO(10846,20),new NeedVO(10847,20),new NeedVO(10848,6)]);
         this._list[this._list.length] = new ZBaoRLVO(31062,777777,5000,200,[new NeedVO(10841,20),new NeedVO(10842,20),new NeedVO(10843,20),new NeedVO(10844,6)]);
         this._list[this._list.length] = new ZBaoRLVO(31061,777777,5000,200,[new NeedVO(10994,20),new NeedVO(10995,20),new NeedVO(10996,20),new NeedVO(10997,6)]);
         this._list[this._list.length] = new ZBaoRLVO(30049,777777,5000,100,[new ZhiBaoNeedVO(30047),new ZhiBaoNeedVO(30232),new ZhiBaoNeedVO(30233),new NeedVO(10832,10)]);
         this._list[this._list.length] = new ZBaoRLVO(31010,666666,5000,1000,[new NeedVO(10857,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31011,666666,5000,1000,[new NeedVO(10858,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31012,666666,5000,1000,[new NeedVO(10859,20)]);
         this._list[this._list.length] = new ZBaoRLVO(31013,666666,5000,1000,[new NeedVO(10860,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30048,666666,4000,200,[new NeedVO(10990,20),new NeedVO(10991,20),new NeedVO(10992,20),new NeedVO(10993,6)]);
         this._list[this._list.length] = new ZBaoRLVO(30044,666666,4000,200,[new NeedVO(10976,20),new NeedVO(10977,20),new NeedVO(10978,20),new NeedVO(10979,6)]);
         this._list[this._list.length] = new ZBaoRLVO(30043,666666,4000,200,[new NeedVO(10971,20),new NeedVO(10972,20),new NeedVO(10973,20),new NeedVO(10974,6)]);
         this._list[this._list.length] = new ZBaoRLVO(30042,666666,4000,200,[new NeedVO(10965,20),new NeedVO(10966,20),new NeedVO(10967,20),new NeedVO(10968,6)]);
         this._list[this._list.length] = new ZBaoRLVO(30041,666666,4000,200,[new NeedVO(10961,20),new NeedVO(10962,20),new NeedVO(10963,20),new NeedVO(10964,6)]);
         this._list[this._list.length] = new ZBaoRLVO(30040,666666,4000,200,[new NeedVO(10943,20),new NeedVO(10944,20),new NeedVO(10945,20),new NeedVO(10947,6)]);
         this._list[this._list.length] = new ZBaoRLVO(30039,666666,4000,200,[new NeedVO(10927,20),new NeedVO(10928,20),new NeedVO(10929,20),new NeedVO(10946,6)]);
         this._list[this._list.length] = new ZBaoRLVO(30038,666666,4000,200,[new NeedVO(10917,20),new NeedVO(10918,20),new NeedVO(10919,20),new NeedVO(10920,6)]);
         this._list[this._list.length] = new ZBaoRLVO(30047,188888,888,1000,[new ZhiBaoNeedVO(30229),new ZhiBaoNeedVO(30235),new ZhiBaoNeedVO(30245),new ZhiBaoNeedVO(30246)]);
         this._list[this._list.length] = new ZBaoRLVO(30064,666666,3000,300,[new NeedVO(10588,10),new NeedVO(10593,10),new NeedVO(10597,10)]);
         this._list[this._list.length] = new ZBaoRLVO(30051,900000,3000,450,[new ZhiBaoNeedVO(30214),new ZhiBaoNeedVO(30025),new ZhiBaoNeedVO(30224),new ZhiBaoNeedVO(30215)]);
         this._list[this._list.length] = new ZBaoRLVO(30071,666666,3000,400,[new ZhiBaoNeedVO(30051),new NeedVO(10571,6),new NeedVO(10576,6)]);
         this._list[this._list.length] = new ZBaoRLVO(30072,666666,3000,350,[new ZhiBaoNeedVO(30071),new NeedVO(10584,6),new NeedVO(10571,6),new NeedVO(10576,6)]);
         this._list[this._list.length] = new ZBaoRLVO(30052,900000,3000,250,[new ZhiBaoNeedVO(30220),new ZhiBaoNeedVO(30221),new ZhiBaoNeedVO(30222),new ZhiBaoNeedVO(30223)]);
         this._list[this._list.length] = new ZBaoRLVO(30055,600000,1500,350,[new ZhiBaoNeedVO(30027),new NeedVO(10581,20),new NeedVO(10582,20),new NeedVO(10583,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30036,800000,3000,200,[new NeedVO(10598,20),new NeedVO(10599,20),new NeedVO(10600,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30037,800000,3000,200,[new NeedVO(10911,20),new NeedVO(10912,20),new NeedVO(10913,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30112,600000,2000,1000,[new ZhiBaoNeedVO(30012),new NeedVO(10572,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30212,800000,3000,1000,[new ZhiBaoNeedVO(30112),new NeedVO(10573,20)]);
         this._list[this._list.length] = new ZBaoRLVO(30020,500000,1000,600,[new ZhiBaoNeedVO(30007),new NeedVO(10554,5),new NeedVO(10555,5)]);
         this._list[this._list.length] = new ZBaoRLVO(30120,600000,1000,500,[new ZhiBaoNeedVO(30020),new ZhiBaoNeedVO(30107),new NeedVO(10554,10),new NeedVO(10555,10)]);
         this._list[this._list.length] = new ZBaoRLVO(30220,700000,1000,400,[new ZhiBaoNeedVO(30120),new ZhiBaoNeedVO(30207),new NeedVO(10554,15),new NeedVO(10555,15)]);
         this._list[this._list.length] = new ZBaoRLVO(30021,500000,1000,600,[new ZhiBaoNeedVO(30008),new NeedVO(10556,5),new NeedVO(10557,5)]);
         this._list[this._list.length] = new ZBaoRLVO(30121,600000,1000,500,[new ZhiBaoNeedVO(30021),new ZhiBaoNeedVO(30108),new NeedVO(10556,10),new NeedVO(10557,10)]);
         this._list[this._list.length] = new ZBaoRLVO(30221,700000,1000,400,[new ZhiBaoNeedVO(30121),new ZhiBaoNeedVO(30208),new NeedVO(10556,15),new NeedVO(10557,15)]);
         this._list[this._list.length] = new ZBaoRLVO(30022,500000,1000,600,[new ZhiBaoNeedVO(30009),new NeedVO(10567,5),new NeedVO(10568,5)]);
         this._list[this._list.length] = new ZBaoRLVO(30122,600000,1000,500,[new ZhiBaoNeedVO(30022),new ZhiBaoNeedVO(30109),new NeedVO(10567,10),new NeedVO(10568,10)]);
         this._list[this._list.length] = new ZBaoRLVO(30222,700000,1000,400,[new ZhiBaoNeedVO(30122),new ZhiBaoNeedVO(30209),new NeedVO(10567,15),new NeedVO(10568,15)]);
         this._list[this._list.length] = new ZBaoRLVO(30023,500000,1000,600,[new ZhiBaoNeedVO(30011),new NeedVO(10569,5),new NeedVO(10570,5)]);
         this._list[this._list.length] = new ZBaoRLVO(30123,600000,1000,500,[new ZhiBaoNeedVO(30023),new ZhiBaoNeedVO(30011),new NeedVO(10569,10),new NeedVO(10570,10)]);
         this._list[this._list.length] = new ZBaoRLVO(30223,700000,1000,400,[new ZhiBaoNeedVO(30123),new ZhiBaoNeedVO(30011),new NeedVO(10569,15),new NeedVO(10570,15)]);
         this._list[this._list.length] = new ZBaoRLVO(30024,200000,500,700,[new ZhiBaoNeedVO(30007),new ZhiBaoNeedVO(30008),new ZhiBaoNeedVO(30009)]);
         this._list[this._list.length] = new ZBaoRLVO(30025,400000,1000,600,[new ZhiBaoNeedVO(30024),new NeedVO(10564,2),new NeedVO(10565,2),new NeedVO(10566,2)]);
      }
      
      public function get list() : Array
      {
         return this._list;
      }
   }
}

