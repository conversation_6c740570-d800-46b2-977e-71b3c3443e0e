package file
{
   import com.mogames.utils.TxtUtil;
   
   public class NoticeConfig
   {
      
      private static var _instance:NoticeConfig;
      
      public var notices:String;
      
      public var url:String;
      
      public function NoticeConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : NoticeConfig
      {
         if(!_instance)
         {
            _instance = new NoticeConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.url = "https://my.4399.com/forums/thread-64305120" + "" + "";
         this.notices = TxtUtil.setColor("惊天神将V26.0更新：","FF00FF") + "<br>" + TxtUtil.setColor("风云活动：（07.22-08.03）","00FF00") + " <br>" + TxtUtil.setColor("\t 充值大返利（次日返利50%）","FFFFFF") + "<br>" + TxtUtil.setColor("\t 充值礼包","FFFFFF") + "<br>" + TxtUtil.setColor("\t 幸运转盘调整道具","FFFFFF") + "<br>" + TxtUtil.setColor("\t 免费砸蛋新增皮肤:辛宪英、左慈、文鸯、夏侯渊、袁绍","FFFFFF") + "<br>" + TxtUtil.setColor("1、新增[真]至宝：青铜链、冰魂灵","00FF00") + "<br>" + TxtUtil.setColor("2、调整活跃度材料奖励：星月龙凤环材料","00FF00") + "<br>" + TxtUtil.setColor("3、免费砸蛋取消皮肤掉落：徐盛、周泰、董卓、郭淮、陆抗","00FF00") + "<br>" + TxtUtil.setColor("玩家交流群：577965374","FF00FF") + "<br>";
      }
   }
}

