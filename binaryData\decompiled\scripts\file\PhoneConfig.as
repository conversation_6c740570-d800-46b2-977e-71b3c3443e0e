package file
{
   import mogames.gameData.phone.PhoneAction;
   import mogames.gameData.phone.PhoneAction0;
   import mogames.gameData.phone.PhoneAction1;
   import mogames.gameData.phone.PhoneAction2;
   
   public class PhoneConfig
   {
      
      private static var _instance:PhoneConfig;
      
      private var _list:Vector.<PhoneAction>;
      
      public function PhoneConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : PhoneConfig
      {
         if(!_instance)
         {
            _instance = new PhoneConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = new Vector.<PhoneAction>();
         this._list[this._list.length] = new PhoneAction0();
         this._list[this._list.length] = new PhoneAction1();
         this._list[this._list.length] = new PhoneAction2();
      }
      
      public function findAction(param1:String) : PhoneAction
      {
         var _loc2_:PhoneAction = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.phoneID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

