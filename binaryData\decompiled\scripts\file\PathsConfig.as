package file
{
   import com.mogames.others.RandCircle;
   import flash.geom.Point;
   import mogames.gameData.main.vo.PathsVO;
   
   public class PathsConfig
   {
      
      private static var _instance:PathsConfig;
      
      private static var MAX:int = 99;
      
      private var _paths:Vector.<PathsVO>;
      
      private var _headList:Array;
      
      private var _points:Array;
      
      public function PathsConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : PathsConfig
      {
         if(!_instance)
         {
            _instance = new PathsConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         var _loc2_:int = 0;
         var _loc3_:PathsVO = null;
         this._headList = [0,2,3,5,6,7,9,13,14,15,17,18];
         this._points = [];
         this._points[0] = new Point(156,513);
         this._points[1] = new RandCircle(new Point(320,495),45);
         this._points[2] = new Point(614,663);
         this._points[3] = new Point(493,524);
         this._points[4] = new Point(160,421);
         this._points[5] = new Point(239,387);
         this._points[6] = new Point(65,362);
         this._points[7] = new Point(600,332);
         this._points[8] = new RandCircle(new Point(360,342),22);
         this._points[9] = new Point(330,263);
         this._points[10] = new Point(110,244);
         this._points[11] = new Point(27,164);
         this._points[12] = new Point(174,134);
         this._points[13] = new Point(249,196);
         this._points[14] = new Point(419,153);
         this._points[15] = new Point(451,190);
         this._points[16] = new Point(555,137);
         this._points[17] = new Point(690,222);
         this._points[18] = new Point(755,133);
         this._points[19] = new RandCircle(new Point(546,240),33);
         this._paths = new Vector.<PathsVO>();
         var _loc1_:int = 0;
         while(_loc1_ < 20)
         {
            _loc2_ = 0;
            while(_loc2_ < 20)
            {
               _loc3_ = new PathsVO();
               _loc3_.start = _loc1_;
               _loc3_.end = _loc2_;
               this._paths[this._paths.length] = _loc3_;
               _loc2_++;
            }
            _loc1_++;
         }
         this.countPaths();
      }
      
      public function get randPaths() : Array
      {
         var _loc7_:Object = null;
         var _loc8_:int = 0;
         var _loc1_:Array = this._headList.slice();
         var _loc2_:int = Math.random() * _loc1_.length;
         var _loc3_:int = int(_loc1_[_loc2_]);
         _loc1_.splice(_loc2_,1);
         _loc2_ = Math.random() * _loc1_.length;
         var _loc4_:int = int(_loc1_[_loc2_]);
         var _loc5_:PathsVO = this.findPathsVO(_loc3_,_loc4_);
         var _loc6_:Array = [];
         for each(_loc8_ in _loc5_.paths)
         {
            _loc7_ = this._points[_loc8_];
            if(_loc7_ is Point)
            {
               _loc6_[_loc6_.length] = _loc7_.clone();
            }
            else if(_loc7_ is RandCircle)
            {
               _loc6_[_loc6_.length] = _loc7_.randPoint;
            }
         }
         return _loc6_;
      }
      
      private function findPathsVO(param1:int, param2:int) : PathsVO
      {
         var _loc3_:PathsVO = null;
         for each(_loc3_ in this._paths)
         {
            if(_loc3_.start == param1 && _loc3_.end == param2)
            {
               return _loc3_;
            }
         }
         return null;
      }
      
      private function countPaths() : void
      {
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc6_:int = 0;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc9_:PathsVO = null;
         var _loc10_:PathsVO = null;
         var _loc11_:PathsVO = null;
         var _loc1_:int = 20;
         var _loc2_:int = 38;
         var _loc3_:Array = [];
         _loc7_ = 0;
         while(_loc7_ < _loc1_)
         {
            _loc3_[_loc7_] = [];
            _loc8_ = 0;
            while(_loc8_ < _loc1_)
            {
               _loc3_[_loc7_][_loc8_] = MAX;
               if(_loc7_ == _loc8_)
               {
                  _loc3_[_loc7_][_loc8_] = 1;
               }
               _loc8_++;
            }
            _loc7_++;
         }
         _loc3_[0][1] = 1;
         _loc3_[1][0] = 1;
         _loc3_[1][2] = 1;
         _loc3_[1][3] = 1;
         _loc3_[1][4] = 1;
         _loc3_[1][7] = 1;
         _loc3_[1][8] = 1;
         _loc3_[2][1] = 1;
         _loc3_[3][1] = 1;
         _loc3_[4][1] = 1;
         _loc3_[4][5] = 1;
         _loc3_[4][6] = 1;
         _loc3_[5][4] = 1;
         _loc3_[6][4] = 1;
         _loc3_[7][1] = 1;
         _loc3_[8][1] = 1;
         _loc3_[8][9] = 1;
         _loc3_[8][10] = 1;
         _loc3_[8][19] = 1;
         _loc3_[9][8] = 1;
         _loc3_[10][8] = 1;
         _loc3_[10][11] = 1;
         _loc3_[10][13] = 1;
         _loc3_[11][10] = 1;
         _loc3_[11][12] = 1;
         _loc3_[12][11] = 1;
         _loc3_[12][14] = 1;
         _loc3_[13][10] = 1;
         _loc3_[14][12] = 1;
         _loc3_[15][19] = 1;
         _loc3_[16][18] = 1;
         _loc3_[16][19] = 1;
         _loc3_[17][19] = 1;
         _loc3_[18][16] = 1;
         _loc3_[19][8] = 1;
         _loc3_[19][15] = 1;
         _loc3_[19][16] = 1;
         _loc3_[19][17] = 1;
         _loc7_ = 0;
         while(_loc7_ < _loc1_)
         {
            _loc8_ = 0;
            while(_loc8_ < _loc1_)
            {
               _loc11_ = this.findPathsVO(_loc7_,_loc8_);
               if(_loc3_[_loc7_][_loc8_] != MAX)
               {
                  _loc11_.paths[0] = _loc7_;
                  _loc11_.paths[1] = _loc8_;
               }
               _loc8_++;
            }
            _loc7_++;
         }
         _loc6_ = 0;
         while(_loc6_ < _loc1_)
         {
            _loc4_ = 0;
            while(_loc4_ < _loc1_)
            {
               _loc5_ = 0;
               while(_loc5_ < _loc1_)
               {
                  if(_loc3_[_loc4_][_loc6_] != MAX && _loc3_[_loc6_][_loc5_] != MAX && _loc3_[_loc4_][_loc6_] + _loc3_[_loc6_][_loc5_] < _loc3_[_loc4_][_loc5_])
                  {
                     _loc3_[_loc4_][_loc5_] = _loc3_[_loc4_][_loc6_] + _loc3_[_loc6_][_loc5_];
                     _loc9_ = this.findPathsVO(_loc4_,_loc6_);
                     _loc10_ = this.findPathsVO(_loc6_,_loc5_);
                     _loc11_ = this.findPathsVO(_loc4_,_loc5_);
                     _loc7_ = 0;
                     while(_loc7_ < _loc1_)
                     {
                        if(_loc9_.paths[_loc7_] < 0)
                        {
                           break;
                        }
                        _loc11_.paths[_loc7_] = _loc9_.paths[_loc7_];
                        _loc7_++;
                     }
                     _loc8_ = 1;
                     while(_loc8_ < _loc1_)
                     {
                        if(_loc10_.paths[_loc8_] < 0)
                        {
                           break;
                        }
                        _loc11_.paths[_loc7_ + _loc8_ - 1] = _loc10_.paths[_loc8_];
                        _loc8_++;
                     }
                  }
                  _loc5_++;
               }
               _loc4_++;
            }
            _loc6_++;
         }
         _loc7_ = 0;
         while(_loc7_ < this._paths.length)
         {
            if(this._headList.indexOf(this._paths[_loc7_].start) == -1 || this._headList.indexOf(this._paths[_loc7_].end) == -1)
            {
               this._paths.splice(_loc7_,1);
               _loc7_--;
            }
            _loc7_++;
         }
      }
   }
}

