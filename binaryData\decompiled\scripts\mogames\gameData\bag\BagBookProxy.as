package mogames.gameData.bag
{
   import mogames.gameData.bag.base.BookBagVO;
   
   public class BagBookProxy
   {
      
      private static var _instance:BagBookProxy;
      
      private var _bookBag:Array;
      
      public function BagBookProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
      }
      
      public static function instance() : BagBookProxy
      {
         if(!_instance)
         {
            _instance = new BagBookProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this._bookBag = [];
         this._bookBag[this._bookBag.length] = new BookBagVO(21);
         this._bookBag[this._bookBag.length] = new BookBagVO(22);
      }
      
      public function set loadData(param1:Array) : void
      {
         var _loc2_:Array = null;
         var _loc3_:BookBagVO = null;
         var _loc4_:String = null;
         this.startNew();
         if(!param1)
         {
            return;
         }
         for each(_loc4_ in param1)
         {
            _loc2_ = _loc4_.split("H");
            _loc3_ = this.findBookBag(int(_loc2_[0]));
            if(_loc3_)
            {
               _loc3_.setSkillID(int(_loc2_[1]));
            }
         }
      }
      
      public function get saveData() : Array
      {
         var _loc2_:BookBagVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._bookBag)
         {
            _loc1_[_loc1_.length] = _loc2_.bagID + "H" + _loc2_.skillID;
         }
         return _loc1_;
      }
      
      public function findBookBag(param1:int) : BookBagVO
      {
         var _loc2_:BookBagVO = null;
         for each(_loc2_ in this._bookBag)
         {
            if(_loc2_.bagID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function hasBook(param1:int) : Boolean
      {
         var _loc2_:BookBagVO = null;
         for each(_loc2_ in this._bookBag)
         {
            if(_loc2_.skillID == param1)
            {
               return true;
            }
         }
         return false;
      }
      
      public function get bookBag() : Array
      {
         return this._bookBag;
      }
   }
}

