package com.mogames.system
{
   import flash.events.Event;
   import mogames.Layers;
   
   public class Looper
   {
      
      public static const FPS:int = 30;
      
      private var _force:Boolean;
      
      private var _list:Vector.<Function>;
      
      public function Looper()
      {
         super();
         this._list = new Vector.<Function>();
         Layers.gameStage.addEventListener(Event.ENTER_FRAME,this.update);
      }
      
      private function update(param1:Event) : void
      {
         var _loc2_:Function = null;
         for each(_loc2_ in this._list)
         {
            _loc2_();
         }
      }
      
      public function add(param1:Function) : void
      {
         if(this._list.indexOf(param1) != -1)
         {
            return;
         }
         this._list[this._list.length] = param1;
      }
      
      public function remove(param1:Function) : void
      {
         var _loc2_:int = int(this._list.indexOf(param1));
         if(_loc2_ != -1)
         {
            this._list.splice(_loc2_,1);
         }
      }
      
      public function cleanList() : void
      {
         this._list.length = 0;
      }
   }
}

