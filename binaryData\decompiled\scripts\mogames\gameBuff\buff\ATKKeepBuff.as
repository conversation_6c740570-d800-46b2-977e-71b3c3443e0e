package mogames.gameBuff.buff
{
   import mogames.gameBuff.base.TimeRoleBuff;
   import mogames.gameData.buff.ConstBuffVO;
   import mogames.gameEffect.EffectManager;
   
   public class AT<PERSON><PERSON>eepBuff extends TimeRoleBuff
   {
      
      protected var _add:int;
      
      public function ATK<PERSON>eepBuff(param1:ConstBuffVO)
      {
         super(param1);
      }
      
      override protected function createData() : void
      {
         _owner.signal.add(this.listenATK);
      }
      
      override protected function onCleanRole() : void
      {
         super.onCleanRole();
         _owner.signal.remove(this.listenATK);
         _owner.roleVO.skillATK -= this._add;
         _owner.roleVO.updateATK();
      }
      
      private function listenATK(param1:Object) : void
      {
         if(param1.type != "ROLE_ATK")
         {
            return;
         }
         var _loc2_:int = _buffVO.args.isPer ? int(_owner.roleVO.totalATK * _buffVO.args.value * 0.01) : int(_buffVO.args.value);
         _owner.roleVO.skillATK += _loc2_;
         _owner.roleVO.updateATK();
         this._add += _loc2_;
         EffectManager.addHeadWord("攻击提升！",_owner.center.x,_owner.center.y);
      }
   }
}

