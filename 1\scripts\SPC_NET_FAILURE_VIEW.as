package
{
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol225")]
   public dynamic class SPC_NET_FAILURE_VIEW extends Sprite
   {
      
      public var bg:MovieClip;
      
      public var closeBtn:SimpleButton;
      
      public var iconHolder:MovieClip;
      
      public var titleTf:TextField;
      
      public var tmp:MovieClip;
      
      public function SPC_NET_FAILURE_VIEW()
      {
         super();
      }
   }
}

