package com.mogames.comt
{
   import flash.events.MouseEvent;
   
   public class StepComt
   {
      
      private var _cur:int;
      
      private var _func:Function;
      
      private var _total:int;
      
      private var _skin:Object;
      
      public function StepComt(param1:Object, param2:int = 1, param3:Function = null)
      {
         super();
         this._cur = 1;
         this._total = param2;
         this._func = param3;
         this._skin = param1;
         this._skin.btnAdd.addEventListener(MouseEvent.CLICK,this.onAdd);
         this._skin.btnReduce.addEventListener(MouseEvent.CLICK,this.onReduce);
      }
      
      private function onAdd(param1:MouseEvent) : void
      {
         ++this._cur;
         if(this._cur >= this._total)
         {
            this._cur = this._total;
         }
         if(this._func != null)
         {
            this._func();
         }
      }
      
      private function onReduce(param1:MouseEvent) : void
      {
         --this._cur;
         if(this._cur <= 1)
         {
            this._cur = 1;
         }
         if(this._func != null)
         {
            this._func();
         }
      }
      
      public function set max(param1:int) : void
      {
         this._total = param1;
      }
      
      public function get max() : int
      {
         return this._total;
      }
      
      public function get data() : int
      {
         return this._cur;
      }
      
      public function set data(param1:int) : void
      {
         this._cur = param1;
         if(this._cur <= 1)
         {
            this._cur = 1;
         }
         else if(this._cur >= this.max)
         {
            this._cur = this.max;
         }
         if(this._func != null)
         {
            this._func();
         }
      }
      
      public function destroy() : void
      {
         this._skin.btnAdd.removeEventListener(MouseEvent.CLICK,this.onAdd);
         this._skin.btnReduce.removeEventListener(MouseEvent.CLICK,this.onReduce);
         this._skin = null;
      }
   }
}

