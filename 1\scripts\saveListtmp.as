package
{
   import flash.display.Sprite;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol206")]
   public dynamic class saveListtmp extends Sprite
   {
      
      public var item0:mouseOverSkin;
      
      public var item1:mouseOverSkin;
      
      public var item2:mouseOverSkin;
      
      public var item3:mouseOverSkin;
      
      public var item4:mouseOverSkin;
      
      public var item5:mouseOverSkin;
      
      public var item6:mouseOverSkin;
      
      public var item7:mouseOverSkin;
      
      public var titleTxt:TextField;
      
      public var titleTxtBack:TextField;
      
      public function saveListtmp()
      {
         super();
      }
   }
}

