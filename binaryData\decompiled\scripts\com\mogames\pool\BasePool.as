package com.mogames.pool
{
   import flash.utils.Dictionary;
   import flash.utils.getDefinitionByName;
   import flash.utils.getQualifiedClassName;
   
   public class BasePool
   {
      
      private var _dict:Dictionary;
      
      public function BasePool(param1:Array = null, param2:Array = null)
      {
         var _loc4_:Class = null;
         var _loc5_:int = 0;
         var _loc6_:int = 0;
         super();
         this._dict = new Dictionary();
         if(!param1)
         {
            return;
         }
         var _loc3_:int = int(param1.length);
         while(_loc5_ < _loc3_)
         {
            _loc4_ = param1[_loc5_] as Class;
            _loc6_ = 0;
            while(_loc6_ < param2[_loc5_])
            {
               this.push(new _loc4_());
               _loc5_++;
            }
            _loc5_++;
         }
      }
      
      public function push(param1:Object) : void
      {
         if(param1 == null)
         {
            return;
         }
         var _loc2_:String = getQualifiedClassName(param1);
         if(this._dict[_loc2_] == null)
         {
            this._dict[_loc2_] = [];
         }
         this._dict[_loc2_].push(param1);
      }
      
      public function pop(param1:Object) : Object
      {
         var _loc2_:String = getQualifiedClassName(param1);
         if(this._dict[_loc2_] != null && this._dict[_loc2_].length > 0)
         {
            return this._dict[_loc2_].pop();
         }
         var _loc3_:Class = getDefinitionByName(_loc2_) as Class;
         return new _loc3_() as Object;
      }
      
      public function destroy() : void
      {
         var _loc1_:String = null;
         var _loc2_:Object = null;
         if(!this._dict)
         {
            return;
         }
         for(_loc1_ in this._dict)
         {
            for each(_loc2_ in this._dict[_loc1_])
            {
               if(_loc2_)
               {
                  _loc2_.destroy();
               }
            }
            this._dict[_loc1_].length = 0;
            delete this._dict[_loc1_];
         }
         this._dict = null;
      }
   }
}

