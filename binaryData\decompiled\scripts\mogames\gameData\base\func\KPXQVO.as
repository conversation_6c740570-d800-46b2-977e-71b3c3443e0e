package mogames.gameData.base.func
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import com.mogames.utils.TxtUtil;
   import mogames.gameData.base.vo.NeedVO;
   
   public class KPXQVO
   {
      
      private var _index:Oint = new Oint();
      
      private var _qians:Array;
      
      private var _chais:Array;
      
      private var _qianTip:String;
      
      private var _chaiTip:String;
      
      public function KPXQVO(param1:int, param2:Array, param3:Array)
      {
         super();
         MathUtil.saveINT(this._index,param1);
         this._qians = param2;
         this._chais = param3;
      }
      
      public function get index() : int
      {
         return MathUtil.loadINT(this._index);
      }
      
      public function get qians() : Array
      {
         return this._qians;
      }
      
      public function get chais() : Array
      {
         return this._chais;
      }
      
      public function get qianTip() : String
      {
         var _loc2_:NeedVO = null;
         if(this._qianTip)
         {
            return this._qianTip;
         }
         this._qianTip = "镶嵌需消耗：<br>";
         var _loc1_:Array = [];
         for each(_loc2_ in this._qians)
         {
            if(!_loc2_.lackVO)
            {
               _loc1_[_loc1_.length] = _loc2_.needName + "X" + _loc2_.needNum;
            }
            else
            {
               _loc1_[_loc1_.length] = TxtUtil.setColor(_loc2_.needName + "X" + _loc2_.needNum);
            }
         }
         this._qianTip += _loc1_.join(",");
         return this._qianTip;
      }
      
      public function get chaiTip() : String
      {
         var _loc2_:NeedVO = null;
         if(this._chaiTip)
         {
            return this._chaiTip;
         }
         this._chaiTip = "拆除需消耗：<br>";
         var _loc1_:Array = [];
         for each(_loc2_ in this._chais)
         {
            if(!_loc2_.lackVO)
            {
               _loc1_[_loc1_.length] = _loc2_.needName + "X" + _loc2_.needNum;
            }
            else
            {
               _loc1_[_loc1_.length] = TxtUtil.setColor(_loc2_.needName + "X" + _loc2_.needNum);
            }
         }
         this._chaiTip += _loc1_.join(",");
         return this._chaiTip;
      }
   }
}

