package file
{
   import flash.utils.Dictionary;
   import mogames.gameData.role.battle.RoleArgVO;
   
   public class BattlePMConfig
   {
      
      private static var _instance:BattlePMConfig;
      
      private var _dic:Dictionary;
      
      public function BattlePMConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : BattlePMConfig
      {
         if(!_instance)
         {
            _instance = new BattlePMConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._dic = new Dictionary();
         this._dic[100] = [new RoleArgVO(800,1000,0,0,10,0,0,7,null),new RoleArgVO(800,1000,0,0,10,0,0,4,null),new RoleArgVO(800,1000,0,0,10,0,0,9,null),new RoleArgVO(800,1000,0,0,10,0,0,5,null),new RoleArgVO(800,1000,0,0,10,0,0,12,null),new RoleArgVO(800,1000,0,0,10,0,0,4,null),new RoleArgVO(800,1000,0,0,10,0,0,5,null),new RoleArgVO(800,1000,0,0,10,0,0,10,null),new RoleArgVO(800,1000,0,0,10,0,0,11,null),new RoleArgVO(800,1000,0,0,10,0,0,6,null)];
      }
      
      public function findPMList(param1:int) : Array
      {
         return this._dic[param1];
      }
   }
}

