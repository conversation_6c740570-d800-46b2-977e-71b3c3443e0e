package file
{
   import mogames.gameData.base.func.NpcFeelVO;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.base.vo.EquipRewardVO;
   import mogames.gameData.base.vo.NeedVO;
   
   public class NpcFeelConfig
   {
      
      private static var _instance:NpcFeelConfig;
      
      public var opens:Array = [900,902,903,904,906];
      
      private var _list:Array;
      
      public function NpcFeelConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : NpcFeelConfig
      {
         if(!_instance)
         {
            _instance = new NpcFeelConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = [];
         this._list[this._list.length] = new NpcFeelVO(900,[new NeedVO(10726,10),new NeedVO(10727,10),new NeedVO(10728,10),new NeedVO(10726,7),new NeedVO(10727,7),new NeedVO(10728,7),new NeedVO(10726,5),new NeedVO(10727,5),new NeedVO(10728,5)],[new BaseRewardVO(10277,10),new BaseRewardVO(10278,10),new BaseRewardVO(10279,10),new BaseRewardVO(10280,10)]);
         this._list[this._list.length] = new NpcFeelVO(901,[new NeedVO(10000,1)],[new BaseRewardVO(10000,2),new BaseRewardVO(10000,1),new BaseRewardVO(10000,1),new BaseRewardVO(10000,1)]);
         this._list[this._list.length] = new NpcFeelVO(902,[new NeedVO(10021,5),new NeedVO(10022,6),new NeedVO(10023,7),new NeedVO(10021,6),new NeedVO(10022,7),new NeedVO(10023,5),new NeedVO(10250,5),new NeedVO(10301,6),new NeedVO(11011,5),new NeedVO(11012,3),new NeedVO(11501,5),new NeedVO(10250,6),new NeedVO(10301,7),new NeedVO(11011,6),new NeedVO(11012,5),new NeedVO(11501,7)],[new BaseRewardVO(11003,20),new BaseRewardVO(11005,15),new BaseRewardVO(10851,15),new BaseRewardVO(50039,30)]);
         this._list[this._list.length] = new NpcFeelVO(903,[new NeedVO(10601,4),new NeedVO(10602,3),new NeedVO(10611,4),new NeedVO(10612,5),new NeedVO(10616,4),new NeedVO(10617,3),new NeedVO(10618,3),new NeedVO(10619,4),new NeedVO(10620,3)],[new BaseRewardVO(11158,15),new BaseRewardVO(11104,15),new BaseRewardVO(50039,20),new BaseRewardVO(50039,20)]);
         this._list[this._list.length] = new NpcFeelVO(904,[new NeedVO(10412,5),new NeedVO(10413,6),new NeedVO(10415,7),new NeedVO(10416,8),new NeedVO(10412,8),new NeedVO(10413,7),new NeedVO(10415,6),new NeedVO(10416,5)],[new BaseRewardVO(10280,5),new BaseRewardVO(10281,5),new BaseRewardVO(10282,5),new BaseRewardVO(10283,5)]);
         this._list[this._list.length] = new NpcFeelVO(905,[new NeedVO(10000,1)],[new BaseRewardVO(10000,2),new BaseRewardVO(10000,1),new BaseRewardVO(10000,1),new BaseRewardVO(10000,1)]);
         this._list[this._list.length] = new NpcFeelVO(906,[new NeedVO(68001,5),new NeedVO(68002,5),new NeedVO(10024,6),new NeedVO(10025,6),new NeedVO(10029,6)],[new BaseRewardVO(50041,10),new EquipRewardVO(42481,3),new BaseRewardVO(50041,10),new BaseRewardVO(50041,10)]);
         this._list[this._list.length] = new NpcFeelVO(907,[new NeedVO(10000,1)],[new BaseRewardVO(10000,2),new BaseRewardVO(10000,1),new BaseRewardVO(10000,1),new BaseRewardVO(10000,1)]);
         this._list[this._list.length] = new NpcFeelVO(908,[new NeedVO(10000,1)],[new BaseRewardVO(10000,2),new BaseRewardVO(10000,1),new BaseRewardVO(10000,1),new BaseRewardVO(10000,1)]);
      }
      
      public function findFeelVO(param1:int) : NpcFeelVO
      {
         var _loc2_:NpcFeelVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.npcID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

