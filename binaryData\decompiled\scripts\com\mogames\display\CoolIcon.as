package com.mogames.display
{
   import com.mogames.system.SysRender;
   import com.mogames.utils.FontUtil;
   import com.mogames.utils.MethodUtil;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.Sprite;
   import flash.text.TextField;
   import mogames.Layers;
   import mogames.gameAsset.AssetManager;
   
   public class CoolIcon extends BaseSprite
   {
      
      private var _total:int;
      
      private var _cur:int;
      
      private var _radius:int;
      
      private var _toX:Number;
      
      private var _toY:Number;
      
      private var _toAngle:Number;
      
      private var _btm:Bitmap;
      
      private var _black:Bitmap;
      
      private var _top:Bitmap;
      
      private var _mask:Sprite;
      
      private var _txt:TextField;
      
      private var _running:Boolean;
      
      private var _okFunc:Function;
      
      public function CoolIcon()
      {
         super();
         this._btm = new Bitmap();
         this._black = new Bitmap(new BitmapData(10,10,true,2281701376));
         this._top = new Bitmap();
         this._mask = new Sprite();
         this._txt = AssetManager.newText(-15,-12);
         FontUtil.initText(this._txt,16776960,18,30,28);
         this._top.mask = this._mask;
         this._black.visible = false;
         MethodUtil.addChildArr([this._btm,this._black,this._top,this._mask,this._txt],this);
      }
      
      public function create(param1:BitmapData, param2:int, param3:Number, param4:Function) : void
      {
         this._okFunc = param4;
         this.initIcon(this._btm,param1,param2);
         this.initIcon(this._top,param1,param2);
         this._black.width = this._black.height = param2;
         this._black.x = this._black.y = -param2 * 0.5;
         this._black.visible = true;
         this._total = param3 * SysRender.FPS;
         this._radius = Math.sqrt(param2 * param2 * 0.5);
         this.clean();
      }
      
      public function setFilter(param1:Array) : void
      {
         this._btm.filters = param1;
      }
      
      private function initIcon(param1:Bitmap, param2:BitmapData, param3:int) : void
      {
         param1.bitmapData = param2;
         param1.width = param1.height = param3;
         param1.x = param1.y = -param3 * 0.5;
         param1.smoothing = true;
      }
      
      public function startCool() : void
      {
         this._cur = 0;
         this._mask.graphics.beginFill(0);
         this._mask.graphics.moveTo(0,0);
         this._mask.graphics.lineTo(0,-this._radius);
         this._black.visible = true;
         this.stopCool = false;
      }
      
      public function set stopCool(param1:Boolean) : void
      {
         this._running = !param1;
         if(param1)
         {
            Layers.render.remove(this.update);
         }
         else
         {
            Layers.render.add(this.update);
         }
      }
      
      private function update() : void
      {
         ++this._cur;
         if(this._cur < this._total)
         {
            this._toAngle = 2 * Math.PI * (this._cur / this._total) - Math.PI * 0.5;
            this._toX = this._radius * Math.cos(this._toAngle);
            this._toY = this._radius * Math.sin(this._toAngle);
            this._mask.graphics.lineTo(this._toX,this._toY);
            FontUtil.setText(this._txt,int((this._total - this._cur) / SysRender.FPS + 1) + "");
         }
         else
         {
            this.handlerComplete();
         }
      }
      
      private function handlerComplete() : void
      {
         if(this._okFunc != null)
         {
            this._okFunc();
         }
         this.stopCool = true;
         this._black.visible = false;
         this._mask.graphics.clear();
         this._txt.text = "";
      }
      
      private function clean() : void
      {
         this.stopCool = true;
         this._black.visible = false;
         this._mask.graphics.clear();
         this._txt.text = "";
      }
      
      public function get running() : Boolean
      {
         return this._running;
      }
      
      override public function destroy() : void
      {
         super.destroy();
         this.stopCool = true;
         this._okFunc = null;
         this._mask = null;
         this._btm = null;
         this._black = null;
         this._top = null;
         this._txt = null;
      }
   }
}

