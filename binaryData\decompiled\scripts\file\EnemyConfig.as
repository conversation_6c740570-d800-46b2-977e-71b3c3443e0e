package file
{
   import mogames.gameData.role.base.RoleAttVO;
   
   public class EnemyConfig
   {
      
      private static var _instance:EnemyConfig;
      
      private var _atts:Vector.<RoleAttVO>;
      
      public function EnemyConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : EnemyConfig
      {
         if(!_instance)
         {
            _instance = new EnemyConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._atts = new Vector.<RoleAttVO>();
         this._atts[this._atts.length] = new RoleAttVO(213,0,0,0,30,50,210,90,0,0,0);
         this._atts[this._atts.length] = new RoleAttVO(214,0,0,0,50,80,180,90,0,0,0);
         this._atts[this._atts.length] = new RoleAttVO(221,19,2,0,20,20,150,60,30,3.5,0.8);
         this._atts[this._atts.length] = new RoleAttVO(222,23,3,1,30,30,180,70,35,4,1.1);
      }
      
      public function findATT(param1:int) : RoleAttVO
      {
         var _loc2_:RoleAttVO = null;
         for each(_loc2_ in this._atts)
         {
            if(_loc2_.roleID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

