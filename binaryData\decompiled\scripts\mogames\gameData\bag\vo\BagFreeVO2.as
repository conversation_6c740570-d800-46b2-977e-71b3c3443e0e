package mogames.gameData.bag.vo
{
   import mogames.gameData.bag.base.BagLockVO;
   import mogames.gameData.flag.FlagProxy;
   
   public class BagFreeVO2 extends BagLockVO
   {
      
      public function BagFreeVO2(param1:int, param2:String = "")
      {
         super(param1,param2);
      }
      
      override public function get isOpen() : Boolean
      {
         return FlagProxy.instance().openFlag.isComplete(188);
      }
   }
}

