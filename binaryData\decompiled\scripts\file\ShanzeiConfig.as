package file
{
   import com.mogames.utils.MathUtil;
   import mogames.gameData.base.WhereProxy;
   import mogames.gameData.base.func.RandWaveVO;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameData.mission.base.MissionBattleVO;
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.base.RoleAttVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.role.hero.HeroAttVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class ShanzeiConfig
   {
      
      private static var _instance:ShanzeiConfig;
      
      private var _waves:Array;
      
      private var _armyID:int;
      
      private var _bossID:int;
      
      private var _rewards:Array;
      
      public function ShanzeiConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : ShanzeiConfig
      {
         if(!_instance)
         {
            _instance = new ShanzeiConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._armyID = 222;
         this._bossID = 213;
         this._waves = [];
         this._waves[this._waves.length] = new RandWaveVO(5,[{
            "index":3,
            "num":1
         }],[20,20,20,20,30],[10,8,10,8,10]);
         this._waves[this._waves.length] = new RandWaveVO(5,[{
            "index":3,
            "num":2
         }],[20,20,40,20,30],[10,10,10,8,10]);
         this._waves[this._waves.length] = new RandWaveVO(5,[{
            "index":2,
            "num":1
         }],[20,22,15,20,30],[10,8,10,5,10]);
         this._waves[this._waves.length] = new RandWaveVO(5,[{
            "index":2,
            "num":1
         }],[20,15,22,15,30],[10,10,10,12,10]);
         this._waves[this._waves.length] = new RandWaveVO(5,[{
            "index":3,
            "num":1
         }],[20,30,15,20,30],[10,8,10,7,10]);
         this._waves[this._waves.length] = new RandWaveVO(5,[{
            "index":2,
            "num":1
         }],[20,15,22,22,23],[10,10,10,9,10]);
         this._waves[this._waves.length] = new RandWaveVO(5,[{
            "index":3,
            "num":2
         }],[20,20,22,15,30],[10,10,10,12,10]);
         this._waves[this._waves.length] = new RandWaveVO(5,[{
            "index":4,
            "num":1
         }],[20,15,25,20,30],[9,12,10,10,10]);
         this._rewards = [new BaseRewardVO(10302,1),new BaseRewardVO(10273,1),new BaseRewardVO(10274,1),new BaseRewardVO(10275,1),new BaseRewardVO(10276,1),new BaseRewardVO(10277,1),new BaseRewardVO(10278,1),new BaseRewardVO(10302,1),new BaseRewardVO(10275,1),new BaseRewardVO(10276,1),new BaseRewardVO(10277,1),new BaseRewardVO(10278,1),new BaseRewardVO(18891,1),new BaseRewardVO(18892,1),new BaseRewardVO(18893,1),new BaseRewardVO(18894,1),new BaseRewardVO(18895,1),new BaseRewardVO(19001,1),new BaseRewardVO(19002,1),new BaseRewardVO(19003,1)];
      }
      
      private function newSkills() : Array
      {
         var _loc1_:Array = [];
         _loc1_[_loc1_.length] = {
            "sid":1001,
            "arg":{
               "hurt":this.countHurt(39),
               "keepTime":3,
               "hurtCount":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1002,
            "arg":{
               "hurt":this.countHurt(59),
               "hurtCount":5
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1003,
            "arg":{
               "hurt":this.countHurt(53),
               "hurtCount":5
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1004,
            "arg":{
               "hurt":this.countHurt(30),
               "roleNum":4
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1005,
            "arg":{
               "hurt":this.countHurt(44),
               "roleNum":10
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1006,
            "arg":{"hurt":this.countHurt(50)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1007,
            "arg":{
               "hurt":this.countHurt(48),
               "keepTime":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1008,
            "arg":{"hurt":this.countHurt(60)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1009,
            "arg":{"hurt":this.countHurt(50)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1010,
            "arg":{"hurt":this.countHurt(33)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1011,
            "arg":{"hurt":this.countHurt(32)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1012,
            "arg":{"hurt":this.countHurt(44)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1013,
            "arg":{"hurt":this.countHurt(62)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1014,
            "arg":{
               "hurt":this.countHurt(34),
               "keepTime":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1015,
            "arg":{
               "hurt":this.countHurt(27),
               "keepTime":8,
               "hurtCount":5
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1016,
            "arg":{
               "hurt":this.countHurt(34),
               "roleNum":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1017,
            "arg":{
               "hurt":this.countHurt(34),
               "hurtCount":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1018,
            "arg":{
               "hurt":this.countHurt(40),
               "hurtCount":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1019,
            "arg":{
               "hurt":this.countHurt(56),
               "keepTime":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1020,
            "arg":{"hurt":this.countHurt(41)}
         };
         return _loc1_;
      }
      
      private function countHurt(param1:int) : int
      {
         return param1 * int(MasterProxy.instance().masterVO.level * 0.55 + 1);
      }
      
      private function countReward() : Array
      {
         var _loc1_:int = 0;
         var _loc5_:int = 0;
         var _loc2_:int = Math.random() * 100 + 1;
         if(_loc2_ <= 60)
         {
            _loc1_ = 1;
         }
         else if(_loc2_ <= 90)
         {
            _loc1_ = 2;
         }
         else
         {
            _loc1_ = 3;
         }
         var _loc3_:Array = [];
         var _loc4_:Array = this._rewards.slice();
         var _loc6_:int = 0;
         while(_loc6_ < _loc1_)
         {
            _loc5_ = Math.random() * _loc4_.length;
            _loc3_[_loc6_] = _loc4_[_loc5_];
            _loc4_.splice(_loc5_,1);
            _loc6_++;
         }
         return _loc3_;
      }
      
      public function newBattleVO(param1:int) : MissionBattleVO
      {
         var _loc2_:MissionBattleVO = WaveExtraConfig.instance().findBattle(param1);
         var _loc3_:int = MasterProxy.instance().needExp * 0.04 + 18888;
         var _loc4_:int = MasterProxy.instance().needExp * 0.05 + 28888;
         return new MissionBattleVO(param1,_loc3_,_loc3_,this.countReward(),[],_loc2_.jsonURL);
      }
      
      public function newShanzeiWave() : WaveDataVO
      {
         var _loc4_:int = 0;
         var _loc6_:Object = null;
         var _loc9_:int = 0;
         var _loc10_:Object = null;
         var _loc11_:OneWaveVO = null;
         var _loc12_:int = 0;
         var _loc13_:int = 0;
         var _loc1_:Array = this.newSkills();
         var _loc2_:RandWaveVO = this._waves[int(Math.random() * this._waves.length)];
         var _loc3_:WaveDataVO = new WaveDataVO();
         _loc3_.limitBR = new WaveLimitVO(0,1,1);
         var _loc5_:Array = WhereProxy.instance().findWhere(6);
         if(_loc5_.length > 0)
         {
            _loc4_ = int(_loc5_[int(Math.random() * _loc5_.length)]);
         }
         if(_loc4_ != 0 && MathUtil.checkOdds(300))
         {
            _loc9_ = RoleConfig.instance().findInfo(_loc4_).skillID;
            for each(_loc10_ in _loc1_)
            {
               if(_loc10_.sid == _loc9_)
               {
                  _loc6_ = _loc10_;
                  break;
               }
            }
         }
         else
         {
            _loc4_ = this._bossID;
         }
         if(!_loc6_)
         {
            _loc6_ = _loc1_[int(Math.random() * _loc1_.length)];
         }
         if(_loc4_ != this._bossID)
         {
            _loc3_.zhuBoss = this.newHeroBoss(_loc4_,_loc6_);
         }
         else
         {
            _loc3_.zhuBoss = this.newSZBoss(_loc4_,_loc6_);
         }
         var _loc7_:int = 0;
         var _loc8_:int = _loc2_.total;
         while(_loc7_ < _loc8_)
         {
            _loc11_ = new OneWaveVO(_loc2_.findTime(_loc7_));
            _loc11_.addEnemy(new WaveEnemyVO(_loc2_.findEmemies(_loc7_),this.newEnemyArg));
            _loc12_ = _loc2_.findFu(_loc7_);
            _loc13_ = 0;
            while(_loc13_ < _loc12_)
            {
               _loc11_.addFu(this.newSZBoss(this._bossID,_loc1_[int(Math.random() * _loc1_.length)]));
               _loc13_++;
            }
            _loc3_.addWave(_loc11_);
            _loc7_++;
         }
         _loc1_.length = 0;
         _loc1_ = null;
         return _loc3_;
      }
      
      private function get newEnemyArg() : RoleArgVO
      {
         var _loc1_:RoleAttVO = EnemyConfig.instance().findATT(this._armyID);
         var _loc2_:int = MasterProxy.instance().masterVO.level;
         var _loc3_:int = _loc1_.baseHP + _loc1_.argHP * _loc2_;
         var _loc4_:int = _loc1_.baseATK + _loc1_.argATK * _loc2_;
         var _loc5_:int = _loc1_.baseDEF + _loc1_.argDEF * _loc2_;
         return new RoleArgVO(_loc1_.roleID,_loc3_,_loc4_,_loc5_,_loc1_.baseMISS,_loc1_.baseCRIT,_loc1_.baseBEI,_loc1_.baseSPD,null);
      }
      
      private function newHeroBoss(param1:int, param2:Object) : BossArgVO
      {
         var _loc3_:HeroAttVO = HeroConfig.instance().findATT(param1);
         return new BossArgVO(param1,this.bossHP,this.bossATK,this.bossDEF,_loc3_.argMISS,_loc3_.argCRIT,_loc3_.argBEI,_loc3_.argSPD,new BossSkillData0(100,param2.arg),param2.sid,1);
      }
      
      private function newSZBoss(param1:int, param2:Object) : BossArgVO
      {
         var _loc3_:RoleAttVO = EnemyConfig.instance().findATT(param1);
         return new BossArgVO(param1,this.szBossHP,this.szBossATK,this.szBossDEF,_loc3_.baseMISS,_loc3_.baseCRIT,_loc3_.baseBEI,_loc3_.baseSPD,new BossSkillData0(80,param2.arg),param2.sid,0);
      }
      
      private function get szBossHP() : int
      {
         return 250 * MasterProxy.instance().masterVO.level;
      }
      
      private function get szBossATK() : int
      {
         return 3 * MasterProxy.instance().masterVO.level;
      }
      
      private function get szBossDEF() : int
      {
         return MasterProxy.instance().masterVO.level;
      }
      
      private function get bossHP() : int
      {
         return 700 * MasterProxy.instance().masterVO.level;
      }
      
      private function get bossATK() : int
      {
         return 6 * MasterProxy.instance().masterVO.level;
      }
      
      private function get bossDEF() : int
      {
         return MasterProxy.instance().masterVO.level;
      }
   }
}

