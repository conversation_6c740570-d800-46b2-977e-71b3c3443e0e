package mogames.gameBuff.buff
{
   import com.mogames.sound.SoundManager;
   import mogames.gameBuff.base.TimeRoleBuff;
   import mogames.gameData.buff.ConstBuffVO;
   import mogames.gameEffect.EffectManager;
   
   public class WuWeiBuff extends TimeRoleBuff
   {
      
      private var _add:int;
      
      private var _hp:int;
      
      public function WuWeiBuff(param1:ConstBuffVO)
      {
         super(param1);
      }
      
      override protected function createData() : void
      {
         _owner.signal.add(this.listenHP);
      }
      
      private function listenHP(param1:Object) : void
      {
         var _loc2_:int = 0;
         if(param1.type != "ROLE_HURT")
         {
            return;
         }
         this._hp += param1.hurtValue;
         if(int(this._hp / _owner.roleVO.totalHP * 100) >= _buffVO.args.hpPer)
         {
            _loc2_ = _owner.roleVO.totalDEF * _buffVO.args.defPer * 0.01;
            _owner.roleVO.skillDEF += _loc2_;
            _owner.roleVO.updateDEF();
            this._add += _loc2_;
            this._hp = 0;
            EffectManager.addHeadWord("防御提升！",_owner.x,_owner.y + _owner.height);
            SoundManager.instance().playAudio("AUDIO_BUFF");
         }
      }
      
      override protected function onCleanRole() : void
      {
         _owner.signal.remove(this.listenHP);
         _owner.roleVO.skillDEF -= this._add;
         _owner.roleVO.updateDEF();
      }
   }
}

