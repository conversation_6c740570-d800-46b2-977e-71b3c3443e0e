package com.mogames.system
{
   public class ActiveObject
   {
      
      protected var _active:Boolean;
      
      public function ActiveObject()
      {
         super();
         this._active = true;
      }
      
      public function get isActive() : Boolean
      {
         return this._active;
      }
      
      protected function clean() : void
      {
      }
      
      public function destroy() : void
      {
         if(!this._active)
         {
            return;
         }
         this._active = false;
         this.clean();
      }
   }
}

