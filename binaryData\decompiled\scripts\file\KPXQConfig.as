package file
{
   import mogames.gameData.base.func.KPXQVO;
   import mogames.gameData.base.vo.NeedVO;
   
   public class KPXQConfig
   {
      
      private static var _instance:KPXQConfig;
      
      private var _list:Vector.<KPXQVO>;
      
      public var hideCards:Array = [18601,18611,18621,18631,18602,18612,18622,18632,18603,18613,18623,18633,18604,18614,18624,18634,18605,18615,18625,18635];
      
      public function KPXQConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : KPXQConfig
      {
         if(!_instance)
         {
            _instance = new KPXQConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = new Vector.<KPXQVO>();
         this._list[this._list.length] = new KPXQVO(0,[new NeedVO(10000,5000),new NeedVO(10006,10)],[new NeedVO(10980,1)]);
         this._list[this._list.length] = new KPXQVO(1,[new NeedVO(10000,10000),new NeedVO(10006,20)],[new NeedVO(10980,2)]);
         this._list[this._list.length] = new KPXQVO(2,[new NeedVO(10000,20000),new NeedVO(10006,30)],[new NeedVO(10980,3)]);
      }
      
      public function findVO(param1:int) : KPXQVO
      {
         var _loc2_:KPXQVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.index == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

