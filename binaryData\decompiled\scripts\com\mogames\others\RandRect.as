package com.mogames.others
{
   import flash.geom.Point;
   
   public class RandRect
   {
      
      private var _left:Point;
      
      private var _width:int;
      
      private var _height:int;
      
      public function RandRect(param1:Point, param2:int, param3:int)
      {
         super();
         this._left = param1;
         this._width = param2;
         this._height = param3;
      }
      
      public function get randPoint() : Point
      {
         return new Point(int(this._left.x + Math.random() * this._width),int(this._left.y + Math.random() * this._height));
      }
   }
}

