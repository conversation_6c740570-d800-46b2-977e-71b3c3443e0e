package mogames.gameData.achieve.base
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   
   public class AchieveLockVO extends BaseAchieveVO
   {
      
      protected var _open:Oint = new Oint();
      
      public function AchieveLockVO(param1:int, param2:Array, param3:String, param4:String)
      {
         super(param1,param2,param3,param4);
         MathUtil.saveINT(this._open,0);
      }
      
      override public function checkOpen(param1:int = 1) : void
      {
         if(this.isOpen)
         {
            return;
         }
         MathUtil.saveINT(this._open,param1);
         dispatchTip();
      }
      
      override public function get isOpen() : Boolean
      {
         return Boolean(MathUtil.loadINT(this._open));
      }
      
      override public function get saveData() : String
      {
         return [id,MathUtil.loadINT(this._open),MathUtil.loadINT(_get),MathUtil.loadINT(_fail)].join("H");
      }
      
      override public function set loadData(param1:Array) : void
      {
         MathUtil.saveINT(this._open,int(param1[1]));
         MathUtil.saveINT(_get,int(param1[2]));
         MathUtil.saveINT(_fail,int(param1[2]));
      }
   }
}

