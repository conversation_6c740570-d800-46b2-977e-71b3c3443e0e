package com.mogames.utils
{
   import com.mogames.data.Onum;
   
   public class ObjUtil
   {
      
      public function ObjUtil()
      {
         super();
      }
      
      public static function saveObj(param1:Object) : Object
      {
         var _loc3_:String = null;
         var _loc4_:Onum = null;
         if(!param1)
         {
            return null;
         }
         var _loc2_:Object = new Object();
         for(_loc3_ in param1)
         {
            _loc4_ = new Onum();
            MathUtil.saveNUM(_loc4_,param1[_loc3_]);
            _loc2_[_loc3_] = _loc4_;
         }
         return _loc2_;
      }
      
      public static function loadObj(param1:Object) : Object
      {
         var _loc3_:String = null;
         if(!param1)
         {
            return null;
         }
         var _loc2_:Object = new Object();
         for(_loc3_ in param1)
         {
            _loc2_[_loc3_] = MathUtil.loadNUM(param1[_loc3_]);
         }
         return _loc2_;
      }
   }
}

