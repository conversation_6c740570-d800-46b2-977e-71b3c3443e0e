package file
{
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.base.vo.EquipRewardVO;
   import mogames.gameData.good.base.ConstFashionVO;
   import mogames.gameData.mall.vo.MallMoneyVO;
   
   public class MallConfig
   {
      
      private static var _instance:MallConfig;
      
      private var _news:Array;
      
      private var _goods:Array;
      
      private var _bags:Array;
      
      private var _skins:Array;
      
      public function MallConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : MallConfig
      {
         if(!_instance)
         {
            _instance = new MallConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._news = [];
         // 免费商城：所有新品价格改为1
         this._news[this._news.length] = new MallMoneyVO(3778,1,new BaseRewardVO(10317,1));
         this._news[this._news.length] = new MallMoneyVO(3778,1,new BaseRewardVO(10315,1));
         this._news[this._news.length] = new MallMoneyVO(3952,1,new BaseRewardVO(10853,1));
         this._news[this._news.length] = new MallMoneyVO(3934,1,new BaseRewardVO(10987,1));
         this._news[this._news.length] = new MallMoneyVO(3900,1,new BaseRewardVO(10986,1));
         this._news[this._news.length] = new MallMoneyVO(3847,1,new BaseRewardVO(10985,1));
         this._news[this._news.length] = new MallMoneyVO(3626,1,new BaseRewardVO(50042,1));
         this._news[this._news.length] = new MallMoneyVO(3778,1,new BaseRewardVO(10314,1));
         this._news[this._news.length] = new MallMoneyVO(3734,1,new BaseRewardVO(10924,1));
         this._news[this._news.length] = new MallMoneyVO(3778,1,new BaseRewardVO(10313,1));
         this._news[this._news.length] = new MallMoneyVO(3778,1,new BaseRewardVO(10310,1));
         this._news[this._news.length] = new MallMoneyVO(3733,1,new BaseRewardVO(10308,1));
         this._news[this._news.length] = new MallMoneyVO(3798,1,new BaseRewardVO(50037,1));
         this._news[this._news.length] = new MallMoneyVO(3785,1,new BaseRewardVO(10984,1));
         this._news[this._news.length] = new MallMoneyVO(3778,1,new BaseRewardVO(10313,1));
         this._news[this._news.length] = new MallMoneyVO(3734,1,new BaseRewardVO(10923,1));
         this._news[this._news.length] = new MallMoneyVO(3734,1,new BaseRewardVO(10922,1));
         this._news[this._news.length] = new MallMoneyVO(3734,1,new BaseRewardVO(10921,1));
         this._news[this._news.length] = new MallMoneyVO(3783,1,new BaseRewardVO(10309,1));
         this._news[this._news.length] = new MallMoneyVO(3740,1,new BaseRewardVO(11601,1));
         this._news[this._news.length] = new MallMoneyVO(3738,1,new BaseRewardVO(10983,1));
         this._news[this._news.length] = new MallMoneyVO(3643,1,new BaseRewardVO(10981,1));
         this._news[this._news.length] = new MallMoneyVO(3661,1,new BaseRewardVO(10982,1));
         this._news[this._news.length] = new MallMoneyVO(3662,1,new BaseRewardVO(18840,1));
         this._news[this._news.length] = new MallMoneyVO(3625,1,new BaseRewardVO(18830,1));
         this._news[this._news.length] = new MallMoneyVO(3492,1,new BaseRewardVO(10851,1));
         this._news[this._news.length] = new MallMoneyVO(3614,1,new BaseRewardVO(10852,1));
         this._news[this._news.length] = new MallMoneyVO(3555,1,new BaseRewardVO(68001,1));
         this._news[this._news.length] = new MallMoneyVO(3556,1,new BaseRewardVO(68002,1));
         this._goods = [];
         // 免费商城：所有商品价格改为1
         this._goods[this._goods.length] = new MallMoneyVO(3392,1,new BaseRewardVO(10300,1));
         this._goods[this._goods.length] = new MallMoneyVO(3578,1,new BaseRewardVO(10303,1));
         this._goods[this._goods.length] = new MallMoneyVO(3577,1,new BaseRewardVO(11401,1));
         this._goods[this._goods.length] = new MallMoneyVO(3951,1,new BaseRewardVO(10280,1));
         this._goods[this._goods.length] = new MallMoneyVO(3825,1,new BaseRewardVO(10279,1));
         this._goods[this._goods.length] = new MallMoneyVO(3776,1,new BaseRewardVO(10278,1));
         this._goods[this._goods.length] = new MallMoneyVO(3725,1,new BaseRewardVO(10277,1));
         this._goods[this._goods.length] = new MallMoneyVO(3656,1,new BaseRewardVO(10276,1));
         this._goods[this._goods.length] = new MallMoneyVO(3600,1,new BaseRewardVO(10275,1));
         this._goods[this._goods.length] = new MallMoneyVO(3508,1,new BaseRewardVO(10274,1));
         this._goods[this._goods.length] = new MallMoneyVO(3493,1,new BaseRewardVO(10273,1));
         this._goods[this._goods.length] = new MallMoneyVO(3421,1,new BaseRewardVO(10272,1));
         this._goods[this._goods.length] = new MallMoneyVO(3420,1,new BaseRewardVO(10271,1));
         this._goods[this._goods.length] = new MallMoneyVO(3608,1,new BaseRewardVO(10306,1));
         this._goods[this._goods.length] = new MallMoneyVO(3575,1,new BaseRewardVO(10304,1));
         this._goods[this._goods.length] = new MallMoneyVO(3798,1,new BaseRewardVO(50036,1));
         this._goods[this._goods.length] = new MallMoneyVO(3739,1,new BaseRewardVO(50028,1));
         this._goods[this._goods.length] = new MallMoneyVO(3692,1,new BaseRewardVO(50022,1));
         this._goods[this._goods.length] = new MallMoneyVO(3798,1,new BaseRewardVO(50035,1));
         this._goods[this._goods.length] = new MallMoneyVO(3798,1,new BaseRewardVO(50032,1));
         this._goods[this._goods.length] = new MallMoneyVO(3691,1,new BaseRewardVO(50018,1));
         this._goods[this._goods.length] = new MallMoneyVO(3729,1,new BaseRewardVO(50025,1));
         this._goods[this._goods.length] = new MallMoneyVO(3646,1,new BaseRewardVO(50016,1));
         this._goods[this._goods.length] = new MallMoneyVO(3626,1,new BaseRewardVO(50015,1));
         this._goods[this._goods.length] = new MallMoneyVO(3558,1,new BaseRewardVO(50004,1));
         this._goods[this._goods.length] = new MallMoneyVO(3599,1,new BaseRewardVO(50011,1));
         this._goods[this._goods.length] = new MallMoneyVO(3557,1,new BaseRewardVO(50005,1));
         this._goods[this._goods.length] = new MallMoneyVO(3736,1,new BaseRewardVO(50026,1));
         this._goods[this._goods.length] = new MallMoneyVO(3585,1,new BaseRewardVO(50001,1));
         this._goods[this._goods.length] = new MallMoneyVO(3448,1,new BaseRewardVO(50003,1));
         this._goods[this._goods.length] = new MallMoneyVO(3447,1,new BaseRewardVO(50002,1));
         this._goods[this._goods.length] = new MallMoneyVO(3592,1,new BaseRewardVO(50010,1));
         this._goods[this._goods.length] = new MallMoneyVO(3780,1,new BaseRewardVO(50029,1));
         this._goods[this._goods.length] = new MallMoneyVO(3393,1,new BaseRewardVO(10020,1));
         this._goods[this._goods.length] = new MallMoneyVO(3512,1,new BaseRewardVO(10980,1));
         this._goods[this._goods.length] = new MallMoneyVO(3719,1,new BaseRewardVO(10533,1));
         this._goods[this._goods.length] = new MallMoneyVO(3719,1,new BaseRewardVO(10534,1));
         this._goods[this._goods.length] = new MallMoneyVO(3719,1,new BaseRewardVO(10535,1));
         this._goods[this._goods.length] = new MallMoneyVO(3706,1,new BaseRewardVO(10521,1));
         this._goods[this._goods.length] = new MallMoneyVO(3706,1,new BaseRewardVO(10522,1));
         this._goods[this._goods.length] = new MallMoneyVO(3706,1,new BaseRewardVO(10523,1));
         this._goods[this._goods.length] = new MallMoneyVO(3706,1,new BaseRewardVO(10524,1));
         this._goods[this._goods.length] = new MallMoneyVO(3706,1,new BaseRewardVO(10525,1));
         this._goods[this._goods.length] = new MallMoneyVO(3706,1,new BaseRewardVO(10526,1));
         this._goods[this._goods.length] = new MallMoneyVO(3394,1,new BaseRewardVO(10301,1));
         this._goods[this._goods.length] = new MallMoneyVO(3395,1,new BaseRewardVO(10302,1));
         this._goods[this._goods.length] = new MallMoneyVO(3396,1,new BaseRewardVO(10250,1));
         this._goods[this._goods.length] = new MallMoneyVO(3597,1,new BaseRewardVO(10907,1));
         this._goods[this._goods.length] = new MallMoneyVO(3576,1,new BaseRewardVO(10906,1));
         this._goods[this._goods.length] = new MallMoneyVO(3506,1,new BaseRewardVO(10905,1));
         this._goods[this._goods.length] = new MallMoneyVO(3491,1,new BaseRewardVO(10904,1));
         this._goods[this._goods.length] = new MallMoneyVO(3398,1,new BaseRewardVO(10903,1));
         this._goods[this._goods.length] = new MallMoneyVO(3399,1,new BaseRewardVO(10902,1));
         this._goods[this._goods.length] = new MallMoneyVO(3400,1,new BaseRewardVO(10901,1));
         this._goods[this._goods.length] = new MallMoneyVO(3737,1,new BaseRewardVO(10958,1));
         this._goods[this._goods.length] = new MallMoneyVO(3690,1,new BaseRewardVO(10957,1));
         this._goods[this._goods.length] = new MallMoneyVO(3598,1,new BaseRewardVO(10956,1));
         this._goods[this._goods.length] = new MallMoneyVO(3507,1,new BaseRewardVO(10955,1));
         this._goods[this._goods.length] = new MallMoneyVO(3401,1,new BaseRewardVO(10954,1));
         this._goods[this._goods.length] = new MallMoneyVO(3402,1,new BaseRewardVO(10953,1));
         this._goods[this._goods.length] = new MallMoneyVO(3403,1,new BaseRewardVO(10952,1));
         this._goods[this._goods.length] = new MallMoneyVO(3404,1,new BaseRewardVO(10951,1));
         this._goods[this._goods.length] = new MallMoneyVO(3405,1,new BaseRewardVO(10103,1));
         this._goods[this._goods.length] = new MallMoneyVO(3406,1,new BaseRewardVO(10102,1));
         this._goods[this._goods.length] = new MallMoneyVO(3407,1,new BaseRewardVO(10101,1));
         this._bags = [];
         // 免费商城：所有背包物品价格改为1
         this._bags[this._bags.length] = new MallMoneyVO(3382,1,new BaseRewardVO(11071,1));
         this._bags[this._bags.length] = new MallMoneyVO(3383,1,new BaseRewardVO(11072,1));
         this._bags[this._bags.length] = new MallMoneyVO(3384,1,new BaseRewardVO(11073,1));
         this._bags[this._bags.length] = new MallMoneyVO(3720,1,new BaseRewardVO(11012,1));
         this._bags[this._bags.length] = new MallMoneyVO(3721,1,new BaseRewardVO(11158,1));
         this._bags[this._bags.length] = new MallMoneyVO(3722,1,new BaseRewardVO(11205,1));
         this._bags[this._bags.length] = new MallMoneyVO(3499,1,new BaseRewardVO(11003,1));
         this._bags[this._bags.length] = new MallMoneyVO(3498,1,new BaseRewardVO(11302,1));
         this._bags[this._bags.length] = new MallMoneyVO(3723,1,new BaseRewardVO(11501,1));
         this._bags[this._bags.length] = new MallMoneyVO(3381,1,new BaseRewardVO(11011,1));
         this._bags[this._bags.length] = new MallMoneyVO(3386,1,new BaseRewardVO(11151,1));
         this._bags[this._bags.length] = new MallMoneyVO(3388,1,new BaseRewardVO(11157,1));
         this._bags[this._bags.length] = new MallMoneyVO(3390,1,new BaseRewardVO(11204,1));
         this._bags[this._bags.length] = new MallMoneyVO(3389,1,new BaseRewardVO(11201,1));
         this._bags[this._bags.length] = new MallMoneyVO(3387,1,new BaseRewardVO(11154,1));
         this._bags[this._bags.length] = new MallMoneyVO(3391,1,new BaseRewardVO(11301,1));
         this._bags[this._bags.length] = new MallMoneyVO(3379,1,new BaseRewardVO(11005,1));
         this._bags[this._bags.length] = new MallMoneyVO(3378,1,new BaseRewardVO(11002,1));
         this._bags[this._bags.length] = new MallMoneyVO(3380,1,new BaseRewardVO(11008,1));
         this._bags[this._bags.length] = new MallMoneyVO(3385,1,new BaseRewardVO(11104,1));
         this._skins = [];
         // 免费商城：所有皮肤价格改为1
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42541,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42531,3));
         this._skins[this._skins.length] = new MallMoneyVO(3584,1,new EquipRewardVO(42101,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42471,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42351,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42361,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42381,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42371,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42241,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42251,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42511,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42461,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42451,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42431,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42411,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42441,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42261,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42211,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42221,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42421,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42401,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42231,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42311,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42321,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42391,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42271,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42291,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42201,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42331,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42341,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42301,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42131,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42141,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42171,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42191,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42181,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42151,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42281,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42161,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(41801,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42001,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42111,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(42121,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(41201,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(41701,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(40201,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(40401,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(40501,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(41901,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(41101,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(41301,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(40101,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(40601,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(40701,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(40301,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(40901,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(41001,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(40801,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(41401,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(41501,3));
         this._skins[this._skins.length] = new MallMoneyVO(3658,1,new EquipRewardVO(41601,3));
      }
      
      public function findMall(param1:int) : Array
      {
         switch(param1)
         {
            case 0:
               return this._news;
            case 1:
               return this._goods;
            case 2:
               return this._bags;
            case 3:
               return this._skins;
            default:
               return [];
         }
      }
      
      public function findMallVO(param1:int) : MallMoneyVO
      {
         var _loc3_:MallMoneyVO = null;
         var _loc2_:Array = this._news.concat(this._goods,this._bags,this._skins);
         for each(_loc3_ in _loc2_)
         {
            if(_loc3_.rewardVO.constGood.id == param1)
            {
               return _loc3_;
            }
         }
         return null;
      }
      
      public function hasFashion(param1:int) : Boolean
      {
         var _loc2_:ConstFashionVO = null;
         var _loc3_:MallMoneyVO = null;
         return true;
      }
   }
}

