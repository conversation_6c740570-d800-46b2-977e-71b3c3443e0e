package file
{
   import mogames.gameData.base.func.BrewVO;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.base.vo.NeedVO;
   
   public class BrewConfig
   {
      
      private static var _instance:BrewConfig;
      
      private var _list:Vector.<BrewVO>;
      
      public function BrewConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : BrewConfig
      {
         if(!_instance)
         {
            _instance = new BrewConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = new Vector.<BrewVO>();
         this._list[this._list.length] = new BrewVO(new NeedVO(10201,1),new BaseRewardVO(11011,1),3000,0,[new NeedVO(10027,1),new NeedVO(10028,1),new NeedVO(10029,1)]);
         this._list[this._list.length] = new BrewVO(new NeedVO(10202,1),new BaseRewardVO(11012,1),4000,0,[new NeedVO(11011,1),new NeedVO(10030,1),new NeedVO(10028,1)]);
         this._list[this._list.length] = new BrewVO(new NeedVO(10204,1),new BaseRewardVO(11013,1),8000,0,[new NeedVO(11012,1),new NeedVO(10030,1),new NeedVO(10034,1)]);
         this._list[this._list.length] = new BrewVO(new NeedVO(10203,1),new BaseRewardVO(11501,1),5000,0,[new NeedVO(10031,1),new NeedVO(10032,1),new NeedVO(10033,1)]);
         this._list[this._list.length] = new BrewVO(new NeedVO(10205,1),new BaseRewardVO(11502,1),6000,0,[new NeedVO(11501,2)]);
         this._list[this._list.length] = new BrewVO(new NeedVO(10206,1),new BaseRewardVO(11503,1),7000,0,[new NeedVO(11502,2)]);
      }
      
      public function findBrew(param1:int) : BrewVO
      {
         var _loc2_:BrewVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.source.needID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

