package file
{
   import flash.geom.Point;
   import mogames.gameData.mission.res.ConstBeastVO;
   import mogames.gameData.mission.res.ConstGatherVO;
   import mogames.gameData.mission.res.ConstResVO;
   import mogames.gameData.mission.res.ConstWoodVO;
   
   public class ResConfig
   {
      
      private static var _instance:ResConfig;
      
      private var _list:Array;
      
      public function ResConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : ResConfig
      {
         if(!_instance)
         {
            _instance = new ResConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = [];
         this._list[this._list.length] = new ConstWoodVO(101,1200,"PIC_TREE01",new Point(-45,-90));
         this._list[this._list.length] = new ConstWoodVO(102,800,"PIC_TREE02",new Point(-32,-77));
         this._list[this._list.length] = new ConstWoodVO(103,400,"PIC_TREE03",new Point(-39,-72));
         this._list[this._list.length] = new ConstWoodVO(104,300,"PIC_TREE04",new Point(-27,-52));
         this._list[this._list.length] = new ConstWoodVO(105,600,"PIC_TREE05",new Point(-37,-45));
         this._list[this._list.length] = new ConstWoodVO(106,500,"PIC_TREE06",new Point(-25,-30));
         this._list[this._list.length] = new ConstWoodVO(107,300,"PIC_TREE07",new Point(-28,-54));
         this._list[this._list.length] = new ConstWoodVO(108,600,"PIC_TREE08",new Point(-26,-80));
         this._list[this._list.length] = new ConstWoodVO(109,600,"PIC_TREE09",new Point(-30,-78));
         this._list[this._list.length] = new ConstWoodVO(110,600,"PIC_TREE10",new Point(-26,-74));
         this._list[this._list.length] = new ConstWoodVO(111,600,"PIC_TREE11",new Point(-30,-60));
         this._list[this._list.length] = new ConstGatherVO(201,300,"MC_FRUIT_CLIP1");
         this._list[this._list.length] = new ConstGatherVO(202,400,"MC_FRUIT_CLIP2");
         this._list[this._list.length] = new ConstGatherVO(203,500,"MC_FRUIT_CLIP3");
         this._list[this._list.length] = new ConstBeastVO(301,600,800,3,"BEAST_LU");
         this._list[this._list.length] = new ConstBeastVO(302,850,1100,3,"BEAST_NIU");
         this._list[this._list.length] = new ConstBeastVO(303,350,500,3,"BEAST_YANG");
      }
      
      public function findRes(param1:int) : ConstResVO
      {
         var _loc2_:ConstResVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.id == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

