package mogames.gameBuff.debuff
{
   import mogames.gameBuff.base.LoopRoleBuff;
   import mogames.gameData.buff.ConstBuffVO;
   import mogames.gameData.role.battle.HurtData;
   
   public class HurtDebuff extends LoopRoleBuff
   {
      
      private var _hurtData:HurtData;
      
      public function HurtDebuff(param1:ConstBuffVO)
      {
         super(param1);
         this._hurtData = new HurtData();
      }
      
      override protected function onLoop() : void
      {
         var _loc1_:int = 0;
         if(_owner.isDead)
         {
            return;
         }
         switch(_buffVO.args.isPer)
         {
            case 0:
               _loc1_ = int(_buffVO.args.value);
               break;
            case 1:
               _loc1_ = _owner.roleVO.totalHP * _buffVO.args.value * 0.01;
               break;
            case 2:
               _loc1_ = _owner.roleVO.curHP * _buffVO.args.value * 0.01;
         }
         this._hurtData.createHurt(_loc1_,true,false,false);
         _owner.setHurt(this._hurtData);
      }
   }
}

