package mogames.gameBuff.debuff
{
   import mogames.gameBuff.base.TimeRoleBuff;
   import mogames.gameData.buff.ConstBuffVO;
   
   public class MoveDebuff extends TimeRoleBuff
   {
      
      private var _value:int;
      
      public function MoveDebuff(param1:ConstBuffVO)
      {
         super(param1);
      }
      
      override protected function createData() : void
      {
         this._value = _owner.roleVO.totalSPD * _buffVO.args.value * 0.01;
         _owner.roleVO.skillSPD -= this._value;
         _owner.roleVO.updateSPD();
      }
      
      override protected function onCleanRole() : void
      {
         _owner.roleVO.skillSPD += this._value;
         _owner.roleVO.updateSPD();
      }
   }
}

