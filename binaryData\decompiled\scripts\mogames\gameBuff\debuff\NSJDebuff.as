package mogames.gameBuff.debuff
{
   import com.mogames.utils.TxtUtil;
   import mogames.gameBuff.base.TimeRoleBuff;
   import mogames.gameData.buff.ConstBuffVO;
   import mogames.gameEffect.EffectManager;
   
   public class NSJDebuff extends TimeRoleBuff
   {
      
      private var _value:int;
      
      public function NSJDebuff(param1:ConstBuffVO)
      {
         super(param1);
      }
      
      override protected function createData() : void
      {
         this._value = _owner.roleVO.totalATK * _buffVO.args.per * 0.01;
         _owner.roleVO.skillATK -= this._value;
         _owner.roleVO.updateATK();
         var _loc1_:* = "攻击-" + _buffVO.args.per + "%";
         EffectManager.addHeadWord(TxtUtil.setColor(_loc1_,"FFFF00"),_owner.x,_owner.y - _owner.height);
      }
      
      override protected function onCleanRole() : void
      {
         _owner.roleVO.skillATK += this._value;
         _owner.roleVO.updateATK();
      }
   }
}

