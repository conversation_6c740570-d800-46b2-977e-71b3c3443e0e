package file
{
   import com.mogames.data.RandomVO;
   import mogames.gameData.base.vo.NeedVO;
   import mogames.gameData.cell.CellConstVO;
   
   public class CellConfig
   {
      
      private static var _instance:CellConfig;
      
      private var _list:Array;
      
      private var _counts:Array;
      
      public function CellConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : CellConfig
      {
         if(!_instance)
         {
            _instance = new CellConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = [];
         this._list[this._list.length] = new CellConstVO(0,[[new NeedVO(10000,10000),new NeedVO(10003,50),new NeedVO(10004,50),new NeedVO(10005,50)],[new NeedVO(10000,20000),new NeedVO(10003,100),new NeedVO(10004,100),new NeedVO(10005,100)],[new NeedVO(10006,300),new NeedVO(10003,150),new NeedVO(10004,150),new NeedVO(10005,150)]]);
         this._list[this._list.length] = new CellConstVO(1,[[new NeedVO(10000,10000),new NeedVO(10003,50),new NeedVO(10004,50),new NeedVO(10005,50)],[new NeedVO(10000,20000),new NeedVO(10003,100),new NeedVO(10004,100),new NeedVO(10005,100)],[new NeedVO(10006,300),new NeedVO(10003,150),new NeedVO(10004,150),new NeedVO(10005,150)]]);
         this._list[this._list.length] = new CellConstVO(2,[[new NeedVO(10000,10000),new NeedVO(10003,50),new NeedVO(10004,50),new NeedVO(10005,50)],[new NeedVO(10000,20000),new NeedVO(10003,100),new NeedVO(10004,100),new NeedVO(10005,100)],[new NeedVO(10006,300),new NeedVO(10003,150),new NeedVO(10004,150),new NeedVO(10005,150)]]);
         this._list[this._list.length] = new CellConstVO(3,[[new NeedVO(10000,10000),new NeedVO(10003,50),new NeedVO(10004,50),new NeedVO(10005,50)],[new NeedVO(10000,20000),new NeedVO(10003,100),new NeedVO(10004,100),new NeedVO(10005,100)],[new NeedVO(10006,300),new NeedVO(10003,150),new NeedVO(10004,150),new NeedVO(10005,150)]]);
         this._list[this._list.length] = new CellConstVO(4,[[new NeedVO(10000,10000),new NeedVO(10003,50),new NeedVO(10004,50),new NeedVO(10005,50)],[new NeedVO(10000,20000),new NeedVO(10003,100),new NeedVO(10004,100),new NeedVO(10005,100)],[new NeedVO(10006,300),new NeedVO(10003,150),new NeedVO(10004,150),new NeedVO(10005,150)]]);
         this._list[this._list.length] = new CellConstVO(5,[[new NeedVO(10000,10000),new NeedVO(10003,50),new NeedVO(10004,50),new NeedVO(10005,50)],[new NeedVO(10000,20000),new NeedVO(10003,100),new NeedVO(10004,100),new NeedVO(10005,100)],[new NeedVO(10006,300),new NeedVO(10003,150),new NeedVO(10004,150),new NeedVO(10005,150)]]);
         this._list[this._list.length] = new CellConstVO(6,[[new NeedVO(10000,10000),new NeedVO(10003,50),new NeedVO(10004,50),new NeedVO(10005,50)],[new NeedVO(10000,20000),new NeedVO(10003,100),new NeedVO(10004,100),new NeedVO(10005,100)],[new NeedVO(10006,300),new NeedVO(10003,150),new NeedVO(10004,150),new NeedVO(10005,150)]]);
         this._counts = [];
         this._counts[0] = new RandomVO(3,7);
         this._counts[1] = new RandomVO(9,21);
         this._counts[2] = new RandomVO(18,46);
         this._counts[3] = new RandomVO(56,98);
      }
      
      public function findConst(param1:int) : CellConstVO
      {
         var _loc2_:CellConstVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.index == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function randomCount(param1:int) : int
      {
         return this._counts[param1].randomValue;
      }
   }
}

