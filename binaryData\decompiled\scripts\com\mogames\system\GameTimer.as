package com.mogames.system
{
   import mogames.Layers;
   
   public class GameTimer
   {
      
      private var _count:int;
      
      private var _interval:int;
      
      private var _total:int;
      
      private var _loop:<PERSON>olean;
      
      private var _running:Boolean;
      
      private var _processFunc:Function;
      
      private var _completeFunc:Function;
      
      public function GameTimer()
      {
         super();
      }
      
      public function setLoop(param1:Number, param2:Function = null, param3:<PERSON><PERSON>an = true, param4:Boolean = false) : void
      {
         this._loop = true;
         this._interval = Math.max(1,param1 * SysRender.FPS);
         this._processFunc = param2;
         this._count = 1;
         if(param3)
         {
            this.start();
         }
         if(param4 && this._processFunc != null)
         {
            this._processFunc();
         }
      }
      
      public function setInterval(param1:Number, param2:Number = 0, param3:Function = null, param4:Function = null, param5:Boolean = true, param6:Boolean = false) : void
      {
         this._loop = false;
         this._interval = Math.max(1,int(param1 * SysRender.FPS));
         this._total = param2 * SysRender.FPS;
         this._processFunc = param3;
         this._completeFunc = param4;
         this._count = 1;
         if(param5)
         {
            this.start();
         }
         if(param6 && this._processFunc != null)
         {
            this._processFunc();
         }
      }
      
      public function setTimeOut(param1:Number, param2:Function = null, param3:Boolean = true) : void
      {
         this._loop = false;
         this._interval = Math.max(1,int(param1 * SysRender.FPS));
         this._total = param1 * SysRender.FPS;
         this._processFunc = null;
         this._completeFunc = param2;
         this._count = 1;
         if(param3)
         {
            this.start();
         }
      }
      
      public function stop() : void
      {
         if(!this._running)
         {
            return;
         }
         this._running = false;
         Layers.render.remove(this.startCount);
      }
      
      public function start() : void
      {
         if(this._running)
         {
            return;
         }
         this._running = true;
         Layers.render.add(this.startCount);
      }
      
      public function reset(param1:Boolean = true) : void
      {
         this._count = 1;
         if(param1)
         {
            this.stop();
         }
         else
         {
            this.start();
         }
      }
      
      private function startCount() : void
      {
         if(!this._loop && this._count >= this._total)
         {
            this.stop();
            if(this._completeFunc != null)
            {
               this._completeFunc();
            }
         }
         else if(this._count % this._interval == 0)
         {
            if(this._processFunc != null)
            {
               this._processFunc();
            }
         }
         ++this._count;
      }
      
      public function get running() : Boolean
      {
         return this._running;
      }
      
      public function get leftTime() : Number
      {
         return Math.ceil((this._total - this._count) / SysRender.FPS);
      }
      
      public function get timerPer() : int
      {
         return this._count / this._total * 100;
      }
      
      public function get intervalTime() : Number
      {
         return this._interval / SysRender.FPS;
      }
      
      public function destroy() : void
      {
         this.stop();
         this._processFunc = null;
         this._completeFunc = null;
      }
   }
}

