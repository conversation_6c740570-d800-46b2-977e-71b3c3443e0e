package mogames.gameData.build
{
   import file.BuildConfig;
   import mogames.gameData.build.vo.GameBuildVO;
   
   public class BuildProxy
   {
      
      private static var _instance:BuildProxy;
      
      private var _list:Array;
      
      public function BuildProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.startNew();
      }
      
      public static function instance() : BuildProxy
      {
         if(!_instance)
         {
            _instance = new BuildProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this._list = BuildConfig.instance().newBuildList();
      }
      
      public function set loadData(param1:Array) : void
      {
         var _loc2_:Array = null;
         var _loc3_:GameBuildVO = null;
         var _loc4_:String = null;
         this.startNew();
         if(!param1)
         {
            return;
         }
         for each(_loc4_ in param1)
         {
            _loc2_ = _loc4_.split("H");
            _loc3_ = this.findBuild(int(_loc2_[0]));
            if(_loc3_)
            {
               _loc3_.loadData = _loc2_;
            }
         }
      }
      
      public function get saveData() : Array
      {
         var _loc2_:GameBuildVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._list)
         {
            _loc1_[_loc1_.length] = _loc2_.saveData;
         }
         return _loc1_;
      }
      
      public function findBuild(param1:int) : GameBuildVO
      {
         var _loc2_:GameBuildVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.constVO.buildID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function get list() : Array
      {
         return this._list;
      }
   }
}

