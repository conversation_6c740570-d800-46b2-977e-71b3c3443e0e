package com.mogames.display
{
   import flash.events.Event;
   import flash.utils.Dictionary;
   
   public class EventSprite extends BaseSprite
   {
      
      private var _dic:Dictionary;
      
      public function EventSprite()
      {
         super();
         this._dic = new Dictionary();
      }
      
      override public function addEventListener(param1:String, param2:Function, param3:Boolean = false, param4:int = 0, param5:Boolean = false) : void
      {
         super.addEventListener(param1,param2,param3,param4,param5);
         if(param1 == Event.REMOVED_FROM_STAGE)
         {
            return;
         }
         if(this._dic[param1] == null)
         {
            this._dic[param1] = [];
         }
         var _loc6_:int = 0;
         while(_loc6_ < this._dic[param1].length)
         {
            if(this._dic[param1][_loc6_] == param2)
            {
               return;
            }
            _loc6_++;
         }
         this._dic[param1].push(param2);
      }
      
      public function removeListeners() : void
      {
         var _loc1_:String = null;
         var _loc2_:int = 0;
         for(_loc1_ in this._dic)
         {
            _loc2_ = 0;
            while(_loc2_ < this._dic[_loc1_].length)
            {
               this.removeEventListener(_loc1_,this._dic[_loc1_][_loc2_]);
               _loc2_++;
            }
         }
      }
      
      override public function destroy() : void
      {
         this.removeListeners();
         super.destroy();
      }
   }
}

