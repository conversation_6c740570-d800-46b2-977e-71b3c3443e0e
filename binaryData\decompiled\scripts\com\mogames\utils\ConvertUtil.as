package com.mogames.utils
{
   import com.mogames.data.ImageInfo;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.geom.Matrix;
   import flash.geom.Rectangle;
   
   public class ConvertUtil
   {
      
      public function ConvertUtil()
      {
         super();
      }
      
      public static function convertMC(param1:MovieClip) : Vector.<ImageInfo>
      {
         var _loc3_:int = 0;
         var _loc2_:int = param1.totalFrames;
         var _loc4_:Vector.<ImageInfo> = new Vector.<ImageInfo>();
         var _loc5_:int = 0;
         while(_loc5_ < _loc2_)
         {
            _loc4_[_loc5_] = shapeToBitmap(param1,_loc5_ + 1);
            _loc5_++;
         }
         return _loc4_;
      }
      
      public static function shapeToBitmap(param1:MovieClip, param2:int) : ImageInfo
      {
         var _loc3_:ImageInfo = new ImageInfo();
         param1.gotoAndStop(param2);
         var _loc4_:Rectangle = param1.getBounds(param1);
         _loc3_.pivotX = int(_loc4_.x);
         _loc3_.pivotY = int(_loc4_.y);
         var _loc5_:BitmapData = new BitmapData(_loc4_.width,_loc4_.height,true,0);
         _loc5_.draw(param1,new Matrix(1,0,0,1,-_loc3_.pivotX,-_loc3_.pivotY));
         _loc3_.bitmapData = _loc5_;
         return _loc3_;
      }
      
      private static function findFirstMulit(param1:int, param2:MovieClip) : int
      {
         var _loc3_:Array = collectChild(param1,param2);
         var _loc4_:Array = [];
         var _loc5_:int = 1;
         while(_loc5_ < param1)
         {
            _loc4_ = collectChild(_loc5_,param2);
            if(isEqualFrame(_loc3_,_loc4_))
            {
               return _loc5_;
            }
            _loc5_++;
         }
         return 0;
      }
      
      private static function collectChild(param1:int, param2:MovieClip) : Array
      {
         if(param2.currentFrame != param1)
         {
            param2.gotoAndStop(param1);
         }
         var _loc3_:int = 0;
         var _loc4_:int = param2.numChildren;
         var _loc5_:Array = [];
         while(_loc3_ < _loc4_)
         {
            _loc5_[_loc3_] = param2.getChildAt(_loc3_);
            _loc3_++;
         }
         return _loc5_;
      }
      
      public static function isEqualFrame(param1:Array, param2:Array) : Boolean
      {
         var _loc5_:DisplayObject = null;
         var _loc6_:DisplayObject = null;
         if(param1.length != param2.length)
         {
            return false;
         }
         var _loc3_:int = 0;
         var _loc4_:int = int(param1.length);
         while(_loc3_ < _loc4_)
         {
            _loc5_ = param1[_loc3_];
            _loc6_ = param2[_loc3_];
            if(_loc5_ != _loc6_)
            {
               return false;
            }
            if(_loc5_.x != _loc6_.x)
            {
               return false;
            }
            if(_loc5_.y != _loc6_.y)
            {
               return false;
            }
            if(_loc5_.scaleX != _loc6_.scaleX)
            {
               return false;
            }
            if(_loc5_.scaleY != _loc6_.scaleY)
            {
               return false;
            }
            if(_loc5_.rotation != _loc6_.rotation)
            {
               return false;
            }
            if(_loc5_.alpha != _loc6_.alpha)
            {
               return false;
            }
            _loc3_++;
         }
         return true;
      }
   }
}

