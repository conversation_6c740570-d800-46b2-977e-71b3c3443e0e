package mogames.gameData.achieve.base
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   
   public class NumAchieveVO1 extends BaseAchieveVO
   {
      
      protected var _curNum:Oint = new Oint();
      
      protected var _maxNum:Oint = new Oint();
      
      public function NumAchieveVO1(param1:int, param2:int, param3:Array, param4:String, param5:String)
      {
         super(param1,param3,param4,param5);
         MathUtil.saveINT(this._maxNum,param2);
         MathUtil.saveINT(this._curNum,0);
      }
      
      override public function reset() : void
      {
         MathUtil.saveINT(this._curNum,0);
      }
      
      override public function checkOpen(param1:int = 0) : void
      {
         if(this.isOpen)
         {
            return;
         }
         MathUtil.saveINT(this._curNum,this.curNum + param1);
         super.checkOpen();
      }
      
      override public function get isOpen() : Boolean
      {
         return this.curNum >= this.maxNum;
      }
      
      override public function get infor() : String
      {
         if(this.isOpen)
         {
            return _infor;
         }
         return _infor + "（" + this.curNum + "/" + this.maxNum + "）";
      }
      
      private function get curNum() : int
      {
         return MathUtil.loadINT(this._curNum);
      }
      
      private function get maxNum() : int
      {
         return MathUtil.loadINT(this._maxNum);
      }
      
      override public function get saveData() : String
      {
         return [id,this.curNum,MathUtil.loadINT(_get)].join("H");
      }
      
      override public function set loadData(param1:Array) : void
      {
         MathUtil.saveINT(this._curNum,int(param1[1]));
         MathUtil.saveINT(_get,int(param1[2]));
      }
   }
}

