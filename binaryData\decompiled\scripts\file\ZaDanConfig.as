package file
{
   import com.mogames.utils.MathUtil;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.base.vo.EquipRewardVO;
   import mogames.gameData.flag.base.FlagVO;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameData.zadan.ZaDanProxy;
   
   public class ZaDanConfig
   {
      
      private static var _instance:ZaDanConfig;
      
      private var _listA:Array;
      
      private var _listB:Array;
      
      private var _listC:Array;
      
      public function ZaDanConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : ZaDanConfig
      {
         if(!_instance)
         {
            _instance = new ZaDanConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._listA = [new BaseRewardVO(10302,1),new BaseRewardVO(10303,1),new BaseRewardVO(10851,1),new BaseRewardVO(10852,1),new BaseRewardVO(10852,1),new BaseRewardVO(10550,1),new BaseRewardVO(10250,2),new BaseRewardVO(10250,3),new BaseRewardVO(11003,5),new BaseRewardVO(11005,5),new BaseRewardVO(11012,5),new BaseRewardVO(10563,1),new BaseRewardVO(10853,1),new BaseRewardVO(11073,2),new BaseRewardVO(11104,3),new BaseRewardVO(11151,2),new BaseRewardVO(11157,5),new BaseRewardVO(11204,5),new BaseRewardVO(11501,5),new BaseRewardVO(10984,1),new BaseRewardVO(11401,1),new BaseRewardVO(68001,10),new BaseRewardVO(68002,10),new BaseRewardVO(10006,333),new BaseRewardVO(10806,1),new BaseRewardVO(10103,1),new BaseRewardVO(10281,1),new BaseRewardVO(10282,1),new BaseRewardVO(10283,1),new BaseRewardVO(10284,1),new EquipRewardVO(42201,3),new EquipRewardVO(42211,3),new EquipRewardVO(42221,3),new EquipRewardVO(42231,3),new EquipRewardVO(42241,3)];
         this._listB = [new EquipRewardVO(30002,3),new EquipRewardVO(30004,3),new EquipRewardVO(30010,3),new EquipRewardVO(30006,3),new EquipRewardVO(30011,3),new EquipRewardVO(42564,3)];
         this._listC = [new BaseRewardVO(10285,3),new BaseRewardVO(10286,3),new BaseRewardVO(10287,3),new BaseRewardVO(10288,3),new BaseRewardVO(10289,3),new BaseRewardVO(10290,3),new BaseRewardVO(10303,5),new BaseRewardVO(10303,10),new BaseRewardVO(10984,2),new BaseRewardVO(10985,1),new BaseRewardVO(10986,1),new BaseRewardVO(10987,1),new BaseRewardVO(10573,2),new BaseRewardVO(10573,2),new BaseRewardVO(10981,1),new BaseRewardVO(10982,1),new BaseRewardVO(11401,4),new BaseRewardVO(18531,1),new BaseRewardVO(18532,1),new BaseRewardVO(18531,1),new BaseRewardVO(18532,1),new BaseRewardVO(10573,3),new BaseRewardVO(10302,8),new BaseRewardVO(10853,3),new BaseRewardVO(10853,2),new BaseRewardVO(10854,2),new BaseRewardVO(10854,3)];
      }
      
      public function get randomFree() : BaseRewardVO
      {
         return this._listA[int(Math.random() * this._listA.length)];
      }
      
      public function get randomMoney() : BaseRewardVO
      {
         var _loc1_:Array = this.countListB();
         var _loc2_:FlagVO = ZaDanProxy.instance().limitFlag.findFlag(100);
         if(_loc2_.isComplete && _loc1_.length > 0)
         {
            _loc2_.setValue(0);
            return this.randomB(_loc1_);
         }
         _loc2_.changeValue(1);
         if(MathUtil.checkOdds(20) && _loc1_.length > 0)
         {
            return this.randomB(_loc1_);
         }
         return this._listC[int(Math.random() * this._listC.length)];
      }
      
      private function randomB(param1:Array) : BaseRewardVO
      {
         var _loc2_:Array = ZaDanProxy.instance().records;
         var _loc3_:BaseRewardVO = param1[int(Math.random() * param1.length)];
         var _loc4_:int = int(this._listB.indexOf(_loc3_));
         if(_loc4_ != -1 && _loc2_.indexOf(_loc4_) == -1)
         {
            _loc2_[_loc2_.length] = _loc4_;
         }
         return _loc3_;
      }
      
      private function countListB() : Array
      {
         var _loc5_:EquipRewardVO = null;
         var _loc1_:Array = ZaDanProxy.instance().records;
         if(_loc1_.length >= this._listB.length)
         {
            return [];
         }
         var _loc2_:Array = [];
         var _loc3_:int = 0;
         var _loc4_:int = int(this._listB.length);
         while(_loc3_ < _loc4_)
         {
            if(_loc1_.indexOf(_loc3_) == -1)
            {
               _loc5_ = this._listB[_loc3_];
               if(!(_loc5_.isZhiBao && MasterProxy.instance().hasOneEquip(_loc5_.constGood.id)))
               {
                  _loc2_[_loc2_.length] = _loc5_;
               }
            }
            _loc3_++;
         }
         return _loc2_;
      }
   }
}

