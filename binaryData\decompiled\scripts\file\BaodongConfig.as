package file
{
   import mogames.gameData.base.func.RandWaveVO;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.master.MasterProxy;
   import mogames.gameData.mission.base.MissionBattleVO;
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.base.RoleAttVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class BaodongConfig
   {
      
      private static var _instance:BaodongConfig;
      
      private var _waves:Array;
      
      private var _armyID:int;
      
      private var _bossID:int;
      
      private var _rewards:Array;
      
      public function BaodongConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : BaodongConfig
      {
         if(!_instance)
         {
            _instance = new BaodongConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._armyID = 221;
         this._bossID = 214;
         this._waves = [];
         this._waves[this._waves.length] = new RandWaveVO(5,[{
            "index":2,
            "num":1
         }],[30,20,22,22,20],[12,15,12,12,10]);
         this._waves[this._waves.length] = new RandWaveVO(5,[{
            "index":3,
            "num":1
         }],[20,25,25,22,20],[9,16,10,15,12]);
         this._waves[this._waves.length] = new RandWaveVO(5,[{
            "index":4,
            "num":1
         }],[25,25,20,22,20],[9,12,10,15,15]);
         this._rewards = [new BaseRewardVO(10000,5000),new BaseRewardVO(10304,1),new BaseRewardVO(11002,1),new BaseRewardVO(10007,10),new BaseRewardVO(10203,1),new BaseRewardVO(10031,1),new BaseRewardVO(10033,1)];
      }
      
      private function newSkills() : Array
      {
         var _loc1_:Array = [];
         _loc1_[_loc1_.length] = {
            "sid":1001,
            "arg":{
               "hurt":this.countHurt(48),
               "keepTime":3,
               "hurtCount":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1002,
            "arg":{
               "hurt":this.countHurt(48),
               "hurtCount":5
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1003,
            "arg":{
               "hurt":this.countHurt(46),
               "hurtCount":5
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1004,
            "arg":{
               "hurt":this.countHurt(64),
               "roleNum":4
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1005,
            "arg":{
               "hurt":this.countHurt(50),
               "roleNum":10
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1006,
            "arg":{"hurt":this.countHurt(96)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1007,
            "arg":{
               "hurt":this.countHurt(48),
               "keepTime":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1008,
            "arg":{"hurt":this.countHurt(42)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1009,
            "arg":{"hurt":this.countHurt(93)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1010,
            "arg":{"hurt":this.countHurt(33)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1011,
            "arg":{"hurt":this.countHurt(36)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1012,
            "arg":{"hurt":this.countHurt(124)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1013,
            "arg":{"hurt":this.countHurt(61)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1014,
            "arg":{
               "hurt":this.countHurt(46),
               "keepTime":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1015,
            "arg":{
               "hurt":this.countHurt(31),
               "keepTime":8,
               "hurtCount":5
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1016,
            "arg":{
               "hurt":this.countHurt(64),
               "roleNum":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1017,
            "arg":{
               "hurt":this.countHurt(46),
               "hurtCount":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1018,
            "arg":{"hurt":this.countHurt(40)}
         };
         _loc1_[_loc1_.length] = {
            "sid":1019,
            "arg":{
               "hurt":this.countHurt(40),
               "keepTime":3
            }
         };
         _loc1_[_loc1_.length] = {
            "sid":1020,
            "arg":{"hurt":this.countHurt(39)}
         };
         return _loc1_;
      }
      
      private function countHurt(param1:int) : int
      {
         return param1 * int(MasterProxy.instance().masterVO.level * 0.55 + 1);
      }
      
      private function countReward() : Array
      {
         var _loc1_:int = 0;
         var _loc5_:int = 0;
         var _loc2_:int = Math.random() * 100 + 1;
         if(_loc2_ <= 50)
         {
            _loc1_ = 1;
         }
         else if(_loc2_ <= 75)
         {
            _loc1_ = 2;
         }
         else
         {
            _loc1_ = 3;
         }
         var _loc3_:Array = [];
         var _loc4_:Array = this._rewards.slice();
         var _loc6_:int = 0;
         while(_loc6_ < _loc1_)
         {
            _loc5_ = Math.random() * _loc4_.length;
            _loc3_[_loc6_] = _loc4_[_loc5_];
            _loc4_.splice(_loc5_,1);
            _loc6_++;
         }
         return _loc3_;
      }
      
      public function newBattleVO(param1:int) : MissionBattleVO
      {
         var _loc2_:MissionBattleVO = WaveExtraConfig.instance().findBattle(param1);
         var _loc3_:int = MasterProxy.instance().needExp * 0.03 + 88;
         var _loc4_:int = MasterProxy.instance().needExp * 0.02 + 188;
         return new MissionBattleVO(param1,_loc3_,_loc3_,this.countReward(),[],_loc2_.jsonURL);
      }
      
      public function newBaodongWave() : WaveDataVO
      {
         var _loc6_:OneWaveVO = null;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc1_:Array = this.newSkills();
         var _loc2_:RandWaveVO = this._waves[int(Math.random() * this._waves.length)];
         var _loc3_:WaveDataVO = new WaveDataVO();
         _loc3_.limitBR = new WaveLimitVO(0,1,1);
         _loc3_.zhuBoss = this.newSZBoss(this._bossID,_loc1_[int(Math.random() * _loc1_.length)]);
         var _loc4_:int = 0;
         var _loc5_:int = _loc2_.total;
         while(_loc4_ < _loc5_)
         {
            _loc6_ = new OneWaveVO(_loc2_.findTime(_loc4_));
            _loc6_.addEnemy(new WaveEnemyVO(_loc2_.findEmemies(_loc4_),this.newEnemyArg));
            _loc7_ = _loc2_.findFu(_loc4_);
            _loc8_ = 0;
            while(_loc8_ < _loc7_)
            {
               _loc6_.addFu(this.newSZBoss(this._bossID,_loc1_[int(Math.random() * _loc1_.length)]));
               _loc8_++;
            }
            _loc3_.addWave(_loc6_);
            _loc4_++;
         }
         _loc1_.length = 0;
         _loc1_ = null;
         return _loc3_;
      }
      
      private function get newEnemyArg() : RoleArgVO
      {
         var _loc1_:RoleAttVO = EnemyConfig.instance().findATT(this._armyID);
         var _loc2_:int = MasterProxy.instance().masterVO.level;
         var _loc3_:int = _loc1_.baseHP + _loc1_.argHP * _loc2_;
         var _loc4_:int = _loc1_.baseATK + _loc1_.argATK * _loc2_;
         var _loc5_:int = _loc1_.baseDEF + _loc1_.argDEF * _loc2_;
         return new RoleArgVO(_loc1_.roleID,_loc3_,_loc4_,_loc5_,_loc1_.baseMISS,_loc1_.baseCRIT,_loc1_.baseBEI,_loc1_.baseSPD,null);
      }
      
      private function newSZBoss(param1:int, param2:Object) : BossArgVO
      {
         var _loc3_:RoleAttVO = EnemyConfig.instance().findATT(param1);
         return new BossArgVO(param1,this.szBossHP,this.szBossATK,this.szBossDEF,_loc3_.baseMISS,_loc3_.baseCRIT,_loc3_.baseBEI,_loc3_.baseSPD,new BossSkillData0(80,param2.arg),param2.sid,0);
      }
      
      private function get szBossHP() : int
      {
         return 600 * MasterProxy.instance().masterVO.level;
      }
      
      private function get szBossATK() : int
      {
         return 5 * MasterProxy.instance().masterVO.level;
      }
      
      private function get szBossDEF() : int
      {
         return MasterProxy.instance().masterVO.level;
      }
   }
}

