package file
{
   import flash.geom.Point;
   import mogames.gameData.base.func.QuanVO;
   
   public class QuanConfig
   {
      
      private static var _instance:QuanConfig;
      
      private var _scenes:Array;
      
      private var _missions:Array;
      
      public function QuanConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : QuanConfig
      {
         if(!_instance)
         {
            _instance = new QuanConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._scenes = [];
         this._scenes[this._scenes.length] = new QuanVO(105,31,new Point(928,400));
         this._scenes[this._scenes.length] = new QuanVO(106,32,new Point(604,122));
         this._scenes[this._scenes.length] = new QuanVO(107,33,new Point(87,490));
         this._scenes[this._scenes.length] = new QuanVO(107,34,new Point(701,83));
         this._scenes[this._scenes.length] = new QuanVO(103,35,new Point(761,484));
         this._scenes[this._scenes.length] = new QuanVO(111,41,new Point(67,398));
         this._missions = [];
         this._missions[this._missions.length] = new QuanVO(1004,36,new Point(312,424));
         this._missions[this._missions.length] = new QuanVO(1008,37,new Point(275,132));
         this._missions[this._missions.length] = new QuanVO(1027,38,new Point(803,160));
         this._missions[this._missions.length] = new QuanVO(1034,39,new Point(1394,180));
         this._missions[this._missions.length] = new QuanVO(1037,40,new Point(1842,382));
      }
      
      public function get totalNum() : int
      {
         return Math.max(10,this._scenes.length + this._missions.length);
      }
      
      public function get sceneQuans() : Array
      {
         return this._scenes;
      }
      
      public function get missionQuans() : Array
      {
         return this._missions;
      }
   }
}

