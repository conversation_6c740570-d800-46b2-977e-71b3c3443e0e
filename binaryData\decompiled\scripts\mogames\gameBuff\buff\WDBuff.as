package mogames.gameBuff.buff
{
   import mogames.gameBuff.base.TimeRoleBuff;
   import mogames.gameData.buff.ConstBuffVO;
   import mogames.gameEffect.EffectManager;
   
   public class WDBuff extends TimeRoleBuff
   {
      
      public function WDBuff(param1:ConstBuffVO)
      {
         super(param1);
      }
      
      override protected function createData() : void
      {
         _owner.isMDEF = true;
         _owner.isPDEF = true;
         EffectManager.addHeadWord("无敌！",_owner.center.x,_owner.center.y);
      }
      
      override protected function onCleanRole() : void
      {
         _owner.isMDEF = false;
         _owner.isPDEF = false;
      }
   }
}

