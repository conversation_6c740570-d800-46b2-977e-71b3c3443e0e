package file
{
   import mogames.ConstData;
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.fuben.vo.FubenVO;
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.RoleArgVO;
   
   public class ZQSConfig
   {
      
      private static var _instance:ZQSConfig;
      
      public var argVO:RoleArgVO;
      
      public var waveData:WaveDataVO;
      
      public var showTime:Number;
      
      public var yuanbings:Array;
      
      public function ZQSConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : ZQSConfig
      {
         if(!_instance)
         {
            _instance = new ZQSConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         var _loc1_:OneWaveVO = null;
         this.showTime = 30;
         this.argVO = new RoleArgVO(166,3500,500,200,30,10,250,130,null);
         this.yuanbings = [];
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(101,1800,500,0,30,25,150,100,{
            "atkPer":60,
            "keepTime":10
         }));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(102,1800,500,0,30,25,150,100,{
            "rate":200,
            "hurtBei":1.5,
            "atkPer":50,
            "keepTime":5
         }));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(103,1800,500,0,30,25,150,100,{
            "rate":200,
            "defPer":20,
            "keepTime":3
         }));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(104,1800,500,0,30,25,150,100,{
            "rate":200,
            "curePer":40
         }));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(105,1800,500,0,30,25,150,100,{
            "rate":150,
            "curePer":30
         }));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(106,1800,500,0,30,25,150,100,null));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(107,1800,500,0,30,25,150,100,{
            "rate":200,
            "hurtBei":1.5
         }));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(108,1800,500,0,30,25,150,100,{
            "rate":200,
            "hurtPer":2,
            "keepTime":1
         }));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(109,1800,500,0,30,25,150,100,{
            "rate":200,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         }));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(110,1800,500,0,30,25,150,100,{
            "rate":200,
            "hurtBei":1.8
         }));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(111,1800,500,0,30,25,150,100,{
            "rate":150,
            "keepTime":2
         }));
         this.yuanbings[this.yuanbings.length] = new WaveEnemyVO(1,new RoleArgVO(112,1800,500,0,30,25,150,100,{
            "rate":50,
            "hurtBei":1.8
         }));
         this.waveData = new WaveDataVO(0);
         this.waveData.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_ = new OneWaveVO(5);
         _loc1_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(271,1800,202,0,30,25,150,70,null)));
         _loc1_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(271,1800,205,0,30,25,150,70,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(271,1800,202,0,30,25,150,100,null)));
         _loc1_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(271,1800,205,0,30,25,150,70,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(271,1800,202,0,30,25,150,70,null)));
         _loc1_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(271,1800,205,0,30,25,150,70,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(271,1800,202,0,30,25,150,70,null)));
         _loc1_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(271,1800,205,0,30,25,150,70,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(271,1800,202,0,30,25,150,70,null)));
         _loc1_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(271,1800,205,0,30,25,150,70,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(11,new RoleArgVO(271,2100,202,0,30,25,150,70,null)));
         _loc1_.addEnemy(new WaveEnemyVO(11,new RoleArgVO(271,1800,205,0,30,25,150,70,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(10);
         _loc1_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(271,2500,202,0,30,25,150,70,null)));
         _loc1_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(271,2000,205,0,30,25,150,70,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(13,new RoleArgVO(271,2500,202,0,30,25,150,70,null)));
         _loc1_.addEnemy(new WaveEnemyVO(13,new RoleArgVO(271,2000,205,0,30,25,150,70,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(14,new RoleArgVO(271,2500,202,0,30,25,150,70,null)));
         _loc1_.addEnemy(new WaveEnemyVO(14,new RoleArgVO(271,3000,205,0,30,25,150,70,null)));
         this.waveData.addWave(_loc1_);
         _loc1_ = new OneWaveVO(15);
         _loc1_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(271,3000,202,0,30,25,150,70,null)));
         _loc1_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(271,2500,205,0,30,25,150,70,null)));
         this.waveData.addWave(_loc1_);
      }
      
      public function newReward(param1:Boolean) : Array
      {
         if(!param1)
         {
            return this.loseReward;
         }
         return this.winReward;
      }
      
      private function get winReward() : Array
      {
         var _loc1_:int = 0;
         var _loc6_:int = 0;
         var _loc2_:int = Math.random() * 100 + 1;
         if(_loc2_ <= 30)
         {
            _loc1_ = ConstData.INT1.v;
         }
         else if(_loc2_ <= 80)
         {
            _loc1_ = ConstData.INT2.v;
         }
         else
         {
            _loc1_ = ConstData.INT3.v;
         }
         var _loc3_:Array = [];
         var _loc4_:FubenVO = FubenConfig.instance().findFuben(408);
         var _loc5_:Array = _loc4_.drops.slice(1);
         var _loc7_:int = 0;
         while(_loc7_ < _loc1_)
         {
            _loc6_ = Math.random() * _loc5_.length;
            _loc3_[_loc3_.length] = _loc5_[_loc6_];
            _loc5_.splice(_loc6_,1);
            _loc7_++;
         }
         return _loc3_;
      }
      
      private function get loseReward() : Array
      {
         return [new BaseRewardVO(10000,35000)];
      }
   }
}

