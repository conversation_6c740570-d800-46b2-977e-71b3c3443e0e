package mogames.gameBuff.debuff
{
   import mogames.gameBuff.base.TimeRoleBuff;
   import mogames.gameData.buff.ConstBuffVO;
   
   public class CritDebuff extends TimeRoleBuff
   {
      
      private var _value:int;
      
      public function CritDebuff(param1:ConstBuffVO)
      {
         super(param1);
      }
      
      override protected function createData() : void
      {
         this._value = _buffVO.args.isPer ? int(_owner.roleVO.totalCRIT * _buffVO.args.value * 0.01) : int(_buffVO.args.value);
         _owner.roleVO.skillCRIT -= this._value;
         _owner.roleVO.updateCRIT();
      }
      
      override protected function onCleanRole() : void
      {
         _owner.roleVO.skillCRIT += this._value;
         _owner.roleVO.updateCRIT();
      }
   }
}

