package mogames.gameData.base.func
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   
   public class ForgeVO
   {
      
      private var _level:Oint = new Oint();
      
      private var _needs:Array;
      
      public function ForgeVO(param1:int, param2:Array)
      {
         super();
         MathUtil.saveINT(this._level,param1);
         this._needs = param2;
      }
      
      public function get level() : int
      {
         return MathUtil.loadINT(this._level);
      }
      
      public function get needList() : Array
      {
         return this._needs;
      }
   }
}

