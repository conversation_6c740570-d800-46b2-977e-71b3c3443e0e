package file
{
   import mogames.gameData.base.vo.BaseRewardVO;
   import mogames.gameData.boon.SevenDayVO;
   import mogames.gameData.flag.FlagProxy;
   
   public class SevenConfig
   {
      
      private static var _instance:SevenConfig;
      
      private var _sevens:Array;
      
      public function SevenConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : SevenConfig
      {
         if(!_instance)
         {
            _instance = new SevenConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._sevens = [];
         this._sevens[this._sevens.length] = new SevenDayVO(0,42,[new BaseRewardVO(18800,1),new BaseRewardVO(10901,10),new BaseRewardVO(10250,3),new BaseRewardVO(10271,5)]);
         this._sevens[this._sevens.length] = new SevenDayVO(1,43,[new BaseRewardVO(10316,1),new BaseRewardVO(10902,10),new BaseRewardVO(10250,5),new BaseRewardVO(10272,5)]);
         this._sevens[this._sevens.length] = new SevenDayVO(2,44,[new BaseRewardVO(50001,5),new BaseRewardVO(10903,10),new BaseRewardVO(10301,2),new BaseRewardVO(10273,5)]);
         this._sevens[this._sevens.length] = new SevenDayVO(3,45,[new BaseRewardVO(11104,10),new BaseRewardVO(10904,10),new BaseRewardVO(10301,5),new BaseRewardVO(10274,5)]);
         this._sevens[this._sevens.length] = new SevenDayVO(4,46,[new BaseRewardVO(50006,10),new BaseRewardVO(10905,10),new BaseRewardVO(11151,5),new BaseRewardVO(10301,5)]);
         this._sevens[this._sevens.length] = new SevenDayVO(5,47,[new BaseRewardVO(11073,5),new BaseRewardVO(10906,10),new BaseRewardVO(10274,2),new BaseRewardVO(10302,5)]);
         this._sevens[this._sevens.length] = new SevenDayVO(6,48,[new BaseRewardVO(50031,30),new BaseRewardVO(10020,1),new BaseRewardVO(10907,10),new BaseRewardVO(11072,3)]);
      }
      
      public function get todayVO() : SevenDayVO
      {
         var _loc2_:SevenDayVO = null;
         var _loc1_:int = this.loginIndex;
         for each(_loc2_ in this._sevens)
         {
            if(_loc2_.index == _loc1_)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function get loginIndex() : int
      {
         return Math.max(0,FlagProxy.instance().limitFlag.findFlag(301).cur - 1);
      }
      
      public function get sevens() : Array
      {
         return this._sevens;
      }
      
      public function get tipGet() : Boolean
      {
         var _loc1_:SevenDayVO = null;
         for each(_loc1_ in this._sevens)
         {
            if(_loc1_.isDay && !_loc1_.hasGet)
            {
               return true;
            }
         }
         return false;
      }
   }
}

