package mogames.gameData.good.base
{
   import com.mogames.data.Oint;
   import com.mogames.data.ValueVO;
   import com.mogames.utils.MathUtil;
   import file.RoleConfig;
   import mogames.gameData.role.base.SkinVO;
   
   public class ConstFashionVO extends ConstEquipVO
   {
      
      private var _skinID:Oint = new Oint();
      
      public function ConstFashionVO(param1:int, param2:int, param3:int, param4:int, param5:int, param6:int, param7:ValueVO, param8:ValueVO, param9:ValueVO, param10:ValueVO, param11:ValueVO, param12:ValueVO, param13:ValueVO, param14:ValueVO, param15:int, param16:String, param17:String, param18:String, param19:String)
      {
         super(param1,param3,7,-1,param4,param5,param6,param7,param8,param9,param10,param11,param12,param13,param14,-1,param15,param16,param17,param18,param19);
         MathUtil.saveINT(this._skinID,param2);
      }
      
      public function get skinID() : int
      {
         return MathUtil.loadINT(this._skinID);
      }
      
      public function get skinVO() : SkinVO
      {
         return RoleConfig.instance().findSkin(MathUtil.loadINT(this._skinID));
      }
   }
}

