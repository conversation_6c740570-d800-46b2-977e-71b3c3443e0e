package com.mogames.comt
{
   import com.mogames.utils.FontUtil;
   import com.mogames.utils.MethodUtil;
   import flash.events.MouseEvent;
   
   public class PageSwitcher
   {
      
      public var showTxt:Boolean = true;
      
      private var _curPage:int;
      
      private var _totalPage:int;
      
      private var _allData:Array;
      
      private var _pageData:Array;
      
      private var _pageNum:int;
      
      private var _func:Function;
      
      private var _skin:Object;
      
      public function PageSwitcher(param1:int, param2:Function, param3:Object)
      {
         super();
         this._pageNum = param1;
         this._func = param2;
         this._skin = param3;
      }
      
      private function init() : void
      {
         if(this.showTxt)
         {
            this._skin.visible = true;
         }
         this._skin.btnPrev.addEventListener(MouseEvent.CLICK,this.onPrev,false,0,true);
         this._skin.btnNext.addEventListener(MouseEvent.CLICK,this.onNext,false,0,true);
      }
      
      private function onNext(param1:MouseEvent) : void
      {
         ++this._curPage;
         this.updateBtn();
         this.updateData();
         this._func();
      }
      
      private function onPrev(param1:MouseEvent) : void
      {
         --this._curPage;
         this.updateBtn();
         this.updateData();
         this._func();
      }
      
      private function updateBtn() : void
      {
         if(this._totalPage == 1)
         {
            MethodUtil.enableBtn(this._skin.btnPrev,false);
            MethodUtil.enableBtn(this._skin.btnNext,false);
         }
         else
         {
            MethodUtil.enableBtn(this._skin.btnPrev,this._curPage > 1);
            MethodUtil.enableBtn(this._skin.btnNext,this._curPage < this._totalPage);
         }
         if(this._skin.txtPage != null)
         {
            FontUtil.setText(this._skin.txtPage,this._curPage + "/" + this._totalPage);
         }
      }
      
      private function updateData() : void
      {
         var _loc1_:int = (this._curPage - 1) * this._pageNum;
         var _loc2_:int = Math.min(this._curPage * this._pageNum,this._allData.length);
         this._pageData = this._allData.slice(_loc1_,_loc2_);
      }
      
      public function gotoPage(param1:int) : void
      {
         this._curPage = Math.min(param1,this._totalPage);
         this.updateBtn();
         this.updateData();
         this._func();
      }
      
      public function refreshPage(param1:int) : void
      {
         this._curPage = param1;
         this.refresh();
      }
      
      public function refresh() : void
      {
         this._totalPage = Math.max(1,Math.ceil(this._allData.length / this._pageNum));
         if(this._curPage > this._totalPage)
         {
            this._curPage = this._totalPage;
         }
         this.updateBtn();
         this.updateData();
         this._func();
      }
      
      public function del(param1:*, param2:Boolean = true) : void
      {
         if(!param1)
         {
            return;
         }
         var _loc3_:int = int(this._allData.indexOf(param1));
         if(_loc3_ != -1)
         {
            this._allData.splice(_loc3_,1);
         }
         if(param2)
         {
            this.refresh();
         }
      }
      
      public function add(param1:*, param2:Boolean = true) : void
      {
         if(!param1)
         {
            return;
         }
         var _loc3_:int = int(this._allData.indexOf(param1));
         if(_loc3_ == -1)
         {
            this._allData.push(param1);
         }
         if(param2)
         {
            this.refresh();
         }
      }
      
      public function get curData() : Array
      {
         return this._pageData;
      }
      
      public function get allData() : Array
      {
         return this._allData;
      }
      
      public function set data(param1:Array) : void
      {
         this._allData = param1;
         this._curPage = 1;
         this._totalPage = Math.max(1,Math.ceil(this._allData.length / this._pageNum));
         this.updateData();
         this.updateBtn();
         this._func();
         this.init();
         if(!this.showTxt)
         {
            this._skin.visible = this._allData.length > this._pageNum;
         }
      }
      
      public function get curPage() : int
      {
         return this._curPage;
      }
      
      public function get length() : int
      {
         if(!this._allData)
         {
            return 0;
         }
         return this._allData.length;
      }
      
      public function clean() : void
      {
         if(!this._allData)
         {
            return;
         }
         this._allData.length = 0;
         this._allData = null;
      }
      
      public function destroy() : void
      {
         this._skin.btnPrev.removeEventListener(MouseEvent.CLICK,this.onPrev);
         this._skin.btnNext.removeEventListener(MouseEvent.CLICK,this.onNext);
         this._skin = null;
      }
   }
}

