package file
{
   import mogames.gameData.bag.base.BagLockVO;
   import mogames.gameData.bag.vo.BagFreeVO0;
   import mogames.gameData.bag.vo.BagFreeVO1;
   import mogames.gameData.bag.vo.BagFreeVO2;
   import mogames.gameData.bag.vo.BagFreeVO3;
   import mogames.gameData.bag.vo.BagLeadVO;
   
   public class BagConfig
   {
      
      private static var _instance:BagConfig;
      
      private var _locks:Vector.<BagLockVO>;
      
      public function BagConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : BagConfig
      {
         if(!_instance)
         {
            _instance = new BagConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._locks = new Vector.<BagLockVO>();
         this._locks[this._locks.length] = new BagLockVO(11);
         this._locks[this._locks.length] = new BagLockVO(12);
         this._locks[this._locks.length] = new BagLockVO(13);
         this._locks[this._locks.length] = new BagFreeVO0(14,"通关豫州剧情所有关卡并战役关卡满星。");
         this._locks[this._locks.length] = new BagFreeVO1(15,"主公等级达到20级。");
         this._locks[this._locks.length] = new BagFreeVO2(16,"点击花费100金票解锁。");
         this._locks[this._locks.length] = new BagFreeVO3(17,"相应特权令牌解锁。");
         this._locks[this._locks.length] = new BagLockVO(21);
         this._locks[this._locks.length] = new BagLeadVO(22,"相应特权令牌解锁。");
      }
      
      public function findLock(param1:int) : BagLockVO
      {
         var _loc2_:BagLockVO = null;
         for each(_loc2_ in this._locks)
         {
            if(_loc2_.bagID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function isOpen(param1:int) : Boolean
      {
         var _loc2_:BagLockVO = null;
         for each(_loc2_ in param1)
         {
            if(_loc2_.bagID == param1)
            {
               return _loc2_.isOpen;
            }
         }
         return false;
      }
   }
}

