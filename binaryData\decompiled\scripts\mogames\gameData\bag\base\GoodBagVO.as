package mogames.gameData.bag.base
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import file.BagConfig;
   import file.GoodConfig;
   import mogames.gameData.good.bag.base.GameBagVO;
   import mogames.gameData.good.base.ConstBagVO;
   
   public class GoodBagVO
   {
      
      protected var _goodID:Oint = new Oint();
      
      protected var _bagID:Oint = new Oint();
      
      protected var _lockVO:BagLockVO;
      
      protected var _goodVO:GameBagVO;
      
      public function GoodBagVO(param1:int)
      {
         super();
         MathUtil.saveINT(this._bagID,param1);
         MathUtil.saveINT(this._goodID,0);
         this._lockVO = BagConfig.instance().findLock(param1);
      }
      
      public function setGoodID(param1:int) : void
      {
         MathUtil.saveINT(this._goodID,param1);
         if(param1)
         {
            this._goodVO = GoodConfig.instance().newGameGood(param1) as GameBagVO;
         }
         else
         {
            this._goodVO = null;
         }
      }
      
      public function get bagID() : int
      {
         return MathUtil.loadINT(this._bagID);
      }
      
      public function get isOpen() : Boolean
      {
         return this._lockVO.isOpen;
      }
      
      public function get lockVO() : BagLockVO
      {
         return this._lockVO;
      }
      
      public function get constBag() : ConstBagVO
      {
         if(this._goodVO)
         {
            return this._goodVO.constGood as ConstBagVO;
         }
         return null;
      }
      
      public function get goodID() : int
      {
         return MathUtil.loadINT(this._goodID);
      }
      
      public function get goodVO() : GameBagVO
      {
         return this._goodVO;
      }
   }
}

