package com.mogames.sound
{
   public class SoundConfig
   {
      
      private static var _instance:SoundConfig;
      
      private var _audios:Array;
      
      private var _musics:Array;
      
      public function SoundConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : SoundConfig
      {
         if(!_instance)
         {
            _instance = new SoundConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._audios = [];
         this._audios.push(new GameSoundVO("AUDIO_MAIN01","audio/main/AUDIO_MAIN01.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_MAIN02","audio/main/AUDIO_MAIN02.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_MAIN03","audio/main/AUDIO_MAIN03.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_MAIN04","audio/main/AUDIO_MAIN04.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_MAIN05","audio/main/AUDIO_MAIN05.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_MAIN06","audio/main/AUDIO_MAIN06.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_STAR","audio/system/AUDIO_STAR.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_TRANS","audio/system/AUDIO_TRANS.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_TYPE","audio/system/AUDIO_TYPE.mp3",false,5));
         this._audios.push(new GameSoundVO("AUDIO_ACHIEVE","audio/system/AUDIO_ACHIEVE.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_CLICK","audio/system/AUDIO_CLICK.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_GUIDE","audio/system/AUDIO_GUIDE.mp3",false,2));
         this._audios.push(new GameSoundVO("AUDIO_MISTAKE","audio/system/AUDIO_ERROR.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_KAO_DA","audio/system/AUDIO_KAO_DA.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_BO_HAO","audio/system/AUDIO_BO_HAO.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_DU","audio/system/AUDIO_DU.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_MANG_YIN","audio/system/AUDIO_MANG_YIN.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_TAB","audio/interface/AUDIO_TAB.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_ERROR","audio/interface/AUDIO_ERROR2.mp3",false,2));
         this._audios.push(new GameSoundVO("AUDIO_CLOCK","audio/interface/AUDIO_CLOCK.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_WIN_TWEEN","audio/interface/AUDIO_WIN_TWEEN.mp3",false,3));
         this._audios.push(new GameSoundVO("AUDIO_LOSE_TWEEN","audio/interface/AUDIO_LOSE_TWEEN.mp3",false,3));
         this._audios.push(new GameSoundVO("AUDIO_SHOW","audio/interface/AUDIO_SHOW.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_BOOK","audio/interface/AUDIO_BOOK.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_HUA","audio/interface/AUDIO_HUA.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_CANCEL","audio/interface/AUDIO_CANCEL.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_CLICK","audio/interface/AUDIO_CLICK.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_GET_OFF","audio/interface/AUDIO_GET_OFF.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_PUT_ON","audio/interface/AUDIO_PUT_ON.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_SELL","audio/interface/AUDIO_SELL.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_REWARD","audio/interface/AUDIO_REWARD.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_DRAG_DOWN","audio/interface/AUDIO_DRAG_DOWN.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_LEVEL_UP","audio/interface/AUDIO_LEVEL_UP.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_CONFIRM","audio/interface/AUDIO_CONFIRM.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_SKILL_NAME","audio/interface/AUDIO_SKILL_NAME.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_REFRESH","audio/interface/AUDIO_REFRESH.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_HIRE","audio/interface/AUDIO_HIRE.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_FAIL","audio/interface/AUDIO_FAIL.mp3",false,2));
         this._audios.push(new GameSoundVO("AUDIO_UNLOCK","audio/interface/AUDIO_UNLOCK.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_ADD_TRICK","audio/interface/AUDIO_ADD_TRICK.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_REMOVE_TRICK","audio/interface/AUDIO_REMOVE_TRICK.mp3",false,0.5));
         this._audios.push(new GameSoundVO("AUDIO_MODE","audio/interface/AUDIO_MODE.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_ZJD","audio/interface/AUDIO_ZJD.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_DUDU","audio/dialog/AUDIO_DUDU.mp3",false,0.3));
         this._audios.push(new GameSoundVO("AUDIO_YAHO","audio/dialog/AUDIO_YAHO.mp3",false,0.3));
         this._audios.push(new GameSoundVO("AUDIO_XIA","audio/dialog/AUDIO_XIA.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_SURPRISE","audio/dialog/AUDIO_SURPRISE.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_GATHER_WOOD","audio/gather/AUDIO_GATHER_WOOD.mp3",false,0.5));
         this._audios.push(new GameSoundVO("AUDIO_GATHER_FOOD","audio/gather/AUDIO_GATHER_FOOD1.mp3",false,2));
         this._audios.push(new GameSoundVO("AUDIO_GATHER_BEAST","audio/gather/AUDIO_GATHER_BEAST.mp3",false,0.5));
         this._audios.push(new GameSoundVO("AUDIO_ATTACK_BEAST","audio/gather/AUDIO_ATTACK_BEAST.mp3",false,0.5));
         this._audios.push(new GameSoundVO("AUDIO_DESTROY_WOOD","audio/gather/AUDIO_DESTROY_WOOD.mp3",false,0.5));
         this._audios.push(new GameSoundVO("AUDIO_KILL","audio/battle/AUDIO_KILL.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_START_TRAIN","audio/battle/AUDIO_START_TRAIN.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_PUT_FINISH","audio/battle/AUDIO_PUT_FINISH.mp3",false));
         this._audios.push(new GameSoundVO("TRAIN_HORSEMAN","audio/battle/TRAIN_HORSEMAN.mp3",false));
         this._audios.push(new GameSoundVO("TRAIN_FOOTMAN","audio/battle/TRAIN_FOOTMAN.mp3",false,2));
         this._audios.push(new GameSoundVO("AUDIO_LOSE","audio/battle/AUDIO_LOSE.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_DING","audio/battle/AUDIO_DING.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_WIN","audio/battle/AUDIO_WIN.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_CHEER","audio/battle/AUDIO_CHEER.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_BOSS_SHOW","audio/battle/AUDIO_BOSS_SHOW.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_BOSS_DEAD","audio/battle/AUDIO_BOSS_DEAD.mp3",false,2));
         this._audios.push(new GameSoundVO("AUDIO_ZIQIU","audio/battle/AUDIO_ZIQIU.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_GONG","audio/battle/AUDIO_GONG.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_FEIDAO","audio/battle/AUDIO_FEIDAO.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_NU","audio/battle/AUDIO_NU.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_SOLO_HURT","audio/battle/AUDIO_SOLO_HURT.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_SOLO_DEF","audio/battle/AUDIO_SOLO_DEF.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_TOUSHICHE","audio/battle/AUDIO_TOUSHICHE.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_BOOM0","audio/skill/AUDIO_BOOM0.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_BOOM1","audio/skill/AUDIO_BOOM1.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_BOOM2","audio/skill/AUDIO_BOOM2.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_BURN","audio/skill/AUDIO_BURN.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_DIAO","audio/skill/AUDIO_DIAO.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_DI_CI","audio/skill/AUDIO_DI_CI.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_FALL","audio/skill/AUDIO_FALL.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_FEN_SHEN","audio/skill/AUDIO_FEN_SHEN.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_FIRE_OUT","audio/skill/AUDIO_FIRE_OUT.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_FIRE_BOOM","audio/skill/AUDIO_FIRE_BOOM.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_HNQW","audio/skill/AUDIO_HNQW.mp3",false,4));
         this._audios.push(new GameSoundVO("AUDIO_LEI","audio/skill/AUDIO_LEI.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_NIU_HURT","audio/skill/AUDIO_NIU_HURT.mp3",false,3));
         this._audios.push(new GameSoundVO("AUDIO_NIU_JIAO","audio/skill/AUDIO_NIU_JIAO.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_RYLZ","audio/skill/AUDIO_RYLZ.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_SKILL_ARROW","audio/skill/AUDIO_SKILL_ARROW.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_STONE_OUT","audio/skill/AUDIO_STONE_OUT.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_SWORD_HURT","audio/skill/AUDIO_SWORD_HURT.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_WIND","audio/skill/AUDIO_WIND.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_BUFF","audio/skill/AUDIO_BUFF.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_LEI_HURT","audio/skill/AUDIO_LEI_HURT.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_LEI_BOOM","audio/skill/AUDIO_LEI_BOOM.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_TOU_ZHI","audio/skill/AUDIO_TOU_ZHI.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_NU_HOU","audio/skill/AUDIO_NU_HOU.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_HUI_CHUN","audio/skill/AUDIO_HUI_CHUN.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_CURE","audio/skill/AUDIO_CURE.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_BING_FU","audio/skill/AUDIO_BING_FU.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_CHAO_FENG","audio/skill/AUDIO_CHAO_FENG.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_SHUN_YI","audio/skill/AUDIO_SHUN_YI.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_SHI_QI","audio/skill/AUDIO_SHI_QI.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_ICE_BOOM","audio/skill/AUDIO_ICE_BOOM.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_TIAO_XIN","audio/skill/AUDIO_TIAO_XIN.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_CHUN_FENG","audio/skill/AUDIO_CHUN_FENG.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_YJCK","audio/skill/AUDIO_YJCK.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_TPXS","audio/skill/AUDIO_TPXS.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_MFYH","audio/skill/AUDIO_MFYH.mp3",false));
         this._audios.push(new GameSoundVO("AUDIO_HXZT","audio/skill/AUDIO_HXZT.mp3",false));
         this._audios.push(new GameSoundVO("ATK_DAO_BING","audio/role/ATK_DAO_BING.mp3",false,2));
         this._audios.push(new GameSoundVO("DIE_DAO_BING","audio/role/DIE_DAO_BING.mp3",false));
         this._audios.push(new GameSoundVO("ATK_LIAN_CHUI_BING","audio/role/ATK_LIAN_CHUI_BING.mp3",false));
         this._audios.push(new GameSoundVO("DIE_LIAN_CHUI_BING","audio/role/DIE_LIAN_CHUI_BING.mp3",false));
         this._audios.push(new GameSoundVO("ATK_NV_BING","audio/role/ATK_NV_BING.mp3",false));
         this._audios.push(new GameSoundVO("DIE_NV_BING","audio/role/DIE_NV_BING.mp3",false));
         this._audios.push(new GameSoundVO("ATK_WU_DOU_BING","audio/role/ATK_WU_DOU_BING.mp3",false));
         this._audios.push(new GameSoundVO("DIE_WU_DOU_BING","audio/role/DIE_WU_DOU_BING.mp3",false));
         this._audios.push(new GameSoundVO("ATK_MAN_BING","audio/role/ATK_MAN_BING.mp3",false));
         this._audios.push(new GameSoundVO("DIE_MAN_BING","audio/role/DIE_MAN_BING.mp3",false));
         this._audios.push(new GameSoundVO("ATK_QI_BING","audio/role/ATK_QI_BING.mp3",false));
         this._audios.push(new GameSoundVO("DIE_QI_BING","audio/role/DIE_QI_BING.mp3",false));
         this._audios.push(new GameSoundVO("ATK_QIANG_BING","audio/role/ATK_QIANG_BING.mp3",false));
         this._audios.push(new GameSoundVO("DIE_QIANG_BING","audio/role/DIE_QIANG_BING.mp3",false));
         this._audios.push(new GameSoundVO("ATK_FEI_DAO_BING","audio/role/ATK_FEI_DAO_BING.mp3",false));
         this._audios.push(new GameSoundVO("DIE_FEI_DAO_BING","audio/role/DIE_FEI_DAO_BING.mp3",false));
         this._audios.push(new GameSoundVO("ATK_TIE_ZHUI_BING","audio/role/ATK_TIE_ZHUI_BING.mp3",false));
         this._audios.push(new GameSoundVO("DIE_TIE_ZHUI_BING","audio/role/DIE_TIE_ZHUI_BING.mp3",false));
         this._audios.push(new GameSoundVO("ATK_GONG_JIAN_BING","audio/role/ATK_GONG_JIAN_BING.mp3",false));
         this._audios.push(new GameSoundVO("DIE_GONG_JIAN_BING","audio/role/DIE_GONG_JIAN_BING.mp3",false));
         this._audios.push(new GameSoundVO("ATK_CI_KE","audio/role/ATK_CI_KE.mp3",false));
         this._audios.push(new GameSoundVO("DIE_CI_KE","audio/role/DIE_CI_KE.mp3",false));
         this._audios.push(new GameSoundVO("ATK_NU_BING","audio/role/ATK_NU_BING.mp3",false));
         this._audios.push(new GameSoundVO("DIE_NU_BING","audio/role/DIE_NU_BING.mp3",false));
         this._audios.push(new GameSoundVO("DIE_JIAN_JIANG_QI","audio/role/DIE_JIAN_JIANG_QI.mp3",false));
         this._audios.push(new GameSoundVO("DIE_CHANG_QIANG_QI","audio/role/DIE_CHANG_QIANG_QI.mp3",false));
         this._audios.push(new GameSoundVO("DIE_DAO_JIANG_QI","audio/role/DIE_DAO_JIANG_QI.mp3",false));
         this._audios.push(new GameSoundVO("ATK_Shan_Jiang","audio/role/ATK_Shan_Jiang.mp3",false));
         this._audios.push(new GameSoundVO("DIE_MOU_SHI_QI","audio/role/DIE_MOU_SHI_QI.mp3",false));
         this._audios.push(new GameSoundVO("DIE_GONG_JIANG_QI","audio/role/DIE_GONG_JIANG_QI.mp3",false));
         this._audios.push(new GameSoundVO("DIE_NV_QI_JIANG","audio/role/DIE_NV_QI_JIANG.mp3",false));
         this._audios.push(new GameSoundVO("DIE_JIAN_JIANG","audio/role/DIE_JIAN_JIANG.mp3",false));
         this._audios.push(new GameSoundVO("DIE_QIANG_JIANG","audio/role/DIE_QIANG_JIANG.mp3",false));
         this._audios.push(new GameSoundVO("DIE_DAO_JIANG","audio/role/DIE_DAO_JIANG.mp3",false));
         this._audios.push(new GameSoundVO("DIE_MOU_JIANG","audio/role/DIE_MOU_JIANG.mp3",false));
         this._audios.push(new GameSoundVO("DIE_GONG_JIANG","audio/role/DIE_GONG_JIANG.mp3",false));
         this._audios.push(new GameSoundVO("DIE_NV_BING","audio/role/DIE_NV_BING.mp3",false));
         this._audios.push(new GameSoundVO("DIE_HUO_FENG_HUANG","audio/role/DIE_HUO_FENG_HUANG.mp3",false));
         this._audios.push(new GameSoundVO("ATK_WANG_DATOU","audio/role/ATK_WANG_DATOU.mp3",false));
         this._audios.push(new GameSoundVO("DIE_WANG_DATOU","audio/role/DIE_WANG_DATOU.mp3",false));
         this._audios.push(new GameSoundVO("DIE_JIANG_SHI","audio/role/DIE_JIANG_SHI.mp3",false));
         this._audios.push(new GameSoundVO("ATK_DANGONG_JS","audio/role/ATK_DANGONG_JS.mp3",false));
         this._audios.push(new GameSoundVO("ATK_DALI_JS","audio/role/ATK_DALI_JS.mp3",false));
         this._audios.push(new GameSoundVO("ATK_ZIBAO_JS","audio/role/ATK_ZIBAO_JS.mp3",false));
         this._audios.push(new GameSoundVO("DIE_TOU_SHI_CHE","audio/role/DIE_TOU_SHI_CHE.mp3",false));
         this._audios.push(new GameSoundVO("ATK_TOU_SHI_CHE","audio/role/ATK_TOU_SHI_CHE.mp3",false));
         this._musics = [];
         this._musics.push(new GameSoundVO("BGM_TITLE","music/BGM_TITLE220.mp3",false));
         this._musics.push(new GameSoundVO("BGM_WIN","music/BGM_WIN.mp3",false));
         this._musics.push(new GameSoundVO("BGM_FUNNY0","music/BGM_FUNNY0.mp3",false,0.5));
         this._musics.push(new GameSoundVO("BGM_JU_WEI_HUI","music/BGM_JU_WEI_HUI.mp3",false));
         this._musics.push(new GameSoundVO("BGM_MAP","music/BGM_MAP.mp3",false));
         this._musics.push(new GameSoundVO("BGM_AREA","music/BGM_AREA.mp3",false));
         this._musics.push(new GameSoundVO("BGM_CAPTURE","music/BGM_CAPTURE.mp3",false));
         this._musics.push(new GameSoundVO("BGM_SECRET","music/BGM_SECRET.mp3",false));
         this._musics.push(new GameSoundVO("BGM_FUBEN","music/BGM_FUBEN.mp3",false));
         this._musics.push(new GameSoundVO("BGM_MAIN0","music/BGM_MAIN0.mp3",false));
         this._musics.push(new GameSoundVO("BGM_MAIN1","music/BGM_MAIN1.mp3",false));
         this._musics.push(new GameSoundVO("BGM_MAIN2","music/BGM_MAIN2.mp3",false));
         this._musics.push(new GameSoundVO("BGM_MAIN3","music/BGM_MAIN3.mp3",false));
         this._musics.push(new GameSoundVO("BGM_MAIN4","music/BGM_MAIN4.mp3",false));
         this._musics.push(new GameSoundVO("BGM_MAIN5","music/BGM_MAIN5.mp3",false));
         this._musics.push(new GameSoundVO("BGM_TOWN3900","music/BGM_TOWN3900.mp3",false));
         this._musics.push(new GameSoundVO("BGM_TOWN3901","music/BGM_TOWN3901.mp3",false));
         this._musics.push(new GameSoundVO("BGM_TOWN3902","music/BGM_TOWN3902.mp3",false));
         this._musics.push(new GameSoundVO("BGM_MOU_LUE","music/BGM_MOU_LUE.mp3",false));
         this._musics.push(new GameSoundVO("BGM_WARM","music/BGM_WARM.mp3",false));
         this._musics.push(new GameSoundVO("BGM_PALACE","music/BGM_PALACE.mp3",false));
         this._musics.push(new GameSoundVO("BGM_PRISON","music/BGM_PRISON.mp3",false));
         this._musics.push(new GameSoundVO("BGM_TAVERN","music/BGM_TAVERN.mp3",false));
         this._musics.push(new GameSoundVO("BGM_MARKET","music/BGM_MARKET.mp3",false));
         this._musics.push(new GameSoundVO("BGM_GAMBLE","music/BGM_GAMBLE.mp3",false));
         this._musics.push(new GameSoundVO("BGM_SCENE_PK","music/BGM_SCENE_PK.mp3",false));
         this._musics.push(new GameSoundVO("BGM_GUIDE_VS","music/BGM_GUIDE_VS.mp3",false));
         this._musics.push(new GameSoundVO("BGM_VS0","music/BGM_VS0.mp3",false));
         this._musics.push(new GameSoundVO("BGM_VS1","music/BGM_VS1.mp3",false));
         this._musics.push(new GameSoundVO("BGM_VS2","music/BGM_VS2.mp3",false));
         this._musics.push(new GameSoundVO("BGM_VS3","music/BGM_VS3.mp3",false));
         this._musics.push(new GameSoundVO("BGM_VS_TOWN","music/BGM_VS_TOWN.mp3",false));
         this._musics.push(new GameSoundVO("BGM_VS_SECRET","music/BGM_VS_SECRET.mp3",false));
         this._musics.push(new GameSoundVO("BGM_JIE_BIAO","music/BGM_JIE_BIAO.mp3",false));
         this._musics.push(new GameSoundVO("BGM_MI_LIN","music/BGM_MI_LIN.mp3",false));
         this._musics.push(new GameSoundVO("BGM_SOLO","music/BGM_SOLO.mp3",false));
         this._musics.push(new GameSoundVO("BGM_HAI_GU","music/BGM_HAI_GU.mp3",false));
         this._musics.push(new GameSoundVO("BGM_BOSS0","music/BGM_BOSS0.mp3",false));
         this._musics.push(new GameSoundVO("BGM_BATTLE_PK","music/BGM_BATTLE_PK.mp3",false));
         this._musics.push(new GameSoundVO("BGM_UNION","music/BGM_UNION.mp3",false));
         this._musics.push(new GameSoundVO("BGM_UNION_FUBEN","music/BGM_UNION_FUBEN.mp3",false));
         this._musics.push(new GameSoundVO("BGM_ZJD","music/BGM_ZJD.mp3",false));
      }
      
      public function newAuidos() : Vector.<GameSound>
      {
         return this.newSounds(this._audios);
      }
      
      public function newMusics() : Vector.<GameSound>
      {
         return this.newSounds(this._musics);
      }
      
      private function newSounds(param1:Array) : Vector.<GameSound>
      {
         var _loc3_:GameSoundVO = null;
         var _loc2_:Vector.<GameSound> = new Vector.<GameSound>();
         for each(_loc3_ in param1)
         {
            _loc2_.push(new GameSound(_loc3_));
         }
         return _loc2_;
      }
   }
}

