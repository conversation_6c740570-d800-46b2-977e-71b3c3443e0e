package mogames.gameBuff.buff
{
   import mogames.gameBuff.base.TimeRoleBuff;
   import mogames.gameData.buff.ConstBuffVO;
   
   public class LoseHurtBuff extends TimeRoleBuff
   {
      
      public function LoseHurtBuff(param1:ConstBuffVO)
      {
         super(param1);
      }
      
      public function get losePer() : Number
      {
         return _buffVO.args.losePer * 0.01;
      }
   }
}

