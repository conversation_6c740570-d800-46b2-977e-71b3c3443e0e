package mogames.gameData.base.func
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.flag.FlagProxy;
   
   public class ZSTowerVO
   {
      
      private var _index:Oint = new Oint();
      
      private var _rewards:Array;
      
      public function ZSTowerVO(param1:int, param2:Array)
      {
         super();
         MathUtil.saveINT(this._index,param1);
         this._rewards = param2;
      }
      
      public function handlerEnter() : void
      {
         FlagProxy.instance().limitFlag.changeValue(355);
      }
      
      public function get rewards() : Array
      {
         return this._rewards;
      }
      
      public function get index() : int
      {
         return MathUtil.loadINT(this._index);
      }
   }
}

