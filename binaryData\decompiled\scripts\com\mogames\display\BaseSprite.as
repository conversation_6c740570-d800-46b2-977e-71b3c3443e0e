package com.mogames.display
{
   import com.mogames.utils.MethodUtil;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.geom.Point;
   
   public class BaseSprite extends Sprite implements IBase
   {
      
      protected var _location:Point;
      
      public function BaseSprite()
      {
         super();
         this._location = new Point();
      }
      
      public function setLocation(param1:int, param2:int) : void
      {
         x = param1;
         y = param2;
      }
      
      public function get location() : Point
      {
         this._location.x = x;
         this._location.y = y;
         return this._location;
      }
      
      public function destroy() : void
      {
         var _loc1_:DisplayObject = null;
         while(numChildren)
         {
            _loc1_ = getChildAt(0);
            if(_loc1_ is MovieClip)
            {
               (_loc1_ as MovieClip).stop();
            }
            if(_loc1_ is <PERSON><PERSON>roy)
            {
               (_loc1_ as IDestroy).destroy();
            }
            else
            {
               removeChildAt(0);
            }
         }
         MethodUtil.removeMe(this);
         this._location = null;
         _loc1_ = null;
      }
   }
}

